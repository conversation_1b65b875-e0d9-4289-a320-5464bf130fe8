{"_description": "Video Control Service Default Configuration", "_version": "1.0", "video_control": {"_comment": "Video Control Service for Mavlink communication, SD card management, photo capture and video recording", "udp_mavlink": {"_comment": "UDP socket configuration for receiving Mavlink messages", "bind_ip": "0.0.0.0", "port": 14551, "timeout_ms": 1000, "buffer_size": 1024}, "sdcard": {"_comment": "SD card configuration and monitoring", "mount_path": "/media/sdcard", "photo_save_path": "/media/sdcard/photos", "video_save_path": "/media/sdcard/videos", "status_report_interval_sec": 2, "min_free_space_mb": 100, "auto_cleanup_enabled": true, "max_storage_usage_percent": 90}, "photo_capture": {"_comment": "Photo capture configuration", "format": "JPEG", "quality": 60, "enable_timestamp_overlay": true, "enable_gps_metadata": false, "max_photo_size_mb": 10}, "dual_video_streams": {"_comment": "Dual video stream configuration for visible and infrared cameras", "segment_duration_min": 5, "visible_stream": {"_comment": "Visible light camera configuration - 1920x1080, 25fps, 4Mbps", "name": "visible", "dds_topic": "Video_Frames_Visible", "width": 1920, "height": 1080, "fps": 25, "bitrate": 4000000, "codec": "H265", "jpeg_quality": 60}, "infrared_stream": {"_comment": "Infrared camera configuration - 640x512, 30fps, 1Mbps", "name": "infrared", "dds_topic": "Video_Frames_Infrared", "width": 640, "height": 512, "fps": 30, "bitrate": 1000000, "codec": "H265", "jpeg_quality": 70}}, "dds": {"_comment": "DDS configuration for dual video frame input", "domain_id": 0, "max_samples": 5, "timeout_ms": 100}, "performance": {"_comment": "Performance and threading configuration", "thread_priority": 80, "cpu_affinity": [2, 3], "stats_interval_sec": 10, "enable_performance_monitoring": true, "max_frame_queue_size": 10}, "logging": {"_comment": "Logging configuration", "level": "INFO", "enable_debug": false, "enable_frame_stats": true, "enable_mavlink_debug": false, "log_file_path": "/var/log/video_control.log", "max_log_file_size_mb": 50, "log_rotation_count": 5}, "mavlink": {"_comment": "Mavlink message configuration (placeholder for future library integration)", "system_id": 1, "component_id": 1, "heartbeat_interval_sec": 1, "command_timeout_sec": 5, "max_retries": 3, "enable_heartbeat": true}, "safety": {"_comment": "Safety and error handling configuration", "max_recording_duration_hours": 24, "emergency_stop_on_low_space": true, "auto_restart_on_error": true, "max_restart_attempts": 3, "watchdog_timeout_sec": 30}}, "supported_mavlink_commands": {"_description": "Supported Mavlink command types", "MAV_CMD_TAKE_PHOTO": 1, "MAV_CMD_START_RECORDING": 2, "MAV_CMD_STOP_RECORDING": 3, "MAV_CMD_GET_STATUS": 4, "MAV_CMD_FORMAT_STORAGE": 5, "MAV_CMD_RESET_CAMERA": 6}, "video_formats": {"_description": "Supported video encoding formats", "H264": {"codec_name": "h264", "file_extension": "mp4", "hardware_encoder": "mpph264enc", "software_encoder": "x264enc"}, "H265": {"codec_name": "h265", "file_extension": "mp4", "hardware_encoder": "mpph265enc", "software_encoder": "x265enc"}}, "device_profiles": {"_description": "Predefined device configurations", "high_quality": {"video_recording": {"width": 1920, "height": 1080, "fps": 30, "bitrate": 5000000, "segment_duration_min": 5}, "photo_capture": {"quality": 98}}, "low_latency": {"video_recording": {"width": 1280, "height": 720, "fps": 60, "bitrate": 3000000, "segment_duration_min": 2, "preset": "ultrafast"}, "performance": {"max_frame_queue_size": 5}}, "storage_optimized": {"video_recording": {"width": 1280, "height": 720, "fps": 25, "bitrate": 1000000, "segment_duration_min": 10}, "sdcard": {"max_storage_usage_percent": 95, "auto_cleanup_enabled": true}}}}