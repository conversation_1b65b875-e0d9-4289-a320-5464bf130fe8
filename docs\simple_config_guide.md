# Video Service Configuration System

## Overview

This provides a consolidated configuration system that merges V4L2 device and video transport configuration parameters from the original `capture_config.h` and `video_config_params.h` files into a single, comprehensive configuration header.

## Key Components

### Core Structures

#### V4L2Params
```cpp
struct V4L2Params {
    std::string device_path = "/dev/video0";
    uint32_t width = 1920;
    uint32_t height = 1080;
    uint32_t pixel_format = V4L2_PIX_FMT_YUYV;
    uint32_t fps = 30;
    uint32_t buffer_count = 4;
    bool use_dmabuf = false;
    int timeout_ms = 1000;
};
```

#### TransportParams
```cpp
struct TransportParams {
    video_transport::TransportType type = TransportType::FASTDDS;
    std::string topic_name = "video_frames";
    int timeout_ms = 1000;
    int domain_id = 0;          // FastDDS
    int max_samples = 10;       // FastDDS
    size_t buffer_size = ...;   // DMA/SHMEM
    size_t ring_buffer_size = 8;// DMA/SHMEM
    std::string socket_path = "";// DMA/SHMEM
    std::string shm_name = "";  // SHMEM
};
```

#### SessionParams
```cpp
struct SessionParams {
    std::string name;
    V4L2Params v4l2;
    TransportParams transport;
    
    bool is_compatible() const;  // Basic compatibility check
    V4L2Capture::V4L2DeviceConfig to_v4l2_config(...) const;
    video_transport::TransportConfig to_transport_config() const;
};
```

### Preset Configurations

#### FastDDS (Copy-based)
```cpp
auto params = create_fastdds_session("my_session");
// - use_dmabuf = false (MMAP mode)
// - Transport: FastDDS with network distribution
```

#### DMA (Zero-copy)
```cpp
auto params = create_dma_session("my_session");
// - use_dmabuf = true (DMABUF mode)
// - buffer_count = 8 (more buffers)
// - Transport: DMA with zero-copy
```

#### SHMEM (Copy-based with shared memory)
```cpp
auto params = create_shmem_session("my_session");
// - use_dmabuf = false (MMAP mode - enforced!)
// - Transport: SHMEM with copy to shared memory
```

## Key Compatibility Rule

**SHMEM + DMABUF = ❌ INCOMPATIBLE**

The `is_compatible()` function prevents this:
```cpp
bool is_compatible() const {
    // SHMEM cannot use DMABUF
    if (transport.type == TransportType::SHMEM && v4l2.use_dmabuf) {
        return false;
    }
    return true;
}
```

## Usage Example

```cpp
#include "config/video_service_config.h"

// Use preset configuration
auto params = create_shmem_session("demo");

// Customize if needed
params.v4l2.width = 1280;
params.v4l2.height = 720;

// Check compatibility
if (!params.is_compatible()) {
    std::cerr << "Configuration incompatible!" << std::endl;
    return;
}

// Convert to existing interfaces
auto v4l2_config = params.to_v4l2_config(buffer_provider);
auto transport_config = params.to_transport_config();

// Use with existing factories
auto device = V4L2DeviceFactory::create_and_configure(v4l2_config);
auto publisher = VideoTransportFactory::create_publisher(transport_config);
```

## Benefits

1. **Simple**: Lightweight structures without excessive abstraction
2. **Compatible**: Direct conversion to existing interfaces
3. **Safe**: Prevents SHMEM + DMABUF incompatibility
4. **Convenient**: Preset configurations for common scenarios
5. **Flexible**: Easy customization of parameters

## Files

- `include/config/video_service_config.h` - Consolidated configuration structures
- `examples/simple_config_example.cpp` - Usage examples

This approach consolidates all video service configuration parameters into a single header file, providing both modern simplified structures and legacy compatibility for existing code.