# RGA Accelerator - 移除条件编译

## 概述

根据用户要求，已将所有的`#ifdef HAVE_RGA`条件编译移除，因为在CMakeLists.txt中已经将librga设置为必要库。

## 主要改动

### 1. 头文件简化

**修改前**:
```cpp
#ifdef HAVE_RGA
#include <im2d.hpp>
#include <RgaUtils.h>
#include <rga.h>
#else
// 当没有RGA支持时的空定义
typedef struct {
    int width;
    int height;
    int format;
    int size;
    void* vir_addr;
} rga_buffer_t;
// ... 更多fallback定义
#endif
```

**修改后**:
```cpp
#include <im2d.hpp>
#include <RgaUtils.h>
#include <rga.h>
```

### 2. 初始化方法简化

**修改前**:
```cpp
inline bool RGAAccelerator::init() {
#ifdef HAVE_RGA
    int ret = c_RkRgaInit();
    if (ret) {
        LOG_E("Failed to initialize RGA: %d", ret);
        return false;
    }
    rga_available_ = true;
    return true;
#else
    LOG_W("RGA support not compiled in");
    return false;
#endif
}
```

**修改后**:
```cpp
inline bool RGAAccelerator::init() {
    int ret = c_RkRgaInit();
    if (ret) {
        LOG_E("Failed to initialize RGA: %d", ret);
        return false;
    }
    rga_available_ = true;
    return true;
}
```

### 3. 核心操作方法简化

**修改前**:
```cpp
#ifdef HAVE_RGA
    int ret = imresize(src_buf_, dst_buf_);
    if (ret != IM_STATUS_SUCCESS) {
        LOG_E("RGA resize operation failed: %d", ret);
        return false;
    }
#else
    // 软件fallback
    LOG_W("RGA not available, using software fallback");
    memcpy(dst.data.data(), src.data.data(), ...);
#endif
```

**修改后**:
```cpp
int ret = imresize(src_buf_, dst_buf_);
if (ret != IM_STATUS_SUCCESS) {
    LOG_E("RGA resize operation failed: %d", ret);
    return false;
}
```

### 4. 格式转换方法简化

**修改前**:
```cpp
inline int RGAAccelerator::v4l2_to_rga_format(int32_t v4l2_format) {
#ifdef HAVE_RGA
    switch (v4l2_format) {
        case V4L2_PIX_FMT_YUYV:
            return RK_FORMAT_YUYV_422;
        // ... 其他格式
    }
#else
    return RK_FORMAT_RGB_888;
#endif
}
```

**修改后**:
```cpp
inline int RGAAccelerator::v4l2_to_rga_format(int32_t v4l2_format) {
    switch (v4l2_format) {
        case V4L2_PIX_FMT_YUYV:
            return RK_FORMAT_YUYV_422;
        // ... 其他格式
    }
}
```

### 5. 清理方法简化

**修改前**:
```cpp
inline void RGAAccelerator::cleanup() {
#ifdef HAVE_RGA
    if (rga_available_) {
        c_RkRgaDeInit();
        rga_available_ = false;
    }
#endif
    memset(&src_buf_, 0, sizeof(src_buf_));
    memset(&dst_buf_, 0, sizeof(dst_buf_));
}
```

**修改后**:
```cpp
inline void RGAAccelerator::cleanup() {
    if (rga_available_) {
        c_RkRgaDeInit();
        rga_available_ = false;
    }
    memset(&src_buf_, 0, sizeof(src_buf_));
    memset(&dst_buf_, 0, sizeof(dst_buf_));
}
```

## 测试文件更新

### test_video_converter.cpp

**修改前**:
```cpp
void test_rga_accelerator() {
#ifdef HAVE_RGA
    RGAAccelerator rga;
    // ... 测试代码
#else
    std::cout << "RGA support not compiled in" << std::endl;
#endif
}
```

**修改后**:
```cpp
void test_rga_accelerator() {
    RGAAccelerator rga;
    // ... 测试代码
}
```

## 优势

### 1. 代码简化
- 移除了大量的条件编译代码
- 代码结构更清晰
- 减少了维护复杂度

### 2. 编译简化
- 不再需要定义HAVE_RGA宏
- 编译配置更简单
- 减少了编译错误的可能性

### 3. 运行时行为一致
- 所有环境下行为一致
- 不会因为编译选项不同而产生不同的行为
- 更容易调试和测试

### 4. 依赖明确
- 明确依赖librga库
- 如果库不存在，编译时就会报错
- 避免了运行时的意外行为

## CMakeLists.txt要求

由于移除了条件编译，CMakeLists.txt必须确保librga库可用：

```cmake
# 查找RGA库（必需）
find_path(RGA_INCLUDE_DIR 
    NAMES im2d.hpp
    PATHS /usr/include/rga /usr/local/include/rga
    REQUIRED
)

find_library(RGA_LIBRARY 
    NAMES rga
    PATHS /usr/lib /usr/local/lib
    REQUIRED
)

# 添加包含目录和链接库
target_include_directories(${TARGET} PRIVATE ${RGA_INCLUDE_DIR})
target_link_libraries(${TARGET} ${RGA_LIBRARY})
```

## 错误处理

### 编译时错误
如果librga库不可用，编译时会出现错误：
```
fatal error: im2d.hpp: No such file or directory
```

这比运行时发现RGA不可用要好，因为：
1. 问题在编译时就被发现
2. 强制开发者解决依赖问题
3. 避免了部分功能不可用的情况

### 运行时错误
如果RGA设备不可用，`c_RkRgaInit()`会返回错误：
```cpp
int ret = c_RkRgaInit();
if (ret) {
    LOG_E("Failed to initialize RGA: %d", ret);
    return false;
}
```

这种情况下，RGAAccelerator的init()方法会返回false，调用者可以据此决定如何处理。

## 迁移指南

### 对于现有代码
1. 确保CMakeLists.txt正确配置了librga依赖
2. 移除所有HAVE_RGA相关的条件编译
3. 更新测试代码，移除条件编译
4. 确保在没有RGA硬件的环境中有适当的错误处理

### 对于新代码
1. 直接使用RGA API，无需条件编译
2. 在init()失败时提供适当的错误处理
3. 可以考虑提供软件fallback，但这应该在应用层处理

## 总结

移除`#ifdef HAVE_RGA`条件编译后：

1. **代码更简洁**: 减少了大量的条件编译代码
2. **依赖更明确**: 明确要求librga库存在
3. **行为更一致**: 所有环境下行为一致
4. **维护更容易**: 减少了代码分支和复杂性

这种改动符合"明确依赖，简化代码"的原则，使RGA Accelerator的实现更加清晰和可靠。
