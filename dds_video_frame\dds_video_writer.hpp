#pragma once

#include <fastdds/dds/domain/DomainParticipant.hpp>
#include <fastdds/dds/domain/DomainParticipantFactory.hpp>
#include <fastdds/dds/topic/TypeSupport.hpp>
#include <fastdds/dds/topic/Topic.hpp>
#include <fastdds/dds/publisher/Publisher.hpp>
#include <fastdds/dds/publisher/DataWriter.hpp>
#include <fastdds/dds/publisher/DataWriterListener.hpp>

#include "DDSVideoFramePubSubTypes.hpp"

namespace eprosima {
namespace fastdds {
namespace video {
// DDS Writer封装
class DDSWriter : public dds::DataWriterListener{
private:
    dds::DomainParticipant* participant_;
    dds::Publisher* publisher_;
    dds::Topic* topic_;
    dds::DataWriter* writer_;
    dds::TypeSupport type_;
    int max_samples_;
    
public:
    DDSWriter() :
        participant_(nullptr),
        publisher_(nullptr), 
        topic_(nullptr),
        writer_(nullptr){}

    ~DDSWriter() {
        cleanup();
    }

    bool init(const std::string& topic_name, int domain_id, int max_samples = 3);
    bool write(const DDSVideoFrame& dds_frame);
    void on_publication_matched(dds::DataWriter* , const dds::PublicationMatchedStatus& info) override;
    
private:
    void cleanup();
};

} // namespace video
} // namespace fastdds
} // namespace eprosima