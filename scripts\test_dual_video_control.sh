#!/bin/bash

# Dual Video Control Service Test Script
# This script tests the dual video control service with visible and infrared streams

set -e

echo "=== Dual Video Control Service Test Script ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}[OK]${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[WARN]${NC} $message"
    else
        echo -e "${RED}[ERROR]${NC} $message"
    fi
}

# Check if we're in the project root
if [ ! -f "CMakeLists.txt" ]; then
    print_status "ERROR" "Please run this script from the project root directory"
    exit 1
fi

print_status "OK" "Found project root directory"

# Test dual video configuration
echo -e "\n--- Testing Dual Video Configuration ---"

# Check configuration file
if [ -f "config/video_control.json" ]; then
    print_status "OK" "Found dual video configuration file"
    
    # Validate JSON syntax
    if python3 -m json.tool config/video_control.json > /dev/null 2>&1; then
        print_status "OK" "Configuration file JSON syntax is valid"
        
        # Check for dual stream configuration
        if grep -q "dual_video_streams" config/video_control.json; then
            print_status "OK" "Found dual video streams configuration"
        else
            print_status "ERROR" "Missing dual video streams configuration"
            exit 1
        fi
        
        if grep -q "visible_stream" config/video_control.json; then
            print_status "OK" "Found visible stream configuration"
        else
            print_status "ERROR" "Missing visible stream configuration"
            exit 1
        fi
        
        if grep -q "infrared_stream" config/video_control.json; then
            print_status "OK" "Found infrared stream configuration"
        else
            print_status "ERROR" "Missing infrared stream configuration"
            exit 1
        fi
    else
        print_status "ERROR" "Configuration file has invalid JSON syntax"
        exit 1
    fi
else
    print_status "ERROR" "Configuration file not found"
    exit 1
fi

# Build the project
echo -e "\n--- Building Dual Video Control Service ---"

if [ ! -d "build" ]; then
    mkdir build
    print_status "OK" "Created build directory"
fi

cd build

# Configure with CMake
if cmake .. -DCMAKE_BUILD_TYPE=Debug; then
    print_status "OK" "CMake configuration successful"
else
    print_status "ERROR" "CMake configuration failed"
    exit 1
fi

# Build the project
if make video_control -j$(nproc); then
    print_status "OK" "Dual Video Control Service built successfully"
else
    print_status "ERROR" "Build failed"
    exit 1
fi

# Test executable
if [ -f "video_control" ]; then
    print_status "OK" "video_control executable created"
    
    # Test help output
    echo -e "\n--- Testing Help Output ---"
    if ./video_control --help > /tmp/help_output.txt 2>&1; then
        print_status "OK" "Help command works"
        
        # Check for dual video features in help
        if grep -q "Dual Video Control Service Features" /tmp/help_output.txt; then
            print_status "OK" "Help shows dual video features"
        else
            print_status "WARN" "Help may not show dual video features"
        fi
        
        if grep -q "visible-topic" /tmp/help_output.txt; then
            print_status "OK" "Help shows visible stream topic option"
        else
            print_status "WARN" "Help missing visible stream topic option"
        fi
        
        if grep -q "infrared-topic" /tmp/help_output.txt; then
            print_status "OK" "Help shows infrared stream topic option"
        else
            print_status "WARN" "Help missing infrared stream topic option"
        fi
    else
        print_status "WARN" "Help command failed (may be due to missing dependencies)"
    fi
else
    print_status "ERROR" "video_control executable not found"
    exit 1
fi

# Test configuration parsing
echo -e "\n--- Testing Configuration Parsing ---"
cd ..

# Create test configuration with dual streams
cat > /tmp/test_dual_config.json << EOF
{
  "video_control": {
    "udp_mavlink": {
      "bind_ip": "0.0.0.0",
      "port": 14552
    },
    "sdcard": {
      "mount_path": "/tmp/test_sdcard",
      "photo_save_path": "/tmp/test_sdcard/photos",
      "video_save_path": "/tmp/test_sdcard/videos"
    },
    "dual_video_streams": {
      "segment_duration_min": 5,
      "visible_stream": {
        "name": "visible",
        "dds_topic": "Video_Frames_Visible",
        "width": 1920,
        "height": 1080,
        "fps": 25,
        "bitrate": 4000000,
        "codec": "H265",
        "jpeg_quality": 95
      },
      "infrared_stream": {
        "name": "infrared",
        "dds_topic": "Video_Frames_Infrared",
        "width": 640,
        "height": 512,
        "fps": 30,
        "bitrate": 1000000,
        "codec": "H265",
        "jpeg_quality": 90
      }
    },
    "dds": {
      "domain_id": 0,
      "max_samples": 5
    }
  }
}
EOF

if python3 -m json.tool /tmp/test_dual_config.json > /dev/null 2>&1; then
    print_status "OK" "Test dual video configuration is valid"
else
    print_status "ERROR" "Test dual video configuration is invalid"
    exit 1
fi

# Test command line arguments
echo -e "\n--- Testing Command Line Arguments ---"

# Test with dual video specific arguments
test_args=(
    "--config /tmp/test_dual_config.json"
    "--visible-topic Video_Frames_Visible_Test"
    "--infrared-topic Video_Frames_Infrared_Test"
    "--visible-bitrate 5000000"
    "--infrared-bitrate 1500000"
    "--segment-duration 3"
    "--help"
)

for arg in "${test_args[@]}"; do
    if ./build/video_control $arg > /dev/null 2>&1; then
        print_status "OK" "Argument '$arg' accepted"
    else
        if [[ "$arg" == "--help" ]]; then
            print_status "OK" "Help argument works (exit code expected)"
        else
            print_status "WARN" "Argument '$arg' may have issues"
        fi
    fi
done

# Create test directories
echo -e "\n--- Creating Test Directories ---"
test_dirs=(
    "/tmp/test_dual_video_control"
    "/tmp/test_dual_video_control/photos"
    "/tmp/test_dual_video_control/videos"
)

for dir in "${test_dirs[@]}"; do
    if mkdir -p "$dir" 2>/dev/null; then
        print_status "OK" "Created test directory: $dir"
    else
        print_status "WARN" "Failed to create test directory: $dir"
    fi
done

# Test UDP ports for dual streams
echo -e "\n--- Testing UDP Port Availability ---"
test_ports=(14551 14552)

for port in "${test_ports[@]}"; do
    if netstat -ln 2>/dev/null | grep -q ":$port "; then
        print_status "WARN" "UDP port $port is already in use"
    else
        print_status "OK" "UDP port $port is available"
    fi
done

# Summary
echo -e "\n=== Dual Video Test Summary ==="
print_status "OK" "Dual Video Control Service test completed"

echo -e "\nDual Video Features:"
echo "✓ Visible stream: 1920x1080@25fps, 4Mbps H265"
echo "✓ Infrared stream: 640x512@30fps, 1Mbps H265"
echo "✓ Dual photo capture with JPEG encoding"
echo "✓ Dual video recording with auto-segmentation"
echo "✓ Independent DDS topics for each stream"
echo "✓ Configurable bitrates and quality settings"

echo -e "\nNext steps:"
echo "1. Start FastDDS publishers for both topics:"
echo "   - Video_Frames_Visible (1920x1080@25fps)"
echo "   - Video_Frames_Infrared (640x512@30fps)"
echo "2. Configure SD card mount point"
echo "3. Start the service: ./build/video_control --config config/video_control.json"
echo "4. Test with Mavlink commands:"
echo "   - Send 0x01 for dual photo capture"
echo "   - Send 0x02 for dual video recording start"
echo "   - Send 0x03 for dual video recording stop"

echo -e "\nConfiguration files:"
echo "- Main config: config/video_control.json"
echo "- Test config: /tmp/test_dual_config.json"
echo "- Documentation: docs/video_control_service.md"

print_status "OK" "Dual video test script completed successfully"

# Cleanup
rm -f /tmp/help_output.txt /tmp/test_dual_config.json
