#include "include/transport/simplified_buffer_manager.h"
#include "include/transport/simplified_video_transport.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <sys/mman.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>

using namespace simplified_transport;
using namespace video_transport;

// Test function to demonstrate cross-process memory protection
void demonstrate_cross_process_protection() {
    std::cout << "=== Cross-Process Memory Protection Test ===" << std::endl;
    
    // Create a Unix socket pair to simulate cross-process communication
    int sv[2];
    if (socketpair(AF_UNIX, SOCK_STREAM, 0, sv) == -1) {
        std::cerr << "Failed to create socket pair" << std::endl;
        return;
    }
    
    // Fork to create two processes
    pid_t pid = fork();
    
    if (pid == 0) {
        // Child process (Consumer)
        close(sv[0]); // Close read end in child
        
        std::cout << "Child process (Consumer): Starting..." << std::endl;
        
        // Create buffer manager for consumer
        SimplifiedBufferManager buffer_manager(BufferType::SHMEM, 1024, 2);
        
        // Simulate receiving a buffer from producer
        // In real implementation, this would come via Unix socket
        auto* buffer = buffer_manager.acquire_buffer();
        if (buffer) {
            std::cout << "Child process: Acquired buffer " << buffer->buffer_id << std::endl;
            
            // Simulate receiving data from parent (would normally be via socket)
            // For demo, we'll just write some data
            char* data = static_cast<char*>(buffer->mapped_addr);
            for (size_t i = 0; i < std::min(buffer->size, size_t(100)); ++i) {
                data[i] = static_cast<char>('A' + (i % 26));
            }
            
            // Publish buffer as ready (read-only)
            buffer_manager.publish_buffer(buffer);
            std::cout << "Child process: Published buffer as read-only" << std::endl;
            
            // Try to write to the read-only buffer (should be protected)
            std::cout << "Child process: Attempting to write to read-only buffer..." << std::endl;
            try {
                data[0] = 'Z'; // This might cause a segfault if protection is working
                std::cout << "Child process: Write succeeded (protection may not be working)" << std::endl;
            } catch (...) {
                std::cout << "Child process: Write failed as expected (protection is working)" << std::endl;
            }
            
            // Release buffer
            buffer_manager.release_buffer(buffer);
            std::cout << "Child process: Released buffer" << std::endl;
        }
        
        close(sv[1]); // Close write end in child
        exit(0);
    } else if (pid > 0) {
        // Parent process (Producer)
        close(sv[1]); // Close write end in parent
        
        std::cout << "Parent process (Producer): Starting..." << std::endl;
        
        // Create buffer manager for producer
        SimplifiedBufferManager buffer_manager(BufferType::SHMEM, 1024, 2);
        
        // Acquire buffer for writing
        auto* buffer = buffer_manager.acquire_buffer();
        if (buffer) {
            std::cout << "Parent process: Acquired buffer " << buffer->buffer_id << std::endl;
            
            // Write data to buffer
            char* data = static_cast<char*>(buffer->mapped_addr);
            for (size_t i = 0; i < std::min(buffer->size, size_t(100)); ++i) {
                data[i] = static_cast<char>('a' + (i % 26));
            }
            std::cout << "Parent process: Wrote data to buffer" << std::endl;
            
            // Publish buffer (makes it read-only)
            buffer_manager.publish_buffer(buffer);
            std::cout << "Parent process: Published buffer as read-only" << std::endl;
            
            // Try to write to the published buffer (should be protected)
            std::cout << "Parent process: Attempting to write to published buffer..." << std::endl;
            try {
                data[0] = 'Z'; // This might cause a segfault if protection is working
                std::cout << "Parent process: Write succeeded (protection may not be working)" << std::endl;
            } catch (...) {
                std::cout << "Parent process: Write failed as expected (protection is working)" << std::endl;
            }
            
            // Release buffer
            buffer_manager.release_buffer(buffer);
            std::cout << "Parent process: Released buffer" << std::endl;
        }
        
        close(sv[0]); // Close read end in parent
        
        // Wait for child to finish
        int status;
        waitpid(pid, &status, 0);
        
        std::cout << "Parent process: Child finished with status " << status << std::endl;
    } else {
        std::cerr << "Failed to fork process" << std::endl;
        close(sv[0]);
        close(sv[1]);
    }
}

int main() {
    std::cout << "Cross-Process Memory Protection Demo" << std::endl;
    std::cout << "=====================================" << std::endl;
    
    try {
        demonstrate_cross_process_protection();
        std::cout << "\nTest completed successfully!" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}