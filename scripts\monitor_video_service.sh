#!/bin/bash

# Video Service Monitoring Script
# Monitors system resources and service health

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_DIR="/var/run/video_service"
LOG_DIR="/var/log/video_service"
MONITOR_LOG="${LOG_DIR}/monitor.log"

# Service names
SERVICES=("video_capture" "video_converter" "ai_processor" "cloud_streamer")

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$MONITOR_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$MONITOR_LOG" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$MONITOR_LOG"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$MONITOR_LOG"
}

# Check if service is running
is_service_running() {
    local service_name=$1
    local pid_file="${PID_DIR}/${service_name}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        fi
    fi
    return 1
}

# Get service PID
get_service_pid() {
    local service_name=$1
    local pid_file="${PID_DIR}/${service_name}.pid"
    
    if [[ -f "$pid_file" ]]; then
        cat "$pid_file"
    else
        echo "0"
    fi
}

# Get process statistics
get_process_stats() {
    local pid=$1
    
    if [[ $pid -eq 0 ]] || ! kill -0 "$pid" 2>/dev/null; then
        echo "0 0 0 0"
        return
    fi
    
    # Get CPU, memory, threads, file descriptors
    local cpu=$(ps -p "$pid" -o %cpu --no-headers 2>/dev/null | tr -d ' ' || echo "0")
    local mem=$(ps -p "$pid" -o %mem --no-headers 2>/dev/null | tr -d ' ' || echo "0")
    local threads=$(ps -p "$pid" -o nlwp --no-headers 2>/dev/null | tr -d ' ' || echo "0")
    local fds=$(ls /proc/$pid/fd 2>/dev/null | wc -l || echo "0")
    
    echo "$cpu $mem $threads $fds"
}

# Get system statistics
get_system_stats() {
    # CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    
    # Memory usage
    local mem_info=$(free | grep Mem)
    local mem_total=$(echo $mem_info | awk '{print $2}')
    local mem_used=$(echo $mem_info | awk '{print $3}')
    local mem_percent=$(echo "scale=1; $mem_used * 100 / $mem_total" | bc)
    
    # Disk usage for log directory
    local disk_usage=$(df "$LOG_DIR" | tail -1 | awk '{print $5}' | sed 's/%//')
    
    # Load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    echo "$cpu_usage $mem_percent $disk_usage $load_avg"
}

# Check DDS communication
check_dds_health() {
    local dds_shm_dir="/tmp/dds_shm/video_service"
    
    if [[ ! -d "$dds_shm_dir" ]]; then
        warning "DDS shared memory directory not found"
        return 1
    fi
    
    # Check if DDS topics are active (simplified check)
    local topic_count=$(ls "$dds_shm_dir" 2>/dev/null | wc -l)
    if [[ $topic_count -eq 0 ]]; then
        warning "No active DDS topics found"
        return 1
    fi
    
    return 0
}

# Check video device availability
check_video_devices() {
    local devices_found=0
    
    for device in /dev/video*; do
        if [[ -c "$device" ]]; then
            devices_found=$((devices_found + 1))
        fi
    done
    
    if [[ $devices_found -eq 0 ]]; then
        warning "No video devices found"
        return 1
    fi
    
    return 0
}

# Check network connectivity for streaming
check_network() {
    # Check if we can resolve DNS
    if ! nslookup google.com >/dev/null 2>&1; then
        warning "DNS resolution failed"
        return 1
    fi
    
    # Check internet connectivity
    if ! ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        warning "Internet connectivity failed"
        return 1
    fi
    
    return 0
}

# Generate health report
generate_health_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "=== Video Service Health Report - $timestamp ===" | tee -a "$MONITOR_LOG"
    echo
    
    # System statistics
    local sys_stats=($(get_system_stats))
    echo "System Resources:" | tee -a "$MONITOR_LOG"
    echo "  CPU Usage: ${sys_stats[0]}%" | tee -a "$MONITOR_LOG"
    echo "  Memory Usage: ${sys_stats[1]}%" | tee -a "$MONITOR_LOG"
    echo "  Disk Usage: ${sys_stats[2]}%" | tee -a "$MONITOR_LOG"
    echo "  Load Average: ${sys_stats[3]}" | tee -a "$MONITOR_LOG"
    echo
    
    # Service status
    echo "Service Status:" | tee -a "$MONITOR_LOG"
    local all_running=true
    
    for service in "${SERVICES[@]}"; do
        local pid=$(get_service_pid "$service")
        
        if is_service_running "$service"; then
            local stats=($(get_process_stats "$pid"))
            echo -e "  ${GREEN}●${NC} $service (PID: $pid)" | tee -a "$MONITOR_LOG"
            echo "    CPU: ${stats[0]}%, MEM: ${stats[1]}%, Threads: ${stats[2]}, FDs: ${stats[3]}" | tee -a "$MONITOR_LOG"
        else
            echo -e "  ${RED}●${NC} $service (stopped)" | tee -a "$MONITOR_LOG"
            all_running=false
        fi
    done
    echo
    
    # Health checks
    echo "Health Checks:" | tee -a "$MONITOR_LOG"
    
    if check_dds_health; then
        echo -e "  ${GREEN}✓${NC} DDS Communication" | tee -a "$MONITOR_LOG"
    else
        echo -e "  ${RED}✗${NC} DDS Communication" | tee -a "$MONITOR_LOG"
    fi
    
    if check_video_devices; then
        echo -e "  ${GREEN}✓${NC} Video Devices" | tee -a "$MONITOR_LOG"
    else
        echo -e "  ${RED}✗${NC} Video Devices" | tee -a "$MONITOR_LOG"
    fi
    
    if check_network; then
        echo -e "  ${GREEN}✓${NC} Network Connectivity" | tee -a "$MONITOR_LOG"
    else
        echo -e "  ${RED}✗${NC} Network Connectivity" | tee -a "$MONITOR_LOG"
    fi
    
    echo
    
    # Overall status
    if $all_running; then
        success "All services are running"
    else
        error "Some services are not running"
    fi
    
    echo "================================================" | tee -a "$MONITOR_LOG"
    echo
}

# Continuous monitoring mode
continuous_monitor() {
    local interval=${1:-30}  # Default 30 seconds
    
    log "Starting continuous monitoring (interval: ${interval}s)"
    
    while true; do
        generate_health_report
        sleep "$interval"
    done
}

# Alert on high resource usage
check_alerts() {
    local sys_stats=($(get_system_stats))
    local cpu_usage=${sys_stats[0]%.*}  # Remove decimal part
    local mem_usage=${sys_stats[1]%.*}
    local disk_usage=${sys_stats[2]}
    
    # CPU alert
    if [[ $cpu_usage -gt 80 ]]; then
        warning "High CPU usage: ${cpu_usage}%"
    fi
    
    # Memory alert
    if [[ $mem_usage -gt 85 ]]; then
        warning "High memory usage: ${mem_usage}%"
    fi
    
    # Disk alert
    if [[ $disk_usage -gt 90 ]]; then
        warning "High disk usage: ${disk_usage}%"
    fi
    
    # Check for crashed services
    for service in "${SERVICES[@]}"; do
        if ! is_service_running "$service"; then
            error "Service $service is not running"
        fi
    done
}

# Main function
main() {
    # Ensure log directory exists
    mkdir -p "$LOG_DIR"
    
    case "${1:-}" in
        report)
            generate_health_report
            ;;
        monitor)
            continuous_monitor "${2:-30}"
            ;;
        alerts)
            check_alerts
            ;;
        dds)
            if check_dds_health; then
                success "DDS communication is healthy"
            else
                error "DDS communication issues detected"
                exit 1
            fi
            ;;
        network)
            if check_network; then
                success "Network connectivity is healthy"
            else
                error "Network connectivity issues detected"
                exit 1
            fi
            ;;
        *)
            echo "Usage: $0 {report|monitor [interval]|alerts|dds|network}"
            echo ""
            echo "Commands:"
            echo "  report           - Generate one-time health report"
            echo "  monitor [sec]    - Continuous monitoring (default: 30s interval)"
            echo "  alerts           - Check for alert conditions"
            echo "  dds              - Check DDS communication health"
            echo "  network          - Check network connectivity"
            exit 1
            ;;
    esac
}

main "$@"
