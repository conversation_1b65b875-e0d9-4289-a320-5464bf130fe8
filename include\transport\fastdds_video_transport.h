#ifndef FASTDDS_VIDEO_TRANSPORT_H
#define FASTDDS_VIDEO_TRANSPORT_H

#include "video_transport_interface.h"
#include "dds_video_writer.hpp"
#include "dds_video_reader.hpp"
#include "../common.h"
#include <atomic>
#include <mutex>
#include <memory>
#include <vector>

namespace fastdds {
namespace video {
// 视频帧结构
struct Frame {
    uint64_t frame_id;
    uint64_t timestamp;
    uint32_t source_type;
    uint16_t width;
    uint16_t height;
    int32_t  format;
    bool is_keyframe;
    uint32_t data_length;
    std::vector<uint8_t> data;
    bool valid;
    
    // 构造函数
    Frame():
        frame_id(0),
        timestamp(0),
        source_type(0),
        width(0),
        height(0),
        format(0),
        is_keyframe(false),
        data_length(0),
        data(),
        valid(false) {}
    
    Frame(const DDSVideoFrame& dds_frame) {
        frame_id = dds_frame.frame_id();
        timestamp = dds_frame.timestamp();
        source_type = dds_frame.source_type();
        width = dds_frame.width();
        height = dds_frame.height();
        format = dds_frame.format();
        is_keyframe = dds_frame.is_keyframe();
        data_length = dds_frame.data_length();
        data = std::move(dds_frame.data());
        valid = true;
    }

    static void to_dds_frame(const Frame& frame, DDSVideoFrame& dds_frame) {
        dds_frame.frame_id(frame.frame_id);
        dds_frame.timestamp(frame.timestamp);
        dds_frame.source_type(frame.source_type);
        dds_frame.width(frame.width);
        dds_frame.height(frame.height);
        dds_frame.format(frame.format);
        dds_frame.is_keyframe(frame.is_keyframe);
        dds_frame.data_length(frame.data_length);
        dds_frame.data(frame.data);
    }

    // 获取数据大小
    size_t get_data_size() const {
        return data.size();
    }
    
    // 获取数据指针
    const uint8_t* get_data_ptr() const {
        return data.data();
    }
};

class DDSVideoWriter {
private:
    std::unique_ptr<eprosima::fastdds::video::DDSWriter> writer_;

public:
    DDSVideoWriter(const std::string & topic_name, int max_samples = 3) {
        writer_ = std::make_unique<eprosima::fastdds::video::DDSWriter>();
        if (!writer_->init(topic_name, 0, max_samples)) {
            throw std::runtime_error("Failed to initialize DDS Video Writer");
        }
    }
    ~DDSVideoWriter() = default;
    bool write(const Frame& frame) {
        DDSVideoFrame dds_frame;
        Frame::to_dds_frame(frame, dds_frame);
        return writer_->write(const_cast<DDSVideoFrame&>(dds_frame)) == eprosima::fastdds::dds::RETCODE_OK;
    }
};

class DDSVideoReader {
private:
    std::unique_ptr<eprosima::fastdds::video::DDSReader> reader_;
    ThreadSafeQueue<Frame> frame_queue_;

public:
    DDSVideoReader(const std::string & topic_name, int max_samples = 3): frame_queue_(max_samples) {
        reader_ = std::make_unique<eprosima::fastdds::video::DDSReader>();
        if (!reader_->init(topic_name, 0, max_samples, [this](const DDSVideoFrame& dds_frame) {
            Frame f(dds_frame);
            frame_queue_.push(std::move(f));
        })) {
            throw std::runtime_error("Failed to initialize DDS Video Reader");
        }
    }
    ~DDSVideoReader() = default;
    bool read(Frame & frame, int timeout_ms = -1) {
        return frame_queue_.wait_and_pop(frame, timeout_ms);
    }
};
}
}

namespace video_transport {
using namespace fastdds::video;
// FastDDS视频发布者
class FastDDSVideoPublisher : public IVideoPublisher {
private:
    // DDS组件
    std::unique_ptr<DDSVideoWriter> dds_writer_;
    
    // 配置和状态
    TransportConfig config_;
    std::atomic<bool> initialized_;
    std::atomic<int> subscriber_count_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    TransportStats stats_;

public:
    FastDDSVideoPublisher() : initialized_(false), subscriber_count_(0) {}
    
    ~FastDDSVideoPublisher() override {
        cleanup();
    }
    
    // IVideoPublisher接口实现
    bool initialize(const TransportConfig& config) override {
        if (initialized_.load()) {
            return true;
        }

        if (config.type != TransportType::FASTDDS) {
            LOG_E("Invalid config type for FastDDS transport");
            return false;
        }
        
        config_ = config;
        
        try {
            // 创建DDS Writer
            dds_writer_ = std::make_unique<DDSVideoWriter>(config.topic_name, config.max_samples);
            if (!dds_writer_) {
                LOG_E("Failed to initialize DDS writer");
                return false;
            }
            
            initialized_.store(true);
            reset_stats();
            
            LOG_I("FastDDS Video Publisher initialized successfully on topic: %s", 
                  config.topic_name.c_str());
            
            return true;
        } catch (const std::exception& e) {
            LOG_E("Exception during FastDDS Publisher initialization: %s", e.what());
            return false;
        }
    }
    
    void cleanup() override {
        if (initialized_.load()) {
            dds_writer_.reset();
            initialized_.store(false);
        }
    }
    
    // 核心缓冲区管理接口
    BufferResult acquire_buffer(BufferHandle& handle) override {
        if (!initialized_.load()) {
            return BufferResult::TRANSPORT_ERROR;
        }

        // FastDDS使用数据复制模式，创建内部缓冲区数据容器（空）
        handle.data = nullptr;
        handle.size = 0;
        handle.used_size = 0;
        handle.transport_type = TransportType::FASTDDS;
        handle.transport_data.fastdds.frame_data = new std::vector<uint8_t>();
        handle.is_valid = true;

        return BufferResult::SUCCESS;
    }

    BufferResult publish_buffer(BufferHandle& handle) override {
        if (!initialized_.load()) {
            return BufferResult::TRANSPORT_ERROR;
        }

        if (!handle.is_valid || handle.transport_type != TransportType::FASTDDS) {
            return BufferResult::INVALID_DATA;
        }

        try {
            // 创建DDS帧
            Frame frame;
            frame.frame_id = handle.metadata.frame_id;
            frame.timestamp = handle.metadata.timestamp;
            frame.width = handle.metadata.width;
            frame.height = handle.metadata.height;
            frame.format = handle.metadata.format;
            frame.is_keyframe = false; // 默认非关键帧
            frame.data_length = handle.used_size;
            frame.valid = true;

            // 设置数据
            if (handle.transport_data.fastdds.frame_data) {
                frame.data = *handle.transport_data.fastdds.frame_data;
            }

            bool success = dds_writer_->write(frame);

            // 清理缓冲区
            delete handle.transport_data.fastdds.frame_data;
            handle.is_valid = false;

            update_stats_sent(handle.used_size, success);

            return success ? BufferResult::SUCCESS : BufferResult::TRANSPORT_ERROR;

        } catch (const std::exception& e) {
            LOG_E("Exception during frame publishing: %s", e.what());
            if (handle.transport_data.fastdds.frame_data) {
                delete handle.transport_data.fastdds.frame_data;
            }
            handle.is_valid = false;
            return BufferResult::TRANSPORT_ERROR;
        }
    }
    
    // V4L2 DMABUF集成 - FastDDS不支持零拷贝
    int get_dma_fd(const BufferHandle& handle) override {
        // FastDDS使用数据复制模式，不支持DMA文件描述符
        return -1;
    }

    // V4L2零拷贝支持检查
    bool supports_v4l2_zero_copy() const override {
        return false; // FastDDS不支持零拷贝
    }
    
    bool has_subscribers() const override {
        return subscriber_count_.load() > 0;
    }
    
    TransportStats get_stats() const override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return stats_;
    }
    
    void reset_stats() override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_ = TransportStats();
        stats_.last_update_time.store(get_current_us());
    }
    
    std::string get_status() const override {
        std::ostringstream oss;
        oss << "FastDDS Publisher Status:\\n";
        oss << "  Initialized: " << (initialized_.load() ? "Yes" : "No") << "\\n";
        oss << "  Topic Name: " << config_.topic_name << "\\n";
        oss << "  Domain ID: " << config_.domain_id << "\\n";
        oss << "  Subscriber Count: " << subscriber_count_.load() << "\\n";
        
        auto stats = get_stats();
        oss << "  Frames Sent: " << stats.frames_sent.load() << "\\n";
        oss << "  Bytes Sent: " << stats.bytes_sent.load() << "\\n";
        oss << "  Dropped Frames: " << stats.dropped_frames.load();
        
        return oss.str();
    }

private:
    void update_stats_sent(size_t bytes, bool success) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        if (success) {
            stats_.frames_sent.fetch_add(1);
            stats_.bytes_sent.fetch_add(bytes);
        } else {
            stats_.dropped_frames.fetch_add(1);
        }
        stats_.last_update_time.store(get_current_us());
    }
};

// FastDDS视频订阅者 - 改进版本
class FastDDSVideoSubscriber : public IVideoSubscriber {
private:
    // DDS组件
    std::unique_ptr<DDSVideoReader> dds_reader_;
    
    // 配置和状态
    TransportConfig config_;
    std::atomic<bool> initialized_;
    std::atomic<bool> connected_;
    
    // 回调函数
    std::function<void(BufferHandle&)> buffer_callback_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    TransportStats stats_;
    
    // 帧队列（用于阻塞接收）
    ThreadSafeQueue<fastdds::video::Frame> frame_queue_;

public:
    FastDDSVideoSubscriber() : initialized_(false), connected_(false), frame_queue_(10) {}
    
    ~FastDDSVideoSubscriber() override {
        cleanup();
    }
    
    // IVideoSubscriber接口实现
    bool initialize(const TransportConfig& config) override {
        if (initialized_.load()) {
            return true;
        }
        
        if (config.type != TransportType::FASTDDS) {
            LOG_E("Invalid config type for FastDDS transport");
            return false;
        }
        
        config_ = config;
        
        try {
            // 创建DDS Reader
            dds_reader_ = std::make_unique<DDSVideoReader>(config.topic_name, config.max_samples);
            if (!dds_reader_) {
                LOG_E("Failed to initialize DDS reader");
                return false;
            }
            
            initialized_.store(true);
            connected_.store(true);
            reset_stats();
            
            LOG_I("FastDDS Video Subscriber initialized successfully on topic: %s", 
                  config.topic_name.c_str());
            
            return true;
        } catch (const std::exception& e) {
            LOG_E("Exception during FastDDS Subscriber initialization: %s", e.what());
            return false;
        }
    }
    
    void cleanup() override {
        if (initialized_.load()) {
            dds_reader_.reset();
            initialized_.store(false);
            connected_.store(false);
        }
    }
    
    // 缓冲区管理接口 - FastDDS使用数据复制模式
    BufferResult receive_frame_buffer(BufferHandle& handle, int timeout_ms) override {
        // 尝试从队列获取帧
        fastdds::video::Frame frame;
        if (!frame_queue_.wait_and_pop(frame, timeout_ms)) {
            return BufferResult::TIMEOUT;
        }

        // 将Frame数据包装为BufferHandle
        handle.data = frame.data.data();
        handle.size = frame.data.size();
        handle.used_size = frame.data.size();
        handle.metadata.frame_id = frame.frame_id;
        handle.metadata.timestamp = frame.timestamp;
        handle.metadata.width = frame.width;
        handle.metadata.height = frame.height;
        handle.metadata.format = frame.format;
        handle.metadata.data_size = frame.data.size();
        handle.transport_type = TransportType::FASTDDS;
        handle.transport_data.fastdds.frame_data = new std::vector<uint8_t>(std::move(frame.data));
        handle.is_valid = true;

        return BufferResult::SUCCESS;
    }

    BufferResult return_frame_buffer(BufferHandle& handle) override {
        // 清理FastDDS缓冲区数据
        if (handle.is_valid && handle.transport_data.fastdds.frame_data) {
            delete handle.transport_data.fastdds.frame_data;
            handle.is_valid = false;
        }
        return BufferResult::SUCCESS;
    }
    
    void set_buffer_callback(std::function<void(BufferHandle&)> callback) override {
        buffer_callback_ = std::move(callback);
    }
    
    bool is_connected() const override {
        return connected_.load();
    }
    
    TransportStats get_stats() const override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return stats_;
    }
    
    void reset_stats() override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_ = TransportStats();
        stats_.last_update_time.store(get_current_us());
    }
    
    std::string get_status() const override {
        std::ostringstream oss;
        oss << "FastDDS Subscriber Status:\\n";
        oss << "  Initialized: " << (initialized_.load() ? "Yes" : "No") << "\\n";
        oss << "  Connected: " << (connected_.load() ? "Yes" : "No") << "\\n";
        oss << "  Topic Name: " << config_.topic_name << "\\n";
        oss << "  Domain ID: " << config_.domain_id << "\\n";
        
        auto stats = get_stats();
        oss << "  Frames Received: " << stats.frames_received.load() << "\\n";
        oss << "  Bytes Received: " << stats.bytes_received.load();
        
        return oss.str();
    }

private:
    void on_dds_frame_received(const DDSVideoFrame& dds_frame) {
        try {
            // 转换DDS帧到内部Frame格式
            fastdds::video::Frame frame;
            frame.frame_id = dds_frame.frame_id();
            frame.timestamp = dds_frame.timestamp();
            frame.width = dds_frame.width();
            frame.height = dds_frame.height();
            frame.format = dds_frame.format();
            frame.is_keyframe = dds_frame.is_keyframe();
            frame.data_length = dds_frame.data_length();
            frame.data = dds_frame.data();
            frame.valid = dds_frame.valid();

            // 如果设置了缓冲区回调，创建BufferHandle并调用
            if (buffer_callback_) {
                BufferHandle handle;
                handle.data = frame.data.data();
                handle.size = frame.data.size();
                handle.used_size = frame.data.size();
                handle.metadata.frame_id = frame.frame_id;
                handle.metadata.timestamp = frame.timestamp;
                handle.metadata.width = frame.width;
                handle.metadata.height = frame.height;
                handle.metadata.format = frame.format;
                handle.metadata.data_size = frame.data.size();
                handle.transport_type = TransportType::FASTDDS;
                handle.transport_data.fastdds.frame_data = new std::vector<uint8_t>(frame.data);
                handle.is_valid = true;

                buffer_callback_(handle);
            } else {
                // 将帧放入队列供同步接收
                frame_queue_.push(frame);
            }

            update_stats_received(frame.data.size());

        } catch (const std::exception& e) {
            LOG_E("Exception in FastDDS frame callback: %s", e.what());
        }
    }
    
    void update_stats_received(size_t bytes) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.frames_received.fetch_add(1);
        stats_.bytes_received.fetch_add(bytes);
        stats_.last_update_time.store(get_current_us());
    }
};

} // namespace video_transport

#endif // FASTDDS_VIDEO_TRANSPORT_H