#ifndef FASTDDS_VIDEO_TRANSPORT_H
#define FASTDDS_VIDEO_TRANSPORT_H

#include "video_transport_interface.h"
#include "../common.h"
#include <atomic>
#include <mutex>
#include <memory>
#include <vector>

namespace video_transport {


// FastDDS视频发布者
class FastDDSVideoPublisher : public IVideoPublisher {
private:
    // DDS组件
    std::unique_ptr<eprosima::fastdds::video::DDSWriter> dds_writer_;
    std::unique_ptr<FastDDSBufferProvider> buffer_provider_;
    
    // 配置和状态
    TransportConfig config_;
    std::atomic<bool> initialized_;
    std::atomic<int> subscriber_count_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    TransportStats stats_;

public:
    FastDDSVideoPublisher() : initialized_(false), subscriber_count_(0) {}
    
    ~FastDDSVideoPublisher() override {
        cleanup();
    }
    
    // IVideoPublisher接口实现
    bool initialize(const TransportConfig& config) override {
        if (initialized_.load()) {
            return true;
        }
        
        if (config.type != TransportConfig::Type::FASTDDS) {
            LOG_E("Invalid config type for FastDDS transport");
            return false;
        }
        
        config_ = config;
        
        try {
            // 创建DDS Writer
            dds_writer_ = std::make_unique<eprosima::fastdds::video::DDSWriter>();
            if (!dds_writer_->init(config.topic_name, config.domain_id, config.max_samples)) {
                LOG_E("Failed to initialize DDS writer");
                return false;
            }
            
            // 创建缓冲区提供者
            size_t buffer_size = (config.buffer_size > 0) ? config.buffer_size : (1920 * 1080 * 3); // 默认大小
            buffer_provider_ = std::make_unique<FastDDSBufferProvider>(buffer_size);
            
            initialized_.store(true);
            reset_stats();
            
            LOG_I("FastDDS Video Publisher initialized successfully on topic: %s", 
                  config.topic_name.c_str());
            
            return true;
        } catch (const std::exception& e) {
            LOG_E("Exception during FastDDS Publisher initialization: %s", e.what());
            return false;
        }
    }
    
    void cleanup() override {
        if (initialized_.load()) {
            buffer_provider_.reset();
            dds_writer_.reset();
            initialized_.store(false);
        }
    }
    
    // 新的缓冲区管理接口
    BufferResult get_buffer_for_frame(BufferHandle& handle) override {
        if (!initialized_.load()) {
            return BufferResult::TRANSPORT_ERROR;
        }
        
        return buffer_provider_->acquire_buffer_for_production(handle);
    }
    
    BufferResult publish_frame(BufferHandle& handle) override {
        if (!initialized_.load()) {
            return BufferResult::TRANSPORT_ERROR;
        }
        
        if (!handle.is_valid || handle.transport_type != TransportType::FASTDDS) {
            return BufferResult::INVALID_DATA;
        }
        
        try {
            // 创建FastDDS Frame
            fastdds::video::Frame frame;
            
            // 设置元数据
            frame.frame_id = handle.metadata.buffer_id;
            frame.timestamp = handle.metadata.timestamp;
            frame.width = handle.metadata.width;
            frame.height = handle.metadata.height;
            frame.format = handle.metadata.format;
            frame.data_length = handle.used_size;
            frame.valid = true;
            
            // 复制数据
            frame.data.resize(handle.used_size);
            memcpy(frame.data.data(), handle.data, handle.used_size);
            
            // 发布帧
            bool success = publish_frame(frame);
            
            // 释放缓冲区
            buffer_provider_->release_buffer_from_production(handle);
            
            return success ? BufferResult::SUCCESS : BufferResult::TRANSPORT_ERROR;
            
        } catch (const std::exception& e) {
            LOG_E("Exception during frame publishing: %s", e.what());
            buffer_provider_->release_buffer_from_production(handle);
            return BufferResult::TRANSPORT_ERROR;
        }
    }
    
    // 兼容性接口 - FastDDS原生方式
    bool publish_frame(const fastdds::video::Frame& frame) override {
        if (!initialized_.load() || !dds_writer_) {
            return false;
        }
        
        try {
            // 创建DDS帧
            DDSVideoFrame dds_frame;
            dds_frame.frame_id(frame.frame_id);
            dds_frame.timestamp(frame.timestamp);
            dds_frame.source_type(frame.source_type);
            dds_frame.width(frame.width);
            dds_frame.height(frame.height);
            dds_frame.format(frame.format);
            dds_frame.is_keyframe(frame.is_keyframe);
            dds_frame.data_length(frame.data_length);
            dds_frame.data(frame.data);
            dds_frame.valid(frame.valid);
            
            bool success = dds_writer_->write(dds_frame);
            
            update_stats_sent(frame.data.size(), success);
            
            return success;
            
        } catch (const std::exception& e) {
            LOG_E("Exception during FastDDS frame publishing: %s", e.what());
            update_stats_sent(frame.data.size(), false);
            return false;
        }
    }
    
    bool has_subscribers() const override {
        return subscriber_count_.load() > 0;
    }
    
    TransportStats get_stats() const override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return stats_;
    }
    
    void reset_stats() override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_ = TransportStats();
        stats_.last_update_time.store(get_current_us());
    }
    
    std::string get_status() const override {
        std::ostringstream oss;
        oss << "FastDDS Publisher Status:\\n";
        oss << "  Initialized: " << (initialized_.load() ? "Yes" : "No") << "\\n";
        oss << "  Topic Name: " << config_.topic_name << "\\n";
        oss << "  Domain ID: " << config_.domain_id << "\\n";
        oss << "  Subscriber Count: " << subscriber_count_.load() << "\\n";
        
        auto stats = get_stats();
        oss << "  Frames Sent: " << stats.frames_sent.load() << "\\n";
        oss << "  Bytes Sent: " << stats.bytes_sent.load() << "\\n";
        oss << "  Dropped Frames: " << stats.dropped_frames.load();
        
        return oss.str();
    }

private:
    void update_stats_sent(size_t bytes, bool success) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        if (success) {
            stats_.frames_sent.fetch_add(1);
            stats_.bytes_sent.fetch_add(bytes);
        } else {
            stats_.dropped_frames.fetch_add(1);
        }
        stats_.last_update_time.store(get_current_us());
    }
};

// FastDDS视频订阅者 - 改进版本
class FastDDSVideoSubscriber : public IVideoSubscriber {
private:
    // DDS组件
    std::unique_ptr<eprosima::fastdds::video::DDSReader> dds_reader_;
    
    // 配置和状态
    TransportConfig config_;
    std::atomic<bool> initialized_;
    std::atomic<bool> connected_;
    
    // 回调函数
    std::function<void(const fastdds::video::Frame&)> frame_callback_;
    std::function<void(BufferHandle&)> buffer_callback_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    TransportStats stats_;
    
    // 帧队列（用于阻塞接收）
    ThreadSafeQueue<fastdds::video::Frame> frame_queue_;

public:
    FastDDSVideoSubscriber() : initialized_(false), connected_(false), frame_queue_(10) {}
    
    ~FastDDSVideoSubscriber() override {
        cleanup();
    }
    
    // IVideoSubscriber接口实现
    bool initialize(const TransportConfig& config) override {
        if (initialized_.load()) {
            return true;
        }
        
        if (config.type != TransportConfig::Type::FASTDDS) {
            LOG_E("Invalid config type for FastDDS transport");
            return false;
        }
        
        config_ = config;
        
        try {
            // 创建DDS Reader
            dds_reader_ = std::make_unique<eprosima::fastdds::video::DDSReader>();
            if (!dds_reader_->init(config.topic_name, config.domain_id, config.max_samples,
                                  [this](const DDSVideoFrame& dds_frame) {
                                      this->on_dds_frame_received(dds_frame);
                                  })) {
                LOG_E("Failed to initialize DDS reader");
                return false;
            }
            
            initialized_.store(true);
            connected_.store(true);
            reset_stats();
            
            LOG_I("FastDDS Video Subscriber initialized successfully on topic: %s", 
                  config.topic_name.c_str());
            
            return true;
        } catch (const std::exception& e) {
            LOG_E("Exception during FastDDS Subscriber initialization: %s", e.what());
            return false;
        }
    }
    
    void cleanup() override {
        if (initialized_.load()) {
            dds_reader_.reset();
            initialized_.store(false);
            connected_.store(false);
        }
    }
    
    // 新的缓冲区管理接口
    BufferResult receive_frame_buffer(BufferHandle& handle, int timeout_ms) override {
        // FastDDS使用数据复制模式，不适用缓冲区借还模式
        return BufferResult::TRANSPORT_ERROR;
    }
    
    BufferResult return_frame_buffer(BufferHandle& handle) override {
        // FastDDS使用数据复制模式，不需要归还缓冲区
        return BufferResult::SUCCESS;
    }
    
    // 兼容性接口
    bool receive_frame(fastdds::video::Frame& frame, int timeout_ms) override {
        return frame_queue_.wait_and_pop(frame, timeout_ms);
    }
    
    void set_frame_callback(std::function<void(const fastdds::video::Frame&)> callback) override {
        frame_callback_ = callback;
    }
    
    void set_buffer_callback(std::function<void(BufferHandle&)> callback) override {
        buffer_callback_ = callback;
        LOG_W("Buffer callback is not supported for FastDDS transport");
    }
    
    bool is_connected() const override {
        return connected_.load();
    }
    
    TransportStats get_stats() const override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return stats_;
    }
    
    void reset_stats() override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_ = TransportStats();
        stats_.last_update_time.store(get_current_us());
    }
    
    std::string get_status() const override {
        std::ostringstream oss;
        oss << "FastDDS Subscriber Status:\\n";
        oss << "  Initialized: " << (initialized_.load() ? "Yes" : "No") << "\\n";
        oss << "  Connected: " << (connected_.load() ? "Yes" : "No") << "\\n";
        oss << "  Topic Name: " << config_.topic_name << "\\n";
        oss << "  Domain ID: " << config_.domain_id << "\\n";
        
        auto stats = get_stats();
        oss << "  Frames Received: " << stats.frames_received.load() << "\\n";
        oss << "  Bytes Received: " << stats.bytes_received.load();
        
        return oss.str();
    }

private:
    void on_dds_frame_received(const DDSVideoFrame& dds_frame) {
        try {
            // 转换DDS帧到内部Frame格式
            fastdds::video::Frame frame;
            frame.frame_id = dds_frame.frame_id();
            frame.timestamp = dds_frame.timestamp();
            frame.source_type = dds_frame.source_type();
            frame.width = dds_frame.width();
            frame.height = dds_frame.height();
            frame.format = dds_frame.format();
            frame.is_keyframe = dds_frame.is_keyframe();
            frame.data_length = dds_frame.data_length();
            frame.data = dds_frame.data();
            frame.valid = dds_frame.valid();
            
            // 如果设置了帧回调，调用它
            if (frame_callback_) {
                frame_callback_(frame);
            } else {
                // 将帧放入队列供同步接收
                frame_queue_.push(frame);
            }
            
            update_stats_received(frame.data.size());
            
        } catch (const std::exception& e) {
            LOG_E("Exception in FastDDS frame callback: %s", e.what());
        }
    }
    
    void update_stats_received(size_t bytes) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.frames_received.fetch_add(1);
        stats_.bytes_received.fetch_add(bytes);
        stats_.last_update_time.store(get_current_us());
    }
};

} // namespace video_transport

#endif // FASTDDS_VIDEO_TRANSPORT_H