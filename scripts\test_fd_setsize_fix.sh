#!/bin/bash

# Test script to verify the FD_SETSIZE fix for video_control service
# This addresses the "bit out of range 0 - FD_SETSIZE on fd_set" error

set -e

echo "=== FD_SETSIZE Fix Verification Test ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}[OK]${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[WARN]${NC} $message"
    else
        echo -e "${RED}[ERROR]${NC} $message"
    fi
}

# Check if we're in the project root
if [ ! -f "CMakeLists.txt" ]; then
    print_status "ERROR" "Please run this script from the project root directory"
    exit 1
fi

print_status "OK" "Found project root directory"

# Create test directories
echo -e "\n--- Creating Test Directories ---"
sudo mkdir -p /mnt/sdcard/photos /mnt/sdcard/videos 2>/dev/null || true
sudo chmod 777 /mnt/sdcard /mnt/sdcard/photos /mnt/sdcard/videos 2>/dev/null || true
print_status "OK" "Test directories created"

# Build the test
echo -e "\n--- Building Test ---"
cd build

if make test_video_control -j$(nproc); then
    print_status "OK" "Test compilation successful"
else
    print_status "ERROR" "Test compilation failed"
    exit 1
fi

# Run the test multiple times to catch the FD_SETSIZE error
echo -e "\n--- Running Multiple Test Iterations ---"
echo "Running test 5 times to verify FD_SETSIZE fix..."

test_passed=0
test_failed=0

for i in {1..5}; do
    echo -e "\n--- Test Iteration $i/5 ---"
    
    # Run test with timeout and capture output
    if timeout 60 ./test_video_control > "test_output_$i.log" 2>&1; then
        print_status "OK" "Test iteration $i completed successfully"
        test_passed=$((test_passed + 1))
        
        # Check for specific error patterns
        if grep -q "bit out of range.*FD_SETSIZE" "test_output_$i.log"; then
            print_status "ERROR" "FD_SETSIZE error still present in iteration $i"
            test_failed=$((test_failed + 1))
        elif grep -q "terminated" "test_output_$i.log"; then
            print_status "ERROR" "Process terminated unexpectedly in iteration $i"
            test_failed=$((test_failed + 1))
        else
            print_status "OK" "No FD_SETSIZE errors detected in iteration $i"
        fi
        
    else
        print_status "ERROR" "Test iteration $i failed or timed out"
        test_failed=$((test_failed + 1))
        
        # Check if it was the FD_SETSIZE error
        if grep -q "bit out of range.*FD_SETSIZE" "test_output_$i.log" 2>/dev/null; then
            print_status "ERROR" "FD_SETSIZE error caused failure in iteration $i"
        fi
    fi
    
    # Small delay between tests
    sleep 2
done

# Analyze results
echo -e "\n--- Test Results Analysis ---"
print_status "INFO" "Test iterations completed: $test_passed passed, $test_failed failed"

# Check for specific fixes in the code
echo -e "\n--- Verifying Code Fixes ---"

# Check if UDP receiver loop has FD_SETSIZE validation
if grep -q "udp_socket_fd_ >= FD_SETSIZE" ../src/video_control.cpp; then
    print_status "OK" "FD_SETSIZE validation added to UDP receiver loop"
else
    print_status "WARN" "FD_SETSIZE validation not found in UDP receiver loop"
fi

# Check if socket validity is checked before select()
if grep -q "udp_socket_fd_ < 0" ../src/video_control.cpp; then
    print_status "OK" "Socket validity check added"
else
    print_status "WARN" "Socket validity check not found"
fi

# Check if EBADF error is handled
if grep -q "EBADF" ../src/video_control.cpp; then
    print_status "OK" "EBADF error handling added"
else
    print_status "WARN" "EBADF error handling not found"
fi

# Check if stop() method closes socket before joining threads
if grep -A 10 -B 5 "stop_requested_.store(true)" ../src/video_control.cpp | grep -q "close(udp_socket_fd_)"; then
    print_status "OK" "Socket closed before thread join in stop() method"
else
    print_status "WARN" "Socket closure order may need verification"
fi

# Analyze test outputs for patterns
echo -e "\n--- Analyzing Test Output Patterns ---"

total_errors=0
for i in {1..5}; do
    if [ -f "test_output_$i.log" ]; then
        # Count specific error types
        fd_errors=$(grep -c "bit out of range.*FD_SETSIZE" "test_output_$i.log" 2>/dev/null || echo 0)
        socket_errors=$(grep -c "UDP.*error" "test_output_$i.log" 2>/dev/null || echo 0)
        terminated_errors=$(grep -c "terminated" "test_output_$i.log" 2>/dev/null || echo 0)
        
        total_errors=$((total_errors + fd_errors + socket_errors + terminated_errors))
        
        if [ $fd_errors -gt 0 ]; then
            print_status "ERROR" "Iteration $i: $fd_errors FD_SETSIZE errors"
        fi
        if [ $socket_errors -gt 0 ]; then
            print_status "WARN" "Iteration $i: $socket_errors UDP socket errors"
        fi
        if [ $terminated_errors -gt 0 ]; then
            print_status "ERROR" "Iteration $i: $terminated_errors termination errors"
        fi
    fi
done

# Final assessment
echo -e "\n--- Final Assessment ---"

if [ $total_errors -eq 0 ] && [ $test_passed -ge 4 ]; then
    print_status "OK" "🎉 FD_SETSIZE fix verification PASSED!"
    echo ""
    echo "Fixes verified:"
    echo "✓ No FD_SETSIZE errors detected in multiple test runs"
    echo "✓ Socket validity checks added to UDP receiver loop"
    echo "✓ Proper error handling for closed sockets"
    echo "✓ Improved service stop sequence"
    echo ""
    echo "The service now properly:"
    echo "- Validates socket fd before using with select()"
    echo "- Checks for socket closure during operation"
    echo "- Handles EBADF errors gracefully"
    echo "- Closes socket before joining threads"
    
    exit_code=0
else
    print_status "ERROR" "❌ FD_SETSIZE fix verification FAILED!"
    echo ""
    echo "Issues detected:"
    echo "- Total errors across all iterations: $total_errors"
    echo "- Successful test iterations: $test_passed/5"
    echo "- Failed test iterations: $test_failed/5"
    echo ""
    echo "Recommendations:"
    echo "1. Check test output logs: test_output_*.log"
    echo "2. Verify socket handling in UDP receiver loop"
    echo "3. Ensure proper thread synchronization"
    echo "4. Test with different socket fd values"
    
    exit_code=1
fi

# Show sample output from last test
echo -e "\n--- Sample Test Output (Last Iteration) ---"
if [ -f "test_output_5.log" ]; then
    echo "Last 20 lines of test output:"
    tail -20 "test_output_5.log"
fi

# Cleanup
echo -e "\n--- Cleanup ---"
rm -f test_output_*.log
print_status "OK" "Test output files cleaned up"

cd ..
exit $exit_code
