#!/bin/bash

# RTSP服务器编译脚本
# 用于编译和测试RTSP服务器组件

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
SRC_DIR="$PROJECT_ROOT/src"
INCLUDE_DIR="$PROJECT_ROOT/include"
DDS_DIR="$PROJECT_ROOT/dds_video_frame"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "$DEBUG" == "1" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 显示使用说明
show_usage() {
    cat << EOF
RTSP服务器编译脚本

用法: $0 [选项] [目标]

目标:
    all             编译所有组件 (默认)
    rtsp_server     只编译RTSP服务器
    test            编译测试程序
    clean           清理编译文件
    install         安装到系统
    check           检查依赖项

选项:
    --debug         启用调试模式
    --verbose       显示详细编译信息
    --jobs N        并行编译任务数 (默认: CPU核心数)
    --prefix PATH   安装前缀 (默认: /usr/local)
    --help          显示此帮助信息

示例:
    $0                          # 编译所有组件
    $0 rtsp_server             # 只编译RTSP服务器
    $0 --debug test            # 调试模式编译测试
    $0 clean                   # 清理编译文件
EOF
}

# 检查依赖项
check_dependencies() {
    local missing_deps=()
    
    # 检查编译器
    if ! command -v g++ &> /dev/null; then
        missing_deps+=("g++ (C++编译器)")
    fi
    
    # 检查pkg-config
    if ! command -v pkg-config &> /dev/null; then
        missing_deps+=("pkg-config")
    fi
    
    # 检查GStreamer开发库
    if ! pkg-config --exists gstreamer-1.0; then
        missing_deps+=("libgstreamer1.0-dev")
    fi
    
    if ! pkg-config --exists gstreamer-rtsp-server-1.0; then
        missing_deps+=("libgstreamer-rtsp-server-1.0-dev")
    fi
    
    # 检查FFmpeg开发库
    if ! pkg-config --exists libavformat; then
        missing_deps+=("libavformat-dev")
    fi
    
    # 检查FastDDS
    if [[ ! -f "/usr/include/fastdds/dds/DomainParticipant.hpp" ]] && [[ ! -f "/usr/local/include/fastdds/dds/DomainParticipant.hpp" ]]; then
        missing_deps+=("libfastdds-dev")
    fi
    
    # 检查JsonCpp (可选)
    if ! pkg-config --exists jsoncpp; then
        log_warn "JsonCpp未找到，配置文件支持将被禁用"
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少以下依赖项:"
        for dep in "${missing_deps[@]}"; do
            log_error "  - $dep"
        done
        log_info "在Ubuntu/Debian上安装依赖项:"
        log_info "  sudo apt-get install build-essential pkg-config"
        log_info "  sudo apt-get install libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev"
        log_info "  sudo apt-get install libgstreamer-rtsp-server-1.0-dev"
        log_info "  sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev"
        log_info "  sudo apt-get install libfastdds-dev libfastcdr-dev"
        log_info "  sudo apt-get install libjsoncpp-dev"
        return 1
    fi
    
    log_info "所有依赖项检查通过"
    return 0
}

# 获取编译标志
get_compile_flags() {
    local cflags="-std=c++17 -Wall -Wextra -I$INCLUDE_DIR -I$DDS_DIR"
    local libs="-pthread -ldl"
    
    # 添加调试或优化标志
    if [[ "$DEBUG" == "1" ]]; then
        cflags="$cflags -g -O0 -DDEBUG"
    else
        cflags="$cflags -O3 -DNDEBUG"
    fi
    
    # FFmpeg标志
    if pkg-config --exists libavformat libavcodec libavutil; then
        cflags="$cflags $(pkg-config --cflags libavformat libavcodec libavutil)"
        libs="$libs $(pkg-config --libs libavformat libavcodec libavutil)"
    fi
    
    # GStreamer标志
    if pkg-config --exists gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-rtsp-server-1.0; then
        cflags="$cflags $(pkg-config --cflags gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-rtsp-server-1.0)"
        libs="$libs $(pkg-config --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-rtsp-server-1.0)"
    fi
    
    # FastDDS标志
    cflags="$cflags -I/usr/include/fastdds -I/usr/include/fastcdr"
    libs="$libs -lfastdds -lfastcdr"
    
    # JsonCpp标志 (可选)
    if pkg-config --exists jsoncpp; then
        cflags="$cflags $(pkg-config --cflags jsoncpp) -DHAVE_JSONCPP"
        libs="$libs $(pkg-config --libs jsoncpp)"
    fi
    
    echo "CFLAGS=$cflags"
    echo "LIBS=$libs"
}

# 编译DDS库
compile_dds_lib() {
    log_info "编译DDS库..."
    
    local dds_sources=(
        "$DDS_DIR/DDSVideoFrameTypeObjectSupport.cxx"
        "$DDS_DIR/DDSVideoFramePubSubTypes.cxx"
        "$DDS_DIR/dds_video_reader.cxx"
        "$DDS_DIR/dds_video_writer.cxx"
    )
    
    local dds_objects=()
    for src in "${dds_sources[@]}"; do
        if [[ ! -f "$src" ]]; then
            log_warn "DDS源文件不存在: $src"
            continue
        fi
        
        local obj="$BUILD_DIR/obj/$(basename "$src" .cxx).o"
        dds_objects+=("$obj")
        
        if [[ "$src" -nt "$obj" ]] || [[ ! -f "$obj" ]]; then
            log_debug "编译: $src -> $obj"
            if [[ "$VERBOSE" == "1" ]]; then
                g++ $CFLAGS -I$DDS_DIR -c -o "$obj" "$src"
            else
                g++ $CFLAGS -I$DDS_DIR -c -o "$obj" "$src" 2>/dev/null
            fi
            
            if [[ $? -ne 0 ]]; then
                log_error "编译DDS源文件失败: $src"
                return 1
            fi
        fi
    done
    
    # 创建静态库
    local dds_lib="$BUILD_DIR/libDDSVideoFrame.a"
    if [[ ${#dds_objects[@]} -gt 0 ]]; then
        log_debug "创建DDS库: $dds_lib"
        ar rcs "$dds_lib" "${dds_objects[@]}"
        if [[ $? -ne 0 ]]; then
            log_error "创建DDS库失败"
            return 1
        fi
    fi
    
    log_info "DDS库编译完成"
    return 0
}

# 编译RTSP服务器
compile_rtsp_server() {
    log_info "编译RTSP服务器..."
    
    local sources=(
        "$SRC_DIR/rtsp_server_main.cpp"
        "$SRC_DIR/rtsp_server.cpp"
    )
    
    local output="$BUILD_DIR/rtsp_server_main"
    local dds_lib="$BUILD_DIR/libDDSVideoFrame.a"
    
    # 检查是否需要重新编译
    local need_compile=false
    if [[ ! -f "$output" ]]; then
        need_compile=true
    else
        for src in "${sources[@]}"; do
            if [[ "$src" -nt "$output" ]]; then
                need_compile=true
                break
            fi
        done
        
        # 检查头文件
        for header in "$INCLUDE_DIR"/*.h; do
            if [[ "$header" -nt "$output" ]]; then
                need_compile=true
                break
            fi
        done
    fi
    
    if [[ "$need_compile" == "true" ]]; then
        log_debug "编译RTSP服务器: ${sources[*]} -> $output"
        if [[ "$VERBOSE" == "1" ]]; then
            g++ $CFLAGS -o "$output" "${sources[@]}" "$dds_lib" $LIBS
        else
            g++ $CFLAGS -o "$output" "${sources[@]}" "$dds_lib" $LIBS 2>/dev/null
        fi
        
        if [[ $? -eq 0 ]]; then
            log_info "RTSP服务器编译成功: $output"
        else
            log_error "RTSP服务器编译失败"
            return 1
        fi
    else
        log_info "RTSP服务器已是最新版本"
    fi
    
    return 0
}

# 编译测试程序
compile_test() {
    log_info "编译测试程序..."
    
    # 检查Google Test
    if ! pkg-config --exists gtest; then
        log_warn "Google Test未找到，跳过测试编译"
        return 0
    fi
    
    local test_src="$PROJECT_ROOT/test/test_rtsp_server.cpp"
    local test_output="$BUILD_DIR/test_rtsp_server"
    local dds_lib="$BUILD_DIR/libDDSVideoFrame.a"
    
    if [[ ! -f "$test_src" ]]; then
        log_warn "测试源文件不存在: $test_src"
        return 0
    fi
    
    local test_cflags="$CFLAGS $(pkg-config --cflags gtest)"
    local test_libs="$LIBS $(pkg-config --libs gtest) -lgtest_main"
    
    log_debug "编译测试程序: $test_src -> $test_output"
    if [[ "$VERBOSE" == "1" ]]; then
        g++ $test_cflags -o "$test_output" "$test_src" "$SRC_DIR/rtsp_server.cpp" "$dds_lib" $test_libs
    else
        g++ $test_cflags -o "$test_output" "$test_src" "$SRC_DIR/rtsp_server.cpp" "$dds_lib" $test_libs 2>/dev/null
    fi
    
    if [[ $? -eq 0 ]]; then
        log_info "测试程序编译成功: $test_output"
    else
        log_error "测试程序编译失败"
        return 1
    fi
    
    return 0
}

# 清理编译文件
clean_build() {
    log_info "清理编译文件..."
    rm -rf "$BUILD_DIR"
    log_info "清理完成"
}

# 安装程序
install_program() {
    local prefix="${PREFIX:-/usr/local}"
    log_info "安装到: $prefix"
    
    # 创建目录
    sudo mkdir -p "$prefix/bin"
    sudo mkdir -p "$prefix/share/video_service"
    
    # 安装可执行文件
    if [[ -f "$BUILD_DIR/rtsp_server_main" ]]; then
        sudo cp "$BUILD_DIR/rtsp_server_main" "$prefix/bin/"
        sudo chmod 755 "$prefix/bin/rtsp_server_main"
        log_info "已安装: $prefix/bin/rtsp_server_main"
    fi
    
    # 安装配置文件
    if [[ -f "$PROJECT_ROOT/config/rtsp_server.json" ]]; then
        sudo cp "$PROJECT_ROOT/config/rtsp_server.json" "$prefix/share/video_service/"
        log_info "已安装: $prefix/share/video_service/rtsp_server.json"
    fi
    
    # 安装脚本
    if [[ -f "$PROJECT_ROOT/scripts/start_rtsp_server.sh" ]]; then
        sudo cp "$PROJECT_ROOT/scripts/start_rtsp_server.sh" "$prefix/bin/"
        sudo chmod 755 "$prefix/bin/start_rtsp_server.sh"
        log_info "已安装: $prefix/bin/start_rtsp_server.sh"
    fi
    
    log_info "安装完成"
}

# 创建构建目录
create_build_dir() {
    mkdir -p "$BUILD_DIR"
    mkdir -p "$BUILD_DIR/obj"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --debug)
                DEBUG=1
                shift
                ;;
            --verbose)
                VERBOSE=1
                shift
                ;;
            --jobs)
                JOBS="$2"
                shift 2
                ;;
            --prefix)
                PREFIX="$2"
                shift 2
                ;;
            --help)
                show_usage
                exit 0
                ;;
            all|rtsp_server|test|clean|install|check)
                TARGET="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    # 设置默认值
    TARGET="${TARGET:-all}"
    JOBS="${JOBS:-$(nproc)}"
    
    # 获取编译标志
    eval $(get_compile_flags)
    
    case "$TARGET" in
        check)
            check_dependencies
            exit $?
            ;;
        clean)
            clean_build
            exit 0
            ;;
        install)
            install_program
            exit $?
            ;;
    esac
    
    # 检查依赖项
    if ! check_dependencies; then
        exit 1
    fi
    
    # 创建构建目录
    create_build_dir
    
    # 执行编译
    case "$TARGET" in
        all)
            compile_dds_lib && compile_rtsp_server && compile_test
            ;;
        rtsp_server)
            compile_dds_lib && compile_rtsp_server
            ;;
        test)
            compile_dds_lib && compile_test
            ;;
        *)
            log_error "未知目标: $TARGET"
            show_usage
            exit 1
            ;;
    esac
    
    if [[ $? -eq 0 ]]; then
        log_info "编译完成"
    else
        log_error "编译失败"
        exit 1
    fi
}

# 运行主函数
main "$@"
