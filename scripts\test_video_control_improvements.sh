#!/bin/bash

# Test script to verify video_control service improvements
# 1. UDP blocking receive mode
# 2. Frame processors only read when recording
# 3. Public methods for external control
# 4. Enhanced test coverage

set -e

echo "=== Video Control Service Improvements Test ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}[OK]${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[WARN]${NC} $message"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}[INFO]${NC} $message"
    else
        echo -e "${RED}[ERROR]${NC} $message"
    fi
}

# Check if we're in the project root
if [ ! -f "CMakeLists.txt" ]; then
    print_status "ERROR" "Please run this script from the project root directory"
    exit 1
fi

print_status "OK" "Found project root directory"

# Create test directories
echo -e "\n--- Creating Test Directories ---"
sudo mkdir -p /mnt/sdcard/photos /mnt/sdcard/videos 2>/dev/null || true
sudo chmod 777 /mnt/sdcard /mnt/sdcard/photos /mnt/sdcard/videos 2>/dev/null || true
print_status "OK" "Test directories created"

# Build the project
echo -e "\n--- Building Project ---"
cd build

if make video_control test_video_control -j$(nproc); then
    print_status "OK" "Build successful"
else
    print_status "ERROR" "Build failed"
    exit 1
fi

# Verify improvements in source code
echo -e "\n--- Verifying Code Improvements ---"

# Check 1: UDP blocking receive
if grep -q "setsockopt.*SO_RCVTIMEO" ../src/video_control.cpp; then
    print_status "OK" "UDP blocking receive with timeout implemented"
else
    print_status "WARN" "UDP blocking receive may not be implemented"
fi

if ! grep -q "select.*udp_socket_fd_" ../src/video_control.cpp; then
    print_status "OK" "select() removed from UDP receiver (blocking mode)"
else
    print_status "WARN" "select() still present in UDP receiver"
fi

# Check 2: Frame processors only read when recording
if grep -A 5 -B 5 "recording_session_.is_active" ../src/video_control.cpp | grep -q "visible_dds_reader_->read"; then
    print_status "OK" "Visible frame processor only reads when recording"
else
    print_status "WARN" "Visible frame processor may always read frames"
fi

if grep -A 5 -B 5 "recording_session_.is_active" ../src/video_control.cpp | grep -q "infrared_dds_reader_->read"; then
    print_status "OK" "Infrared frame processor only reads when recording"
else
    print_status "WARN" "Infrared frame processor may always read frames"
fi

if grep -q "sleep_for.*100.*milliseconds" ../src/video_control.cpp; then
    print_status "OK" "Frame processors sleep when not recording"
else
    print_status "WARN" "Frame processors may not sleep when idle"
fi

# Check 3: Public methods
if grep -A 10 "public:" ../include/video_control.h | grep -q "capture_photo_dual"; then
    print_status "OK" "capture_photo_dual() is public"
else
    print_status "ERROR" "capture_photo_dual() not found in public section"
fi

if grep -A 10 "public:" ../include/video_control.h | grep -q "start_recording_session"; then
    print_status "OK" "start_recording_session() is public"
else
    print_status "ERROR" "start_recording_session() not found in public section"
fi

if grep -A 10 "public:" ../include/video_control.h | grep -q "stop_recording_session"; then
    print_status "OK" "stop_recording_session() is public"
else
    print_status "ERROR" "stop_recording_session() not found in public section"
fi

# Check 4: Enhanced test coverage
if grep -q "test_public_methods" ../test/test_video_control.cpp; then
    print_status "OK" "Public methods test added"
else
    print_status "ERROR" "Public methods test not found"
fi

# Run the enhanced tests
echo -e "\n--- Running Enhanced Tests ---"

print_status "INFO" "Running video control tests with improvements..."

if timeout 120 ./test_video_control > test_improvements.log 2>&1; then
    print_status "OK" "Enhanced tests completed successfully"
    
    # Analyze test results
    echo -e "\n--- Analyzing Test Results ---"
    
    if grep -q "Public Methods" test_improvements.log; then
        print_status "OK" "Public methods test executed"
        
        if grep -q "capture_photo_dual.*executed successfully" test_improvements.log; then
            print_status "OK" "capture_photo_dual() test passed"
        elif grep -q "capture_photo_dual.*handled.*correctly" test_improvements.log; then
            print_status "OK" "capture_photo_dual() handled edge case correctly"
        else
            print_status "WARN" "capture_photo_dual() test may have issues"
        fi
        
        if grep -q "capture_single_photo.*executed successfully" test_improvements.log; then
            print_status "OK" "capture_single_photo() test passed"
        elif grep -q "capture_single_photo.*handled.*correctly" test_improvements.log; then
            print_status "OK" "capture_single_photo() handled edge case correctly"
        else
            print_status "WARN" "capture_single_photo() test may have issues"
        fi
        
        if grep -q "start_recording_session.*executed successfully" test_improvements.log; then
            print_status "OK" "start_recording_session() test passed"
        elif grep -q "start_recording_session.*handled.*correctly" test_improvements.log; then
            print_status "OK" "start_recording_session() handled edge case correctly"
        else
            print_status "WARN" "start_recording_session() test may have issues"
        fi
        
        if grep -q "stop_recording_session.*executed successfully" test_improvements.log; then
            print_status "OK" "stop_recording_session() test passed"
        elif grep -q "stop_recording_session.*handled.*correctly" test_improvements.log; then
            print_status "OK" "stop_recording_session() handled edge case correctly"
        else
            print_status "WARN" "stop_recording_session() test may have issues"
        fi
        
        if grep -q "Multiple recording cycles.*completed" test_improvements.log; then
            print_status "OK" "Multiple recording cycles test passed"
        else
            print_status "WARN" "Multiple recording cycles test may have issues"
        fi
    else
        print_status "ERROR" "Public methods test not found in output"
    fi
    
    # Check for overall test success
    if grep -q "All dual video tests PASSED" test_improvements.log; then
        print_status "OK" "All enhanced tests passed"
        test_result=0
    else
        print_status "WARN" "Some enhanced tests may have failed"
        test_result=1
    fi
    
else
    print_status "ERROR" "Enhanced tests failed or timed out"
    test_result=1
fi

# Test UDP blocking behavior
echo -e "\n--- Testing UDP Blocking Behavior ---"

print_status "INFO" "Starting service to test UDP blocking receive..."

# Start service in background
./video_control --config ../test/test_dual_video_config.json > service_test.log 2>&1 &
SERVICE_PID=$!

sleep 3

if kill -0 $SERVICE_PID 2>/dev/null; then
    print_status "OK" "Service started successfully"
    
    # Send test UDP message
    print_status "INFO" "Sending test UDP message..."
    python3 -c "
import socket
import struct
import time

sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
try:
    # Send get status command
    packet = struct.pack('BB', 4, 123)  # MAV_CMD_GET_STATUS, seq 123
    sock.sendto(packet, ('127.0.0.1', 14552))
    print('UDP message sent successfully')
except Exception as e:
    print(f'Failed to send UDP message: {e}')
finally:
    sock.close()
" 2>&1
    
    sleep 2
    
    # Check service logs for UDP message processing
    if grep -q "mavlink_messages_received" service_test.log; then
        print_status "OK" "UDP blocking receive working"
    else
        print_status "WARN" "UDP message processing may not be working"
    fi
    
    # Stop service
    kill -TERM $SERVICE_PID 2>/dev/null || true
    sleep 2
    kill -KILL $SERVICE_PID 2>/dev/null || true
    
    print_status "OK" "Service stopped"
else
    print_status "ERROR" "Service failed to start"
    test_result=1
fi

# Final assessment
echo -e "\n--- Final Assessment ---"

if [ $test_result -eq 0 ]; then
    print_status "OK" "🎉 Video Control Service Improvements Verification PASSED!"
    echo ""
    echo "Improvements verified:"
    echo "✓ UDP receiver uses blocking mode with timeout"
    echo "✓ Frame processors only read when recording is active"
    echo "✓ Public methods available for external control"
    echo "✓ Enhanced test coverage for public methods"
    echo "✓ Multiple recording cycles work correctly"
    echo ""
    echo "Benefits:"
    echo "- Reduced CPU usage when not recording"
    echo "- Simplified UDP handling without select()"
    echo "- Better testability with public methods"
    echo "- More robust recording session management"
else
    print_status "ERROR" "❌ Video Control Service Improvements Verification FAILED!"
    echo ""
    echo "Issues detected:"
    echo "- Check test logs: test_improvements.log, service_test.log"
    echo "- Verify public method implementations"
    echo "- Check UDP blocking receive implementation"
    echo "- Ensure frame processor improvements are working"
fi

# Show sample test output
echo -e "\n--- Sample Test Output ---"
if [ -f "test_improvements.log" ]; then
    echo "Last 15 lines of test output:"
    tail -15 test_improvements.log
fi

# Cleanup
rm -f test_improvements.log service_test.log

cd ..
exit $test_result
