#ifndef COMMON_H
#define COMMON_H

#include <memory>
#include <chrono>
#include <vector>
#include <string>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <functional>
#include <fstream>
#include <sstream>
#include <cstdint>

// Linux系统头文件
#include <sys/time.h>
#include <sys/mman.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>
#include <string.h>
#include <syslog.h>
#include <linux/videodev2.h>

// V4L2头文件
#include <linux/videodev2.h>

// FFmpeg头文件 (用于RTSP包获取，不解码)
extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libavutil/imgutils.h>
}

// GStreamer头文件 (用于video converter中的编解码)
#include <gst/gst.h>
#include <gst/app/gstappsrc.h>
#include <gst/app/gstappsink.h>
#include <gst/video/video.h>

#include "dds_video_writer.hpp"
#include "dds_video_reader.hpp"

#include "config/video_service_config.h"

// 时间戳工具
inline uint64_t get_current_us() {
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return tv.tv_sec * 1000000ULL + tv.tv_usec;
}

inline uint64_t get_current_ns() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000000000ULL + ts.tv_nsec;
}

inline std::string v4l2_format_to_gst_string(uint32_t v4l2_format) {
    switch (v4l2_format) {
        // RGB formats
        case V4L2_PIX_FMT_RGB24:
            return "RGB";
        case V4L2_PIX_FMT_BGR24:
            return "BGR";
        case V4L2_PIX_FMT_RGB32:
            return "RGBx";
        case V4L2_PIX_FMT_BGR32:
            return "BGRx";
        case V4L2_PIX_FMT_ARGB32:
            return "ARGB";
        case V4L2_PIX_FMT_ABGR32:
            return "ABGR";

        // YUV formats
        case V4L2_PIX_FMT_YUYV:
            return "YUY2";
        case V4L2_PIX_FMT_UYVY:
            return "UYVY";
        case V4L2_PIX_FMT_YUV420:
            return "I420";
        case V4L2_PIX_FMT_YVU420:
            return "YV12";
        case V4L2_PIX_FMT_NV12:
            return "NV12";
        case V4L2_PIX_FMT_NV21:
            return "NV21";
        case V4L2_PIX_FMT_YUV422P:
            return "Y42B";
        case V4L2_PIX_FMT_YUV411P:
            return "Y41B";
        case V4L2_PIX_FMT_YUV444:
            return "Y444";

        // Grayscale
        case V4L2_PIX_FMT_GREY:
            return "GRAY8";

        // MJPEG (special case - needs different handling)
        case V4L2_PIX_FMT_MJPEG:
            return "MJPG";  // Will need special pipeline handling

        default:
            return "RGB";
    }
}

// 线程安全的帧队列
template<typename T>
class ThreadSafeQueue {
private:
    mutable std::mutex mutex_;
    std::queue<T> queue_;
    std::condition_variable condition_;
    size_t max_size_;

public:
    ThreadSafeQueue(size_t max_size = 10) : max_size_(max_size) {}
    
    void push(T item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.size() >= max_size_) {
            queue_.pop();  // 丢弃最老的帧
        }
        queue_.push(std::move(item));
        condition_.notify_one();
    }
    
    bool try_pop(T& item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.empty()) {
            return false;
        }
        item = std::move(queue_.front());
        queue_.pop();
        return true;
    }
    
    bool wait_and_pop(T& item, int timeout_ms = -1) {
        std::unique_lock<std::mutex> lock(mutex_);
        if (timeout_ms < 0) {
            condition_.wait(lock, [this] { return !queue_.empty(); });
        } else {
            if (!condition_.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                                   [this] { return !queue_.empty(); })) {
                return false;
            }
        }
        item = std::move(queue_.front());
        queue_.pop();
        return true;
    }
    
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.size();
    }
    
    bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.empty();
    }
};

namespace fastdds {
namespace video {
// 视频帧结构
struct Frame {
    uint64_t frame_id;
    uint64_t timestamp;
    uint32_t source_type;
    uint16_t width;
    uint16_t height;
    int32_t  format;
    bool is_keyframe;
    uint32_t data_length;
    std::vector<uint8_t> data;
    bool valid;
    
    // 构造函数
    Frame():
        frame_id(0),
        timestamp(0),
        source_type(0),
        width(0),
        height(0),
        format(0),
        is_keyframe(false),
        data_length(0),
        data(),
        valid(false) {}
    
    Frame(const DDSVideoFrame& dds_frame) {
        frame_id = dds_frame.frame_id();
        timestamp = dds_frame.timestamp();
        source_type = dds_frame.source_type();
        width = dds_frame.width();
        height = dds_frame.height();
        format = dds_frame.format();
        is_keyframe = dds_frame.is_keyframe();
        data_length = dds_frame.data_length();
        data = std::move(dds_frame.data());
        valid = true;
    }

    static void to_dds_frame(const Frame& frame, DDSVideoFrame& dds_frame) {
        dds_frame.frame_id(frame.frame_id);
        dds_frame.timestamp(frame.timestamp);
        dds_frame.source_type(frame.source_type);
        dds_frame.width(frame.width);
        dds_frame.height(frame.height);
        dds_frame.format(frame.format);
        dds_frame.is_keyframe(frame.is_keyframe);
        dds_frame.data_length(frame.data_length);
        dds_frame.data(frame.data);
    }

    // 获取数据大小
    size_t get_data_size() const {
        return data.size();
    }
    
    // 获取数据指针
    const uint8_t* get_data_ptr() const {
        return data.data();
    }
};

class DDSVideoWriter {
private:
    std::unique_ptr<eprosima::fastdds::video::DDSWriter> writer_;

public:
    DDSVideoWriter(const std::string & topic_name, int max_samples = 3) {
        writer_ = std::make_unique<eprosima::fastdds::video::DDSWriter>();
        if (!writer_->init(topic_name, 0, max_samples)) {
            throw std::runtime_error("Failed to initialize DDS Video Writer");
        }
    }
    ~DDSVideoWriter() = default;
    bool write(const Frame& frame) {
        DDSVideoFrame dds_frame;
        Frame::to_dds_frame(frame, dds_frame);
        return writer_->write(const_cast<DDSVideoFrame&>(dds_frame)) == eprosima::fastdds::dds::RETCODE_OK;
    }
};

class DDSVideoReader {
private:
    std::unique_ptr<eprosima::fastdds::video::DDSReader> reader_;
    ThreadSafeQueue<Frame> frame_queue_;

public:
    DDSVideoReader(const std::string & topic_name, int max_samples = 3): frame_queue_(max_samples) {
        reader_ = std::make_unique<eprosima::fastdds::video::DDSReader>();
        if (!reader_->init(topic_name, 0, max_samples, [this](const DDSVideoFrame& dds_frame) {
            Frame f(dds_frame);
            frame_queue_.push(std::move(f));
        })) {
            throw std::runtime_error("Failed to initialize DDS Video Reader");
        }
    }
    ~DDSVideoReader() = default;
    bool read(Frame & frame, int timeout_ms = -1) {
        return frame_queue_.wait_and_pop(frame, timeout_ms);
    }
};
}
}

// CPU使用率监控
class CPUMonitor {
private:
    uint64_t last_total_ = 0;
    uint64_t last_idle_ = 0;
    
public:
    float get_usage() {
        std::ifstream file("/proc/stat");
        std::string line;
        std::getline(file, line);
        
        std::istringstream iss(line);
        std::string cpu;
        uint64_t user, nice, system, idle, iowait, irq, softirq, steal;
        iss >> cpu >> user >> nice >> system >> idle >> iowait >> irq >> softirq >> steal;
        
        uint64_t total = user + nice + system + idle + iowait + irq + softirq + steal;
        uint64_t total_diff = total - last_total_;
        uint64_t idle_diff = idle - last_idle_;
        
        float usage = 0.0f;
        if (total_diff > 0) {
            usage = 100.0f * (1.0f - (float)idle_diff / total_diff);
        }
        
        last_total_ = total;
        last_idle_ = idle;
        
        return usage;
    }
};

// 日志工具
enum LogLevel {
    LEVEL_DEBUG = 0,
    LEVEL_INFO = 1,
    LEVEL_WARN = 2,
    LEVEL_ERROR = 3
};

class Logger {
private:
    inline static LogLevel level_ = LEVEL_INFO;
    inline static std::mutex mutex_;
    inline static bool syslog_initialized_ = false;

    static int log_level_to_syslog_priority(LogLevel level) {
        switch (level) {
            case LEVEL_DEBUG: return LOG_DEBUG;
            case LEVEL_INFO:  return LOG_INFO;
            case LEVEL_WARN:  return LOG_WARNING;
            case LEVEL_ERROR: return LOG_ERR;
            default:          return LOG_INFO;
        }
    }

public:
    static void init(const char* ident = "video_service") {
        std::lock_guard<std::mutex> lock(mutex_);
        if (!syslog_initialized_) {
            openlog(ident, LOG_PID | LOG_CONS, LOG_USER);
            syslog_initialized_ = true;
        }
    }

    static void cleanup() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (syslog_initialized_) {
            closelog();
            syslog_initialized_ = false;
        }
    }

    static void set_level(LogLevel level) { level_ = level; }

    template<typename... Args>
    static void log(LogLevel level, const char* format, Args... args) {
        if (level < level_) return;

        std::lock_guard<std::mutex> lock(mutex_);

        // 确保syslog已初始化（不能调用init()因为会死锁）
        if (!syslog_initialized_) {
            openlog("video_service", LOG_PID | LOG_CONS, LOG_USER);
            syslog_initialized_ = true;
        }

        // 格式化消息
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), format, args...);

        // 发送到syslog
        syslog(log_level_to_syslog_priority(level), "%s", buffer);
    }
};

#define LOG_D(...) Logger::log(LEVEL_DEBUG, __VA_ARGS__)
#define LOG_I(...) Logger::log(LEVEL_INFO, __VA_ARGS__)
#define LOG_W(...) Logger::log(LEVEL_WARN, __VA_ARGS__)
#define LOG_E(...) Logger::log(LEVEL_ERROR, __VA_ARGS__)

// 注意：Logger类使用inline static成员，无需额外定义

#endif // COMMON_H
