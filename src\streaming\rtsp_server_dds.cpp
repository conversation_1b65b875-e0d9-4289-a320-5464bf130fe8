/* RTSP Server with FastDDS Integration for Debugging
 * Based on rtsp_server_pipe.cpp but uses FastDDS as data source
 */

#include <gst/gst.h>
#include <gst/rtsp-server/rtsp-server.h>
#include <gst/app/gstappsrc.h>
#include <thread>
#include <atomic>
#include <chrono>

// FastDDS includes
#include "common.h"
#include "../config/video_service_config.h"

#define DEFAULT_RTSP_PORT "8557"
#define DEFAULT_URI "/stream"

static char *port = (char *) DEFAULT_RTSP_PORT;
static char *server_uri = (char *) DEFAULT_URI;

// Global variables for DDS integration
static std::unique_ptr<DDSVideoReader> dds_reader;
static std::atomic<bool> running{true};
static std::atomic<uint64_t> frame_count{0};

static GOptionEntry entries[] = {
  {"port", 'p', 0, G_OPTION_ARG_STRING, &port,
      "Port to listen on (default: " DEFAULT_RTSP_PORT ")", "PORT"},
  {"uri", 'u', 0, G_OPTION_ARG_STRING, &server_uri,
      "The server URI (default: " DEFAULT_URI ")", "RTSP Server URI"},
  {NULL, 0, 0, G_OPTION_ARG_NONE, NULL, NULL, NULL}
};

// Callback for need-data signal
static void need_data_callback(GstElement *appsrc, guint unused, gpointer user_data) {
    (void)unused;    // Suppress unused parameter warning
    (void)user_data; // Suppress unused parameter warning
    // g_print("=== NEED DATA CALLBACK ===\n");
    
    if (!dds_reader) {
        g_print("DDS reader not initialized\n");
        return;
    }
    
    // Read frame from DDS
    Frame input_frame;
    if (!dds_reader->read(input_frame, 100)) {  // 100ms timeout
        g_print("No DDS data available\n");
        return;
    }
    
    // g_print("Read DDS frame: %dx%d, format=0x%08x, size=%zu bytes\n",
            // input_frame.width, input_frame.height, input_frame.format, input_frame.data.size());
    
    // Create GstBuffer
    GstBuffer* buffer = gst_buffer_new_allocate(NULL, input_frame.data.size(), NULL);
    if (!buffer) {
        g_print("Failed to allocate GstBuffer\n");
        return;
    }
    
    // Fill buffer with data
    GstMapInfo map;
    if (!gst_buffer_map(buffer, &map, GST_MAP_WRITE)) {
        g_print("Failed to map GstBuffer\n");
        gst_buffer_unref(buffer);
        return;
    }
    
    memcpy(map.data, input_frame.data.data(), input_frame.data.size());
    gst_buffer_unmap(buffer, &map);
    
    // Set timestamp
    uint64_t frame_num = frame_count.fetch_add(1);
    uint64_t timestamp_ns = frame_num * GST_SECOND / 30;  // 30fps
    GST_BUFFER_PTS(buffer) = timestamp_ns;
    GST_BUFFER_DTS(buffer) = timestamp_ns;
    GST_BUFFER_DURATION(buffer) = GST_SECOND / 30;
    
    // Push buffer
    GstFlowReturn ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc), buffer);
    g_print("Pushed buffer, result: %d, frame: %lu\n", ret, frame_num);
}

// Callback for enough-data signal
static void enough_data_callback(GstElement *appsrc, gpointer user_data) {
    (void)appsrc;    // Suppress unused parameter warning
    (void)user_data; // Suppress unused parameter warning
    g_print("=== ENOUGH DATA CALLBACK ===\n");
}

// Media factory for creating media instances
static GstRTSPMediaFactory* create_media_factory() {
    GstRTSPMediaFactory *factory = gst_rtsp_media_factory_new();
    
    // Create pipeline with appsrc
    std::string pipeline = "( appsrc name=source is-live=true do-timestamp=false format=time "
                          "caps=\"video/x-raw,format=YUY2,width=640,height=480,framerate=30/1\" "
                          "! queue max-size-buffers=10 max-size-time=0 max-size-bytes=0 leaky=downstream "
                          "! videoscale ! video/x-raw,width=1280,height=720 "
                          "! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 "
                          "! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
    
    g_print("Pipeline: %s\n", pipeline.c_str());
    gst_rtsp_media_factory_set_launch(factory, pipeline.c_str());
    gst_rtsp_media_factory_set_shared(factory, FALSE);  // Each client gets own media
    
    // Set supported protocols
    GstRTSPLowerTrans protocols = GstRTSPLowerTrans(
        GST_RTSP_LOWER_TRANS_UDP | 
        GST_RTSP_LOWER_TRANS_UDP_MCAST | 
        GST_RTSP_LOWER_TRANS_TCP
    );
    gst_rtsp_media_factory_set_protocols(factory, protocols);
    
    return factory;
}

// Signal handler for media-configure
static void media_configure_callback(GstRTSPMediaFactory *factory, GstRTSPMedia *media, gpointer user_data) {
    (void)factory;   // Suppress unused parameter warning
    (void)user_data; // Suppress unused parameter warning
    g_print("=== MEDIA CONFIGURE CALLBACK ===\n");
    
    GstElement *element = gst_rtsp_media_get_element(media);
    GstElement *appsrc = gst_bin_get_by_name_recurse_up(GST_BIN(element), "source");
    
    if (appsrc) {
        g_print("Found appsrc, connecting signals\n");
        
        // Connect signals
        g_signal_connect(appsrc, "need-data", G_CALLBACK(need_data_callback), NULL);
        g_signal_connect(appsrc, "enough-data", G_CALLBACK(enough_data_callback), NULL);
        
        // Configure appsrc properties
        g_object_set(G_OBJECT(appsrc),
                     "is-live", TRUE,
                     "do-timestamp", FALSE,
                     "format", GST_FORMAT_TIME,
                     "max-bytes", 0,
                     "block", FALSE,
                     "min-latency", G_GUINT64_CONSTANT(33333333),  // 33.33ms
                     "max-latency", G_GUINT64_CONSTANT(100000000), // 100ms
                     "stream-type", GST_APP_STREAM_TYPE_STREAM,
                     NULL);
        
        g_print("Configured appsrc properties\n");
        gst_object_unref(appsrc);
    } else {
        g_print("Failed to find appsrc element\n");
    }
    
    gst_object_unref(element);
}

int main(int argc, char *argv[]) {
    GMainLoop *loop;
    GstRTSPServer *server;
    GstRTSPMountPoints *mounts;
    GstRTSPMediaFactory *factory;
    GOptionContext *optctx;
    GError *error = NULL;
    
    optctx = g_option_context_new("<pipeline> - RTSP Server with FastDDS");
    g_option_context_add_main_entries(optctx, entries, NULL);
    g_option_context_add_group(optctx, gst_init_get_option_group());
    if (!g_option_context_parse(optctx, &argc, &argv, &error)) {
        g_printerr("Error parsing options: %s\n", error->message);
        g_option_context_free(optctx);
        g_clear_error(&error);
        return -1;
    }
    g_option_context_free(optctx);
    
    // Initialize DDS reader
    g_print("Initializing DDS reader for topic: Video_Frames\n");
    dds_reader = std::make_unique<DDSVideoReader>("Video_Frames");
    if (!dds_reader) {
        g_printerr("Failed to create DDS reader\n");
        return -1;
    }
    
    // Wait for first frame to get format info
    g_print("Waiting for first DDS frame...\n");
    Frame first_frame;
    while (!dds_reader->read(first_frame, 1000)) {
        g_print("Still waiting for DDS data...\n");
    }
    g_print("First frame received: %dx%d, format=0x%08x\n", 
            first_frame.width, first_frame.height, first_frame.format);
    
    loop = g_main_loop_new(NULL, FALSE);
    
    // Create RTSP server
    server = gst_rtsp_server_new();
    g_object_set(server, "service", port, NULL);
    
    // Get mount points
    mounts = gst_rtsp_server_get_mount_points(server);
    
    // Create media factory
    factory = create_media_factory();
    
    // Connect media-configure signal
    g_signal_connect(factory, "media-configure", G_CALLBACK(media_configure_callback), NULL);
    
    // Add factory to mount points
    gst_rtsp_mount_points_add_factory(mounts, server_uri, factory);
    g_object_unref(mounts);
    
    // Start server
    if (gst_rtsp_server_attach(server, NULL) == 0) {
        g_printerr("Failed to attach RTSP server\n");
        return -1;
    }
    
    g_print("RTSP server ready at rtsp://127.0.0.1:%s%s\n", port, server_uri);
    g_print("Press Ctrl+C to stop\n");
    
    // Run main loop
    g_main_loop_run(loop);
    
    // Cleanup
    running = false;
    g_main_loop_unref(loop);
    g_object_unref(server);
    dds_reader.reset();
    
    return 0;
}
