# Quick Integration Guide: DMA Distribution Compatibility

## Overview

This guide shows how to quickly integrate the improved DMA distribution compatibility with your existing video transport code.

## Basic Usage

### 1. Publisher Setup

```cpp
#include "transport/video_transport_interface_v2.h"

// Create DMA distribution publisher
TransportConfig config(TransportConfig::Type::DMA_DISTRIBUTION, "/tmp/video_stream");
config.buffer_size = 4 * 1024 * 1024;  // 4MB buffers
config.ring_buffer_size = 8;            // 8 buffer ring

auto publisher = VideoTransportFactory::create_publisher(config);
```

### 2. Publishing Frames

```cpp
// Standard buffer management - same API as other transport types
BufferHandle handle;
if (publisher->get_buffer_for_frame(handle) == BufferResult::SUCCESS) {
    // Fill buffer with your frame data
    memcpy(handle.data, your_frame_data, frame_size);
    handle.used_size = frame_size;
    
    // Set frame metadata
    handle.metadata.width = 1920;
    handle.metadata.height = 1080;
    handle.metadata.format = V4L2_PIX_FMT_YUV420;
    handle.metadata.timestamp = get_current_us();
    
    // Publish via DMA distribution (zero-copy)
    publisher->publish_frame(handle);
}
```

### 3. Subscriber Setup

```cpp
// Create DMA distribution subscriber
TransportConfig config(TransportConfig::Type::DMA_DISTRIBUTION, "/tmp/video_stream");
auto subscriber = VideoTransportFactory::create_subscriber(config);
```

### 4. Receiving Frames

```cpp
// Synchronous frame reception
BufferHandle handle;
if (subscriber->receive_frame_buffer(handle, 1000) == BufferResult::SUCCESS) {
    // Process frame data (zero-copy access)
    process_frame(handle.data, handle.used_size, handle.metadata);
    
    // Return buffer when done (required for proper cleanup)
    subscriber->return_frame_buffer(handle);
}
```

## V4L2 Integration

### Direct V4L2 Frame Publishing

```cpp
// Capture V4L2 frame
V4L2Frame v4l2_frame;
if (v4l2_device->capture_frame(v4l2_frame)) {
    // Method 1: Zero-copy (if V4L2 uses DMA buffers)
    BufferHandle v4l2_handle;
    if (V4L2BufferAdapter::wrap_v4l2_buffer(v4l2_frame, v4l2_handle) == BufferResult::SUCCESS) {
        publisher->publish_frame(v4l2_handle);
    }
    
    // Method 2: Copy-based (for MMAP V4L2 buffers)
    BufferHandle dma_handle;
    if (publisher->get_buffer_for_frame(dma_handle) == BufferResult::SUCCESS) {
        V4L2BufferAdapter::copy_to_buffer(v4l2_frame, dma_handle);
        publisher->publish_frame(dma_handle);
    }
    
    v4l2_device->release_frame(v4l2_frame);
}
```

## Error Handling

```cpp
// Comprehensive error handling
BufferResult result = publisher->get_buffer_for_frame(handle);
switch (result) {
    case BufferResult::SUCCESS:
        // Proceed with frame processing
        break;
    case BufferResult::BUFFER_NOT_AVAILABLE:
        // No free buffers - maybe slow down or skip frame
        break;
    case BufferResult::TRANSPORT_ERROR:
        // Transport issue - check connection
        break;
    case BufferResult::TIMEOUT:
        // Timeout - network or system issue
        break;
    default:
        // Handle other errors
        break;
}
```

## Performance Monitoring

```cpp
// Get transport statistics
auto stats = publisher->get_stats();
std::cout << "Frames sent: " << stats.frames_sent.load() << std::endl;
std::cout << "Bytes sent: " << stats.bytes_sent.load() << std::endl;
std::cout << "Failed operations: " << stats.failed_operations.load() << std::endl;

// Reset statistics
publisher->reset_stats();

// Get status information
std::cout << "Publisher status: " << publisher->get_status() << std::endl;
```

## Multi-Consumer Pattern

```cpp
// Multiple consumers can connect to the same DMA distribution socket
void setup_multi_consumer() {
    const std::string socket_path = "/tmp/shared_video";
    
    // AI processing consumer
    std::thread ai_thread([socket_path]() {
        auto subscriber = VideoTransportFactory::create_subscriber(
            TransportConfig(TransportConfig::Type::DMA_DISTRIBUTION, socket_path)
        );
        
        BufferHandle handle;
        while (subscriber->receive_frame_buffer(handle) == BufferResult::SUCCESS) {
            run_ai_inference(handle.data, handle.metadata);
            subscriber->return_frame_buffer(handle);
        }
    });
    
    // Recording consumer
    std::thread record_thread([socket_path]() {
        auto subscriber = VideoTransportFactory::create_subscriber(
            TransportConfig(TransportConfig::Type::DMA_DISTRIBUTION, socket_path)
        );
        
        BufferHandle handle;
        while (subscriber->receive_frame_buffer(handle) == BufferResult::SUCCESS) {
            save_to_file(handle.data, handle.metadata);
            subscriber->return_frame_buffer(handle);
        }
    });
    
    ai_thread.join();
    record_thread.join();
}
```

## Configuration Options

```cpp
TransportConfig config(TransportConfig::Type::DMA_DISTRIBUTION, "/tmp/video");

// Buffer configuration
config.buffer_size = 8 * 1024 * 1024;      // 8MB per buffer
config.ring_buffer_size = 16;              // 16 buffer ring

// Timeout configuration
config.timeout_ms = 2000;                  // 2 second timeout

// Create with configuration
auto publisher = VideoTransportFactory::create_publisher(config);
```

## Comparison with Other Transports

| Feature | FastDDS | DMA_SHARED | DMA_DISTRIBUTION |
|---------|---------|------------|------------------|
| **Memory Copies** | 2-3 | 0-1 | 0 |
| **Cross-Process** | Full frame | Metadata only | Metadata only |
| **Latency** | Medium | Low | Lowest |
| **Setup Complexity** | Medium | Low | Low |
| **Network Support** | ✅ | ❌ | ❌ |
| **Zero-Copy** | ❌ | ✅ | ✅ |
| **Multi-Consumer** | ✅ | Limited | ✅ |

## Best Practices

### 1. Buffer Management
```cpp
// Always check return values
if (publisher->get_buffer_for_frame(handle) == BufferResult::SUCCESS) {
    // Use buffer...
    publisher->publish_frame(handle);
}

// Always return buffers on consumer side
if (subscriber->receive_frame_buffer(handle) == BufferResult::SUCCESS) {
    // Process buffer...
    subscriber->return_frame_buffer(handle);  // Required!
}
```

### 2. Resource Cleanup
```cpp
// RAII - automatic cleanup when objects go out of scope
{
    auto publisher = VideoTransportFactory::create_publisher(config);
    // Use publisher...
}  // Automatic cleanup here
```

### 3. Error Recovery
```cpp
// Handle transport errors gracefully
if (result == BufferResult::TRANSPORT_ERROR) {
    // Recreate transport
    publisher = VideoTransportFactory::create_publisher(config);
}
```

## Migration from Direct io_uring

```cpp
// OLD: Direct io_uring usage
IoUringProducer producer(BufferType::DMA, socket_path, buffer_size, ring_size);
auto* slot = producer.acquire_buffer();
// Manual buffer management...
producer.broadcast_buffer(slot);

// NEW: Transport interface
auto publisher = VideoTransportFactory::create_publisher(
    TransportConfig(TransportConfig::Type::DMA_DISTRIBUTION, socket_path)
);
BufferHandle handle;
if (publisher->get_buffer_for_frame(handle) == BufferResult::SUCCESS) {
    // Standard buffer operations...
    publisher->publish_frame(handle);
}
```

## Summary

The DMA distribution compatibility improvements provide:

- **Same API** as other transport types
- **Zero-copy performance** via io_uring + Unix sockets  
- **Simple integration** with existing video transport code
- **Thread-safe operations** with proper resource management
- **V4L2 compatibility** for seamless capture integration

Start with the basic publisher/subscriber examples above and expand based on your specific use case!