# WebRTC链接问题修复

## 问题描述

编译`cloud_streamer_main.cpp`时出现链接错误：
```
undefined reference to `gst_webrtc_session_description_free`
```

这个错误表明链接器找不到GStreamer WebRTC相关函数的定义。

## 根本原因

1. **缺少WebRTC库链接**：构建系统中缺少`gstreamer-webrtc-1.0`库的链接配置
2. **缺少SDP库链接**：`gst_webrtc_session_description_free`函数还依赖于`gstreamer-sdp-1.0`库
3. **库依赖不完整**：WebRTC功能需要多个GStreamer库的支持

## 函数使用位置

在`include/cloud_streamer.h`的第443行：
```cpp
static void on_offer_created(GstPromise* promise, gpointer user_data) {
    // ... 其他代码 ...
    
    // 发送offer到信令服务器
    if (self->webrtc_signaling_) {
        gchar* sdp_text = gst_sdp_message_as_text(offer->sdp);
        self->webrtc_signaling_->send_offer(sdp_text);
        g_free(sdp_text);
    }

    gst_webrtc_session_description_free(offer);  // 这里导致链接错误
    gst_promise_unref(promise);
}
```

## 修复方案

### 1. 修复CMakeLists.txt中的GStreamer库配置

**修改前：**
```cmake
pkg_check_modules(GSTREAMER REQUIRED
    gstreamer-1.0>=1.16
    gstreamer-app-1.0
    gstreamer-video-1.0
)
```

**修改后：**
```cmake
pkg_check_modules(GSTREAMER REQUIRED
    gstreamer-1.0>=1.16
    gstreamer-app-1.0
    gstreamer-video-1.0
    gstreamer-webrtc-1.0
    gstreamer-sdp-1.0
)
```

### 2. 修复Makefile中的GStreamer库配置

**修改前：**
```makefile
GSTREAMER_CFLAGS = $(shell $(PKG_CONFIG) --cflags gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0)
GSTREAMER_LIBS = $(shell $(PKG_CONFIG) --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0)
```

**修改后：**
```makefile
GSTREAMER_CFLAGS = $(shell $(PKG_CONFIG) --cflags gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-webrtc-1.0 gstreamer-sdp-1.0)
GSTREAMER_LIBS = $(shell $(PKG_CONFIG) --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-webrtc-1.0 gstreamer-sdp-1.0)
```

## 技术细节

### WebRTC相关库依赖
- **gstreamer-webrtc-1.0**: 提供WebRTC核心功能，包括`GstWebRTCSessionDescription`类型和相关函数
- **gstreamer-sdp-1.0**: 提供SDP (Session Description Protocol) 支持，WebRTC session description依赖此库
- **gstreamer-app-1.0**: 提供appsrc/appsink功能，用于数据注入和提取
- **gstreamer-video-1.0**: 提供视频处理相关功能

### 函数依赖关系
```cpp
// 这些函数需要相应的库支持：
gst_webrtc_session_description_new()     // gstreamer-webrtc-1.0
gst_webrtc_session_description_free()    // gstreamer-webrtc-1.0 + gstreamer-sdp-1.0
gst_sdp_message_as_text()               // gstreamer-sdp-1.0
gst_sdp_message_new()                   // gstreamer-sdp-1.0
```

### 头文件包含
在`include/cloud_streamer.h`中正确包含了必要的头文件：
```cpp
#include <gst/sdp/sdp.h>        // SDP支持
#include <gst/webrtc/webrtc.h>  // WebRTC支持
```

## 验证方法

创建了测试程序`test_webrtc_link.cpp`来验证修复：
```cpp
int main() {
    gst_init(nullptr, nullptr);
    
    // 测试SDP消息创建
    GstSDPMessage* sdp_msg = nullptr;
    gst_sdp_message_new(&sdp_msg);
    
    // 测试WebRTC session description
    GstWebRTCSessionDescription* desc = gst_webrtc_session_description_new(
        GST_WEBRTC_SDP_TYPE_OFFER, sdp_msg);
    
    // 测试释放函数（之前导致链接错误的函数）
    gst_webrtc_session_description_free(desc);
    
    gst_deinit();
    return 0;
}
```

## 系统要求

确保系统安装了必要的GStreamer开发包：
```bash
# Ubuntu/Debian
sudo apt-get install libgstreamer1.0-dev \
                     libgstreamer-plugins-base1.0-dev \
                     libgstreamer-plugins-good1.0-dev \
                     libgstreamer-plugins-bad1.0-dev

# 特别需要WebRTC插件
sudo apt-get install gstreamer1.0-plugins-bad
```

## 总结

WebRTC链接错误是由于缺少必要的GStreamer库链接配置造成的。通过在CMakeLists.txt和Makefile中添加`gstreamer-webrtc-1.0`和`gstreamer-sdp-1.0`库的链接配置，解决了`gst_webrtc_session_description_free`等函数的链接问题。

这个修复确保了云流媒体服务的WebRTC功能能够正常编译和运行。
