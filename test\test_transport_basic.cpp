#include "video_transport_interface.h"
#include "../include/config/video_service_config.h"
#include <iostream>
#include <cassert>

using namespace video_transport;

int main() {
    std::cout << \"Video Transport Abstraction Test\" << std::endl;
    std::cout << \"================================\" << std::endl;
    
    try {
        // 测试1: 工厂创建
        std::cout << \"\nTest 1: Factory Creation\" << std::endl;
        auto supported_types = VideoTransportFactory::get_supported_types();
        std::cout << \"Supported transport types: \" << supported_types.size() << std::endl;
        
        for (auto type : supported_types) {
            std::cout << \"  - \" << VideoTransportFactory::transport_type_to_string(type) << std::endl;
        }
        
        // 测试2: 配置加载
        std::cout << \"\nTest 2: Configuration Loading\" << std::endl;
        
        // 创建默认配置
        auto fastdds_config = TransportConfigLoader::create_default_fastdds_config(\"test_topic\");
        std::cout << \"Default FastDDS config created\" << std::endl;
        
        // 验证配置
        std::string error_msg;
        if (TransportConfigLoader::validate_config(fastdds_config, error_msg)) {
            std::cout << \"FastDDS config validation: PASSED\" << std::endl;
        } else {
            std::cout << \"FastDDS config validation: FAILED - \" << error_msg << std::endl;
            return 1;
        }
        
        // 转换配置到JSON
        std::string json_str = TransportConfigLoader::config_to_json_string(fastdds_config);
        std::cout << \"FastDDS config JSON:\n\" << json_str << std::endl;
        
        // 从JSON加载配置
        TransportConfig loaded_config;
        if (TransportConfigLoader::load_from_json_string(json_str, loaded_config)) {
            std::cout << \"Config reload from JSON: PASSED\" << std::endl;
        } else {
            std::cout << \"Config reload from JSON: FAILED\" << std::endl;
            return 1;
        }
        
        // 测试3: 创建发布者和订阅者（不初始化，只测试创建）
        std::cout << \"\nTest 3: Publisher/Subscriber Creation\" << std::endl;
        
        try {
            auto publisher = VideoTransportFactory::create_publisher(TransportType::FASTDDS);
            std::cout << \"FastDDS Publisher created: PASSED\" << std::endl;
        } catch (const std::exception& e) {
            std::cout << \"FastDDS Publisher creation: FAILED - \" << e.what() << std::endl;
        }
        
        try {
            auto subscriber = VideoTransportFactory::create_subscriber(TransportType::FASTDDS);
            std::cout << \"FastDDS Subscriber created: PASSED\" << std::endl;
        } catch (const std::exception& e) {
            std::cout << \"FastDDS Subscriber creation: FAILED - \" << e.what() << std::endl;
        }
        
#ifdef HAVE_LIBURING
        try {
            auto dma_config = TransportConfigLoader::create_default_dma_config(\"/tmp/test.sock\");
            auto dma_publisher = VideoTransportFactory::create_publisher(TransportType::DMA);
            std::cout << \"DMA Publisher created: PASSED\" << std::endl;
        } catch (const std::exception& e) {
            std::cout << \"DMA Publisher creation: FAILED - \" << e.what() << std::endl;
        }
#else
        std::cout << \"DMA transport not available (liburing not found)\" << std::endl;
#endif
        
        // 测试4: 视频服务配置
        std::cout << \"\nTest 4: Video Service Configuration\" << std::endl;
        
        auto service_config = VideoServiceConfigLoader::create_default_config();
        std::cout << \"Default service config created\" << std::endl;
        std::cout << \"Publishers: \" << service_config.publishers.size() << std::endl;
        std::cout << \"Subscribers: \" << service_config.subscribers.size() << std::endl;
        
        if (VideoServiceConfigLoader::validate_config(service_config, error_msg)) {
            std::cout << \"Service config validation: PASSED\" << std::endl;
        } else {
            std::cout << \"Service config validation: FAILED - \" << error_msg << std::endl;
            return 1;
        }
        
        // 保存和加载配置文件
        std::string config_file = \"test_service_config.json\";
        if (VideoServiceConfigLoader::save_to_file(config_file, service_config)) {
            std::cout << \"Service config save: PASSED\" << std::endl;
        } else {
            std::cout << \"Service config save: FAILED\" << std::endl;
            return 1;
        }
        
        VideoServiceConfig loaded_service_config;
        if (VideoServiceConfigLoader::load_from_file(config_file, loaded_service_config)) {
            std::cout << \"Service config load: PASSED\" << std::endl;
        } else {
            std::cout << \"Service config load: FAILED\" << std::endl;
            return 1;
        }
        
        std::cout << \"\n=== All Tests PASSED ===\" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << \"Test failed with exception: \" << e.what() << std::endl;
        return 1;
    }
}