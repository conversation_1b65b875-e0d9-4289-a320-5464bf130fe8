/**
 * 基础库测试程序 - 最小化版本
 * 用于验证交叉编译环境中的基本库是否正常工作
 * Basic Library Test Program for Cross-compiled Environment
 */

#include <iostream>
#include <string>

// 只测试最基本的系统库
#include <unistd.h>
#include <sys/time.h>

// 逐步测试其他库 - 用宏控制
#ifdef TEST_FFMPEG
extern "C" {
#include <libavutil/avutil.h>
}
#endif

#ifdef TEST_GSTREAMER
#include <gst/gst.h>
#endif

#ifdef TEST_JSONCPP
#include <json/json.h>
#endif

#ifdef TEST_FASTDDS
#include <fastdds/dds/domain/DomainParticipantFactory.hpp>
#endif

#ifdef TEST_OPENCV
#include <opencv2/opencv.hpp>
#endif

void test_basic_system() {
    std::cout << "=== 测试基本系统功能 ===" << std::endl;

    // 测试基本输出
    std::cout << "✓ 标准输出工作正常" << std::endl;

    // 测试字符串
    std::string test_str = "Hello World";
    std::cout << "✓ 字符串工作正常: " << test_str << std::endl;
}

void test_system_calls() {
    std::cout << "\n=== 测试系统调用 ===" << std::endl;
    
    // 测试时间函数
    struct timeval tv;
    if (gettimeofday(&tv, nullptr) == 0) {
        std::cout << "✓ gettimeofday() 工作正常" << std::endl;
    } else {
        std::cout << "✗ gettimeofday() 失败" << std::endl;
    }
    
    // 测试进程信息
    pid_t pid = getpid();
    std::cout << "✓ getpid() 返回: " << pid << std::endl;
}

void test_ffmpeg() {
#ifdef TEST_FFMPEG
    std::cout << "\n=== 测试 FFmpeg ===" << std::endl;

    try {
        // 获取 FFmpeg 版本
        unsigned int version = avutil_version();
        std::cout << "✓ FFmpeg libavutil 版本: " <<
                     AV_VERSION_MAJOR(version) << "." <<
                     AV_VERSION_MINOR(version) << "." <<
                     AV_VERSION_MICRO(version) << std::endl;

    } catch (const std::exception& e) {
        std::cout << "✗ FFmpeg 测试失败: " << e.what() << std::endl;
    } catch (...) {
        std::cout << "✗ FFmpeg 测试失败: 未知错误" << std::endl;
    }
#else
    std::cout << "\n=== FFmpeg 测试跳过 ===" << std::endl;
#endif
}

void test_gstreamer() {
#ifdef TEST_GSTREAMER
    std::cout << "\n=== 测试 GStreamer ===" << std::endl;

    try {
        // 初始化 GStreamer
        gst_init(nullptr, nullptr);
        std::cout << "✓ GStreamer 初始化成功" << std::endl;

        // 获取版本信息
        guint major, minor, micro, nano;
        gst_version(&major, &minor, &micro, &nano);
        std::cout << "✓ GStreamer 版本: " << major << "." << minor << "." << micro << std::endl;

    } catch (const std::exception& e) {
        std::cout << "✗ GStreamer 测试失败: " << e.what() << std::endl;
    } catch (...) {
        std::cout << "✗ GStreamer 测试失败: 未知错误" << std::endl;
    }
#else
    std::cout << "\n=== GStreamer 测试跳过 ===" << std::endl;
#endif
}

void test_jsoncpp() {
#ifdef TEST_JSONCPP
    std::cout << "\n=== 测试 JsonCpp ===" << std::endl;

    try {
        Json::Value root;
        root["test"] = "value";
        root["number"] = 42;

        Json::StreamWriterBuilder builder;
        std::string json_string = Json::writeString(builder, root);
        std::cout << "✓ JsonCpp 工作正常: " << json_string << std::endl;

    } catch (const std::exception& e) {
        std::cout << "✗ JsonCpp 测试失败: " << e.what() << std::endl;
    }
#else
    std::cout << "\n=== JsonCpp 测试跳过 ===" << std::endl;
#endif
}

void test_fastdds() {
#ifdef TEST_FASTDDS
    std::cout << "\n=== 测试 FastDDS ===" << std::endl;

    try {
        // 测试 DomainParticipantFactory 访问
        auto factory = eprosima::fastdds::dds::DomainParticipantFactory::get_instance();
        if (factory) {
            std::cout << "✓ FastDDS DomainParticipantFactory 获取成功" << std::endl;
        } else {
            std::cout << "✗ FastDDS DomainParticipantFactory 获取失败" << std::endl;
        }

    } catch (const std::exception& e) {
        std::cout << "✗ FastDDS 测试失败: " << e.what() << std::endl;
    } catch (...) {
        std::cout << "✗ FastDDS 测试失败: 未知错误" << std::endl;
    }
#else
    std::cout << "\n=== FastDDS 测试跳过 ===" << std::endl;
#endif
}

void test_opencv() {
#ifdef TEST_OPENCV
    std::cout << "\n=== 测试 OpenCV ===" << std::endl;

    try {
        // 获取OpenCV版本
        std::cout << "✓ OpenCV 版本: " << CV_VERSION << std::endl;

        // 测试创建简单的Mat对象
        cv::Mat test_mat = cv::Mat::zeros(100, 100, CV_8UC3);
        if (!test_mat.empty()) {
            std::cout << "✓ OpenCV Mat 创建成功: " << test_mat.size() << std::endl;
        } else {
            std::cout << "✗ OpenCV Mat 创建失败" << std::endl;
        }

        // 测试基本操作
        cv::Mat small_mat = cv::Mat::ones(3, 3, CV_8UC1);
        int sum = cv::sum(small_mat)[0];
        std::cout << "✓ OpenCV 基本操作正常: sum=" << sum << std::endl;

    } catch (const cv::Exception& e) {
        std::cout << "✗ OpenCV 测试失败: " << e.what() << std::endl;
    } catch (const std::exception& e) {
        std::cout << "✗ OpenCV 测试失败: " << e.what() << std::endl;
    } catch (...) {
        std::cout << "✗ OpenCV 测试失败: 未知错误" << std::endl;
    }
#else
    std::cout << "\n=== OpenCV 测试跳过 ===" << std::endl;
#endif
}

int main(int argc, char* argv[]) {
    std::cout << "基础库测试程序 - 最小化版本" << std::endl;
    std::cout << "==============================" << std::endl;

    // 显示系统信息
    std::cout << "程序参数数量: " << argc << std::endl;
    if (argc > 0) {
        std::cout << "程序路径: " << argv[0] << std::endl;
    }

    try {
        // 按顺序测试各个库
        test_basic_system();
        test_system_calls();
        test_ffmpeg();
        test_gstreamer();
        test_jsoncpp();
        test_fastdds();
        test_opencv();

        std::cout << "\n=== 测试完成 ===" << std::endl;
        std::cout << "如果看到这条消息，说明测试的库都工作正常" << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cout << "\n✗ 程序异常退出: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "\n✗ 程序异常退出: 未知错误" << std::endl;
        return 2;
    }
}
