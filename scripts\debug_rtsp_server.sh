#!/bin/bash

# RTSP Server 调试脚本
# 用于诊断RTSP服务器的feed_data回调问题

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
LOG_DIR="$PROJECT_ROOT/logs"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志文件
DEBUG_LOG="$LOG_DIR/rtsp_debug.log"
GSTREAMER_LOG="$LOG_DIR/gstreamer_debug.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$DEBUG_LOG"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$DEBUG_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$DEBUG_LOG"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查可执行文件
    if [[ ! -f "$BUILD_DIR/rtsp_server_main" ]]; then
        log_error "RTSP服务器可执行文件不存在: $BUILD_DIR/rtsp_server_main"
        return 1
    fi
    
    # 检查GStreamer工具
    if ! command -v gst-launch-1.0 &> /dev/null; then
        log_warn "gst-launch-1.0 未找到，某些测试可能无法运行"
    fi
    
    if ! command -v gst-inspect-1.0 &> /dev/null; then
        log_warn "gst-inspect-1.0 未找到，某些检查可能无法运行"
    fi
    
    return 0
}

# 检查GStreamer插件
check_gstreamer_plugins() {
    log_info "检查GStreamer插件..."
    
    local plugins=("rtspserver" "appsrc" "appsink" "videoconvert" "videoscale" "x264enc")
    
    for plugin in "${plugins[@]}"; do
        if gst-inspect-1.0 "$plugin" &> /dev/null; then
            log_info "✓ $plugin 插件可用"
        else
            log_error "✗ $plugin 插件不可用"
        fi
    done
    
    # 检查硬件编码器
    if gst-inspect-1.0 mpph264enc &> /dev/null; then
        log_info "✓ mpph264enc 硬件编码器可用"
    else
        log_warn "✗ mpph264enc 硬件编码器不可用"
    fi
    
    if gst-inspect-1.0 mpph265enc &> /dev/null; then
        log_info "✓ mpph265enc 硬件编码器可用"
    else
        log_warn "✗ mpph265enc 硬件编码器不可用"
    fi
}

# 启动调试模式的RTSP服务器
start_debug_server() {
    log_info "启动调试模式的RTSP服务器..."
    
    # 清空之前的日志
    > "$DEBUG_LOG"
    > "$GSTREAMER_LOG"
    
    # 设置GStreamer调试环境变量
    export GST_DEBUG="*:4,rtspserver:5,rtspmedia:5,rtspstream:5,appsrc:5"
    export GST_DEBUG_FILE="$GSTREAMER_LOG"
    export GST_DEBUG_NO_COLOR=1
    
    # 启动服务器，使用高调试级别
    local cmd=(
        "$BUILD_DIR/rtsp_server_main"
        "--topic" "Video_Frames"
        "--port" "8554"
        "--mount" "/debug"
        "--gst-debug" "5"
        "--stats-interval" "5"
    )
    
    log_info "执行命令: ${cmd[*]}"
    log_info "GStreamer调试日志: $GSTREAMER_LOG"
    log_info "应用调试日志: $DEBUG_LOG"
    
    # 在后台启动服务器
    "${cmd[@]}" >> "$DEBUG_LOG" 2>&1 &
    local server_pid=$!
    
    echo "$server_pid" > "$LOG_DIR/debug_server.pid"
    
    log_info "RTSP调试服务器已启动 (PID: $server_pid)"
    log_info "RTSP URL: rtsp://localhost:8554/debug"
    
    return 0
}

# 停止调试服务器
stop_debug_server() {
    if [[ -f "$LOG_DIR/debug_server.pid" ]]; then
        local pid=$(cat "$LOG_DIR/debug_server.pid")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "停止调试服务器 (PID: $pid)"
            kill "$pid"
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                log_warn "强制停止调试服务器"
                kill -9 "$pid"
            fi
        fi
        rm -f "$LOG_DIR/debug_server.pid"
    fi
}

# 测试RTSP连接
test_rtsp_connection() {
    log_info "测试RTSP连接..."
    
    local rtsp_url="rtsp://localhost:8554/debug"
    
    # 等待服务器启动
    sleep 3
    
    # 使用gst-launch测试连接
    if command -v gst-launch-1.0 &> /dev/null; then
        log_info "使用gst-launch-1.0测试RTSP连接..."
        
        timeout 10s gst-launch-1.0 -v \
            rtspsrc location="$rtsp_url" ! \
            fakesink dump=true \
            >> "$DEBUG_LOG" 2>&1 &
        
        local test_pid=$!
        sleep 5
        
        if kill -0 "$test_pid" 2>/dev/null; then
            kill "$test_pid" 2>/dev/null
            log_info "RTSP连接测试完成"
        else
            log_warn "RTSP连接测试可能失败"
        fi
    else
        log_warn "gst-launch-1.0不可用，跳过连接测试"
    fi
}

# 分析日志
analyze_logs() {
    log_info "分析调试日志..."
    
    # 检查关键回调是否被调用
    if grep -q "MEDIA CONFIGURE CALLBACK TRIGGERED" "$DEBUG_LOG"; then
        log_info "✓ media_configure_callback 被调用"
    else
        log_error "✗ media_configure_callback 未被调用"
    fi
    
    if grep -q "NEED DATA CALLBACK TRIGGERED" "$DEBUG_LOG"; then
        log_info "✓ need_data_callback 被调用"
    else
        log_error "✗ need_data_callback 未被调用 - 这是主要问题！"
    fi
    
    if grep -q "FEED DATA CALLED" "$DEBUG_LOG"; then
        log_info "✓ feed_data 被调用"
    else
        log_error "✗ feed_data 未被调用"
    fi
    
    # 检查客户端连接
    if grep -q "CLIENT CONNECTED" "$DEBUG_LOG"; then
        log_info "✓ 检测到客户端连接"
    else
        log_warn "✗ 未检测到客户端连接"
    fi
    
    # 检查DDS数据
    if grep -q "First frame received" "$DEBUG_LOG"; then
        log_info "✓ 接收到DDS第一帧数据"
    else
        log_error "✗ 未接收到DDS第一帧数据"
    fi
    
    # 检查Pipeline创建
    if grep -q "Pipeline:" "$DEBUG_LOG"; then
        log_info "✓ Pipeline描述已创建"
        grep "Pipeline:" "$DEBUG_LOG" | tail -1
    else
        log_error "✗ Pipeline描述未创建"
    fi
    
    # 检查appsrc元素
    if grep -q "Found appsrc element" "$DEBUG_LOG"; then
        log_info "✓ 找到appsrc元素"
    else
        log_error "✗ 未找到appsrc元素"
    fi
}

# 显示使用说明
show_usage() {
    cat << EOF
RTSP服务器调试脚本

用法: $0 [命令]

命令:
    start       启动调试模式的RTSP服务器
    stop        停止调试服务器
    test        测试RTSP连接
    analyze     分析调试日志
    full        执行完整的调试流程
    check       检查依赖和插件
    logs        显示实时日志
    help        显示此帮助信息

示例:
    $0 full     # 执行完整调试流程
    $0 start    # 仅启动调试服务器
    $0 analyze  # 分析现有日志
EOF
}

# 显示实时日志
show_logs() {
    log_info "显示实时日志 (Ctrl+C退出)..."
    tail -f "$DEBUG_LOG" "$GSTREAMER_LOG" 2>/dev/null
}

# 完整调试流程
full_debug() {
    log_info "开始完整调试流程..."
    
    # 停止可能运行的服务器
    stop_debug_server
    
    # 检查依赖
    if ! check_dependencies; then
        return 1
    fi
    
    # 检查插件
    check_gstreamer_plugins
    
    # 启动调试服务器
    start_debug_server
    
    # 测试连接
    test_rtsp_connection
    
    # 等待一段时间收集日志
    log_info "等待10秒收集调试信息..."
    sleep 10
    
    # 分析日志
    analyze_logs
    
    # 停止服务器
    stop_debug_server
    
    log_info "调试流程完成，请查看日志文件："
    log_info "  应用日志: $DEBUG_LOG"
    log_info "  GStreamer日志: $GSTREAMER_LOG"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_debug_server
            ;;
        stop)
            stop_debug_server
            ;;
        test)
            test_rtsp_connection
            ;;
        analyze)
            analyze_logs
            ;;
        check)
            check_dependencies
            check_gstreamer_plugins
            ;;
        logs)
            show_logs
            ;;
        full)
            full_debug
            ;;
        help|*)
            show_usage
            ;;
    esac
}

# 信号处理
trap 'stop_debug_server; exit 0' INT TERM

# 执行主函数
main "$@"
