#include "dds_video_reader.hpp"
#include <fastdds/rtps/transport/shared_mem/SharedMemTransportDescriptor.hpp>
#include <fastdds/rtps/transport/UDPv4TransportDescriptor.hpp>
#include <stdexcept>
#include <thread>

using namespace eprosima::fastdds;
using namespace eprosima::fastdds::video;
using namespace eprosima::fastdds::dds;
using namespace eprosima::fastdds::rtps;

bool DDSReader::init(const std::string& topic_name, int domain_id, int 
    max_samples, std::function<void(const DDSVideoFrame&)> callback) {
    max_samples_ = max_samples;
    callback_ = callback;
    try {

        // printf("Start init DDS reader: %s\n", topic_name.c_str());
        // 创建参与者
        DomainParticipantQos pqos = PARTICIPANT_QOS_DEFAULT;
        pqos.name("VideoService_Reader");
        std::shared_ptr<SharedMemTransportDescriptor> shm_transport_ =
                std::make_shared<SharedMemTransportDescriptor>();
        // 优化共享内存配置以减少延时
        shm_transport_->segment_size(shm_transport_->max_message_size() * max_samples_);
        pqos.transport().user_transports.push_back(shm_transport_);
        pqos.transport().user_transports.push_back(std::make_shared<UDPv4TransportDescriptor>());
        pqos.transport().use_builtin_transports = false;

        // printf("Create share memery qos success\n");
        LibrarySettings library_settings;
        library_settings.intraprocess_delivery = IntraprocessDeliveryType::INTRAPROCESS_OFF;
        auto factory = DomainParticipantFactory::get_instance();
        factory->set_library_settings(library_settings);
        participant_ = factory->create_participant(domain_id, pqos, nullptr, StatusMask::none());
        if (!participant_) {
            printf("Failed to create DDS participant\n");
            return false;
        }
        
        // printf("Create participant success\n");
        // 注册类型
        type_.reset(new DDSVideoFramePubSubType());
        type_.register_type(participant_);
        // printf("Register type success\n");

        // 创建订阅者
        SubscriberQos sub_qos = SUBSCRIBER_QOS_DEFAULT;
        participant_->get_default_subscriber_qos(sub_qos);
        subscriber_ = participant_->create_subscriber(sub_qos, nullptr, StatusMask::none());
        if (subscriber_ == nullptr)
        {
            printf("Failed to create DDS subscriber\n");
            return false;
        }
        // printf("Create subscriber success\n");

        // 创建主题
        TopicQos topic_qos = TOPIC_QOS_DEFAULT;
        participant_->get_default_topic_qos(topic_qos);
        topic_ = participant_->create_topic(topic_name, type_.get_type_name(), topic_qos);
        if (topic_ == nullptr)
        {
            printf("Failed to create DDS topic: %s", topic_name.c_str());
            return false;
        }
        
        // printf("Create topic success\n");
        // 创建读取器
        DataReaderQos reader_qos = DATAREADER_QOS_DEFAULT;
        subscriber_->get_default_datareader_qos(reader_qos);
        reader_qos.reliability().kind = ReliabilityQosPolicyKind::RELIABLE_RELIABILITY_QOS;
        reader_qos.durability().kind = DurabilityQosPolicyKind::TRANSIENT_LOCAL_DURABILITY_QOS;
        reader_qos.history().kind = HistoryQosPolicyKind::KEEP_LAST_HISTORY_QOS;
        reader_qos.history().depth = max_samples_;
        reader_qos.resource_limits().max_samples_per_instance = max_samples_;
        reader_qos.resource_limits().max_samples = reader_qos.resource_limits().max_instances * max_samples_;
        reader_qos.data_sharing().automatic();

        if (callback_) {
            reader_ = subscriber_->create_datareader(topic_, reader_qos, this, StatusMask::all());
        } else {
            reader_ = subscriber_->create_datareader(topic_, reader_qos);
        }

        if (!reader_) {
            printf("Failed to create DDS reader\n");
            return false;
        }
        
        printf("DDS Reader initialized for topic: %s\n", topic_name.c_str());
        return true;
        
    } catch (const std::exception& e) {
        printf("DDS Reader init exception: %s\n", e.what());
        cleanup();
        return false;
    }
}

void DDSReader::on_subscription_matched( DataReader* /*reader*/,
        const SubscriptionMatchedStatus& info)
{
    if (info.current_count_change == 1)
    {
        std::cout << "Subscriber matched" << std::endl;
    }
    else if (info.current_count_change == -1)
    {
        std::cout << "Subscriber unmatched." << std::endl;
    }
    else
    {
        std::cout << info.current_count_change
                  << " is not a valid value for SubscriptionMatchedStatus current count change" << std::endl;
    }
}

void DDSReader::on_data_available(DataReader* reader)
{
    FASTDDS_CONST_SEQUENCE(DataSeq, DDSVideoFrame);
    SampleInfoSeq info_seq;
    DataSeq data_seq;

    while ((RETCODE_OK == reader->take(data_seq, info_seq, 1, ANY_SAMPLE_STATE, ANY_VIEW_STATE, ANY_INSTANCE_STATE)))
    {
        if ((data_seq.length() > 0) && info_seq[0].valid_data) {
            const DDSVideoFrame & frame = data_seq[0];
            if (callback_)
                callback_(frame);
        }
        reader->return_loan(data_seq, info_seq);
    }
}
    
bool DDSReader::wait_for_data(DDSVideoFrame& frame,int timeout_ms) {
    if (!reader_) {
        return false;
    }
    
    // 简单的轮询实现
    auto start = std::chrono::steady_clock::now();
    while (std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - start).count() < timeout_ms) {
        
        FASTDDS_CONST_SEQUENCE(DataSeq, DDSVideoFrame);
        SampleInfoSeq info_seq;
        DataSeq data_seq;
        
        ReturnCode_t ret = reader_->read(data_seq, info_seq, 1, ANY_SAMPLE_STATE, 
                                        ANY_VIEW_STATE, ANY_INSTANCE_STATE);
        
        if (ret == RETCODE_OK && data_seq.length() > 0 && info_seq[0].valid_data) {
            frame = data_seq[0];
            reader_->return_loan(data_seq, info_seq);
            return true;
        }
        
        reader_->return_loan(data_seq, info_seq);
        std::this_thread::sleep_for(std::chrono::milliseconds(3));  // 减少轮询间隔以降低延时
    }
    
    return false;
}
    
void DDSReader::cleanup() {
    try {
        if (reader_ && subscriber_) {
            subscriber_->delete_datareader(reader_);
            reader_ = nullptr;
        }
        if (subscriber_ && participant_) {
            participant_->delete_subscriber(subscriber_);
            subscriber_ = nullptr;
        }
        if (topic_ && participant_) {
            participant_->delete_topic(topic_);
            topic_ = nullptr;
        }
        if (participant_) {
            auto factory = DomainParticipantFactory::get_instance();
            if (factory) {
                factory->delete_participant(participant_);
            }
            participant_ = nullptr;
        }
    } catch (const std::exception& e) {
        printf("DDS Reader cleanup exception: %s\n", e.what());
    } catch (...) {
        printf("DDS Reader cleanup unknown exception\n");
    }
}

