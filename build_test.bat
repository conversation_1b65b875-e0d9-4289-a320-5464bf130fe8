@echo off
REM Build script for memory protection tests on Windows

echo Building memory protection tests...

REM Create build directory if it doesn't exist
if not exist "build" mkdir build

REM Compile the memory protection test (adjust compiler path as needed)
g++ -std=c++17 -O2 -Wall -Wextra ^
    -I. ^
    examples/memory_protection_test.cpp ^
    -o build/memory_protection_test.exe ^
    -pthread

if %ERRORLEVEL% EQU 0 (
    echo Memory protection test build successful!
) else (
    echo Memory protection test build failed!
    exit /b 1
)

REM Compile the cross-process protection test
g++ -std=c++17 -O2 -Wall -Wextra ^
    -I. ^
    examples/cross_process_protection_test.cpp ^
    -o build/cross_process_protection_test.exe ^
    -pthread

if %ERRORLEVEL% EQU 0 (
    echo Cross-process protection test build successful!
) else (
    echo Cross-process protection test build failed!
    exit /b 1
)

echo All builds successful!
echo To run tests:
echo   build\memory_protection_test.exe
echo   build\cross_process_protection_test.exe (requires Unix environment features)