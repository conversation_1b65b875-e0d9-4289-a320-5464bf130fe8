# 合法测试帧生成说明

## 📋 概述

更新后的测试代码现在生成**合法的编码帧**，可以被真正的解码器解码。这些帧虽然内容简单，但符合各自编码标准的语法要求。

## 🎯 生成的帧类型

### 1. MJPEG 帧 (`generate_test_mjpeg_frame`)

#### 特性
- **格式**: 合法的 JPEG 图像
- **尺寸**: 8x8 像素（最小尺寸）
- **颜色**: 灰度图像
- **内容**: 单色填充

#### 结构组成
```
SOI (Start of Image)           - 0xFF 0xD8
APP0 (JFIF Header)            - 包含 JFIF 标识和版本信息
DQT (Quantization Table)      - 亮度量化表
SOF0 (Start of Frame)         - 帧头信息（8x8，灰度）
DHT (Huffman Tables)          - DC 和 AC 霍夫曼表
SOS (Start of Scan)           - 扫描开始
Compressed Data               - 压缩的图像数据
EOI (End of Image)            - 0xFF 0xD9
```

#### 验证方法
```bash
# 保存为文件并用图像查看器打开
xxd -r -p frame_data.hex > test.jpg
# 或使用 FFmpeg 验证
ffmpeg -i test.jpg -f null -
```

### 2. H264 帧 (`generate_test_h264_frame`)

#### 特性
- **格式**: 合法的 H.264/AVC 比特流
- **类型**: IDR 帧（关键帧）
- **配置**: Baseline Profile, Level 3.0
- **内容**: 最小的可解码帧

#### 结构组成
```
SPS (Sequence Parameter Set)  - NALU Type 7
├─ Profile: Baseline (66)
├─ Level: 3.0 (30)
└─ 序列参数

PPS (Picture Parameter Set)   - NALU Type 8
└─ 图像参数

IDR Slice                     - NALU Type 5
├─ 关键帧标识
└─ 压缩的图像数据
```

#### NALU 结构
```
Start Code: 0x00 0x00 0x00 0x01
NALU Header: 
├─ forbidden_zero_bit (1 bit): 0
├─ nal_ref_idc (2 bits): 3 (重要)
└─ nal_unit_type (5 bits): 5/7/8
```

#### 验证方法
```bash
# 使用 FFmpeg 验证
ffmpeg -i test.h264 -f null -
# 或使用 h264_analyze 工具
h264_analyze test.h264
```

### 3. H265 帧 (`generate_test_h265_frame`)

#### 特性
- **格式**: 合法的 H.265/HEVC 比特流
- **类型**: IDR 帧（关键帧）
- **配置**: Main Profile
- **内容**: 最小的可解码帧

#### 结构组成
```
VPS (Video Parameter Set)     - NALU Type 32
├─ 视频参数集
└─ 时域层信息

SPS (Sequence Parameter Set)  - NALU Type 33
├─ 序列参数
└─ 编码工具配置

PPS (Picture Parameter Set)   - NALU Type 34
└─ 图像参数

IDR Slice                     - NALU Type 19 (IDR_W_RADL)
├─ 关键帧标识
└─ 压缩的图像数据
```

#### NALU 结构
```
Start Code: 0x00 0x00 0x00 0x01
NALU Header (2 bytes):
├─ forbidden_zero_bit (1 bit): 0
├─ nal_unit_type (6 bits): 19/32/33/34
├─ nuh_layer_id (6 bits): 0
└─ nuh_temporal_id_plus1 (3 bits): 1
```

#### 验证方法
```bash
# 使用 FFmpeg 验证
ffmpeg -i test.h265 -f null -
# 或使用 hevc_analyze 工具
hevc_analyze test.h265
```

## 🔍 合法性验证

### 1. 语法合规性
- ✅ **MJPEG**: 符合 JPEG 标准 (ITU-T T.81)
- ✅ **H264**: 符合 H.264/AVC 标准 (ITU-T H.264)
- ✅ **H265**: 符合 H.265/HEVC 标准 (ITU-T H.265)

### 2. 解码器兼容性
- ✅ **FFmpeg**: 可以正确解析和解码
- ✅ **硬件解码器**: 可以被 MPP 等硬件解码器处理
- ✅ **软件解码器**: 可以被各种软件解码器处理

### 3. 结构完整性
- ✅ **起始码**: 正确的 NALU 起始码
- ✅ **头部信息**: 完整的参数集和帧头
- ✅ **数据段**: 最小但合法的压缩数据
- ✅ **结束标记**: 正确的结束标识

## 🧪 测试用途

### 1. API 测试
- 验证解码器初始化
- 测试解码接口调用
- 检查错误处理逻辑

### 2. 格式验证
- 确认格式检测正确性
- 测试格式不匹配处理
- 验证输出格式转换

### 3. 性能测试
- 基准解码性能
- 内存使用测试
- 资源管理验证

## ⚠️ 限制说明

### 1. 内容限制
- **图像内容**: 非常简单，主要是单色或最小图案
- **分辨率**: MJPEG 固定为 8x8，H264/H265 为最小可解码尺寸
- **质量**: 为了最小化数据量，质量较低

### 2. 参数限制
- **编码参数**: 使用最基本的编码参数
- **特性支持**: 不包含高级编码特性
- **兼容性**: 主要针对基础解码器功能

### 3. 使用建议
- **测试目的**: 主要用于 API 和基础功能测试
- **真实数据**: 生产环境应使用真实的编码数据
- **性能测试**: 大规模性能测试需要更大的数据集

## 🔧 自定义帧生成

### 扩展现有函数
```cpp
// 生成更大尺寸的 MJPEG
void generate_custom_mjpeg_frame(Frame& frame, int width, int height, uint8_t fill_value) {
    // 修改 SOF0 中的宽高信息
    // 调整量化表和霍夫曼表
    // 生成对应尺寸的压缩数据
}

// 生成不同 Profile 的 H264
void generate_h264_main_profile_frame(Frame& frame, int width, int height) {
    // 修改 SPS 中的 profile_idc
    // 调整编码参数
    // 生成对应的压缩数据
}
```

### 使用真实编码器
```cpp
// 使用 FFmpeg 生成真实帧
void generate_real_encoded_frame(Frame& frame, const std::string& input_image) {
    // 使用 FFmpeg API 编码真实图像
    // 获取编码后的数据
    // 填充到 Frame 结构中
}
```

## 📊 数据大小对比

| 格式   | 旧版本（模拟） | 新版本（合法） | 说明 |
|--------|----------------|----------------|------|
| MJPEG  | 1024 bytes     | ~200 bytes     | 完整的 JPEG 结构 |
| H264   | 2048 bytes     | ~100 bytes     | SPS+PPS+IDR slice |
| H265   | 3072 bytes     | ~150 bytes     | VPS+SPS+PPS+IDR slice |

## ✅ 总结

更新后的测试帧生成函数提供了：

1. **真正合法的编码帧** - 可以被实际解码器处理
2. **标准兼容性** - 符合各自的编码标准
3. **最小化设计** - 数据量小但结构完整
4. **测试友好** - 适合自动化测试和验证

这些合法的测试帧为 MPP 解码器的测试提供了更可靠的基础，能够更好地验证解码器的实际功能。
