#ifndef BUFFER_MANAGER_H
#define BUFFER_MANAGER_H
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/uio.h>
#include <sys/ioctl.h>
#include <cstring>
#include <stdexcept>
#include <vector>
#include <atomic>
#include <set>
#include <list>
#include <mutex>
#include <condition_variable>
#include <fcntl.h>
#include <unistd.h>
#include <fstream>
#include <sstream>
#include <string>
#include <linux/dma-buf.h>

// 添加缺少的宏定义
#ifndef DMA_HEAP_IOCTL_ALLOC
#define DMA_HEAP_IOCTL_ALLOC _IOWR('H', 0x0, struct dma_heap_allocation_data)
struct dma_heap_allocation_data {
    __u64 len;
    __u32 fd_flags;
    __u32 heap_flags;
    __u32 fd;
    __u32 reserved;
};
#endif

#ifndef MFD_CLOEXEC
#define MFD_CLOEXEC 0x0001U
#endif
#ifndef MFD_ALLOW_SEALING
#define MFD_ALLOW_SEALING 0x0002U
#endif
#ifndef F_ADD_SEALS
#define F_ADD_SEALS 1033
#endif
#ifndef F_SEAL_SHRINK
#define F_SEAL_SHRINK 0x0002
#endif
#ifndef F_SEAL_GROW
#define F_SEAL_GROW 0x0004
#endif
#ifndef F_SEAL_WRITE
#define F_SEAL_WRITE 0x0008
#endif

// 前向声明
int memfd_create(const char *name, unsigned int flags);


namespace buffer_manager {

// 缓冲区类型
enum class BufferType {
    DMA,            // DMA缓冲区
    SHMEM           // 共享内存
};

// 帧元数据
struct FrameMetadata {
    uint64_t buffer_id;     // 缓冲区唯一ID
    uint64_t timestamp;     // 时间戳(us)
    uint32_t width;         // 帧宽度
    uint32_t height;        // 帧高度
    uint32_t stride;        // 行跨度
    uint32_t format;        // 像素格式
    uint32_t data_size;     // 有效数据大小
    BufferType type;        // 缓冲区类型
};


// 缓冲区管理器
class BufferManager {
public:
    // 缓冲区槽位
    struct BufferSlot {
        uint64_t buffer_id;             // 唯一缓冲区ID
        int fd;                         // 文件描述符
        void* addr;                     // 映射地址
        size_t size;                    // 缓冲区大小
        BufferType type;                // 缓冲区类型
        std::atomic<bool> ready;        // Simple ready flag for synchronization
        std::atomic<int> readers;       // Reader count for read/write coordination
        FrameMetadata meta;             // 帧元数据
        uint64_t first_create_us;       // 第一次创建的时间戳
        uint64_t last_state_change_us;  // 最后一次状态变化的时间戳
        uint64_t produced_cost_us;      // 生产花费时间
        uint64_t wait_to_consume_us;    // 等待消费时间
        uint64_t consumed_cost_us;      // 消费花费时间
        uint64_t cycle_cost_us;         // 循环一次花费时间
    };

    struct Statistics
    {
        uint64_t total_buffers;         // 总缓冲区数量
        uint64_t free_buffers;          // 空闲缓冲区数量
        uint64_t ready_buffers;         // 就绪缓冲区数量
        uint64_t consuming_buffers;     // 正在消费的缓冲区数量, 包含同一缓冲区被多个消费者使用
        uint64_t reused_buffers;        // 已重用缓冲区数量
        uint64_t recycle_buffers;       // 已回收的缓冲区数量
    };
    

    BufferManager(BufferType type, size_t buffer_size, size_t ring_size)
        : buffer_type_(type), buffer_size_(buffer_size), ring_size_(ring_size) {
        // 初始化环形缓冲区
        for (size_t i = 0; i < ring_size_; i++) {
            auto slot = std::make_unique<BufferSlot>();
            slot->buffer_id = next_buffer_id_++;
            slot->fd = -1;
            slot->addr = nullptr;
            slot->size = buffer_size_;
            slot->type = buffer_type_;
            slot->ref_count = 0;
            slot->state = BufferState::FREE;
            slot->first_create_us = get_current_us();
            slot->last_state_change_us = 0;
            
            ring_buffer_.push_back(std::move(slot));
            free_queue_.push_back(ring_buffer_.back().get());
            stats_.total_buffers++;
            stats_.free_buffers++;
        }
    }

    // 生产者获取缓冲区
    BufferSlot* take_free_buffer() {
        std::unique_lock<std::mutex> lock(mutex_);
        
        // 等待空闲缓冲区
        cv_.wait(lock, [this]() { return !free_queue_.empty(); });
        
        // 获取缓冲区，并把free_queue_ 中最前的元素弹出
        auto* slot = free_queue_.front();
        free_queue_.pop_front();
        
        // 初始化缓冲区
        if (slot->fd == -1) {
            if (!allocate_buffer(slot)) {
                // 分配失败，放回队列的尾部
                free_queue_.push_back(slot);
                cv_.notify_all();
                return nullptr;
            }
        }
        
        // 重置状态
        slot->ref_count = 0;
        slot->consumers.clear();
        slot->state = BufferState::PRODUCING;
        stats_.free_buffers--;
        slot->last_state_change_us = get_current_us();
        return slot;
    }

    // 生产者释放缓冲区（数据填充完成）
    void return_ready_buffer(BufferSlot* slot) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (slot->state != BufferState::PRODUCING) {
            throw std::runtime_error("Buffer not in producing state");
        }
        // 同步数据
        sync_for_producer(slot);
        // 设置状态
        slot->state = BufferState::READY;
        // 放到就绪队列的尾部
        ready_queue_.push_back(slot);
        stats_.ready_buffers++;
        // 记录生产时间
        uint64_t current_us = get_current_us();
        slot->produced_cost_us = current_us - slot->last_state_change_us;
        slot->last_state_change_us = current_us;
        // 通知等待的消费者
        cv_.notify_all();
    }

    // 消费者获取缓冲区
    BufferSlot* consume_latest_ready_buffer(const std::string& consumer_name) {
        std::unique_lock<std::mutex> lock(mutex_);
        
        // 等待缓冲区就绪
        cv_.wait(lock, [this]() {
            return !ready_queue_.empty();
        });

        // 获取ready_queue_的头部元素, 不要pop 出ready_queue_, 可以被其它消费者使用
        auto* slot = ready_queue_.front();
        
        // 增加引用计数
        if (slot->ref_count++ == 0) {
            stats_.ready_buffers--;
            uint64_t current_us = get_current_us();
            slot->wait_to_consume_us = current_us - slot->last_state_change_us;
            slot->last_state_change_us = current_us;
        }
        slot->consumers.insert(consumer_name);
        slot->state = BufferState::CONSUMING;
        // 同步数据
        sync_for_consumer(slot);
        stats_.consuming_buffers++;

        return slot;
    }

    // On-demand reference counting: only increase ref_count when consumer actually receives buffer
    bool loan_ready_buffer(uint64_t buffer_id, const std::string& consumer_id) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Find the buffer in ready queue
        BufferSlot* slot = nullptr;
        for (auto& slot_ptr : ready_queue_) {
            if (slot_ptr->buffer_id == buffer_id) {
                slot = slot_ptr;
                break;
            }
        }
        
        if (!slot || (slot->state != BufferState::READY && slot->state != BufferState::CONSUMING)) {
            return false;  // Buffer not available
        }
        
        // First consumer for this buffer?
        if (slot->ref_count == 0) {
            // Transition from READY to CONSUMING
            slot->state = BufferState::CONSUMING;
            stats_.ready_buffers--;
            
            uint64_t current_us = get_current_us();
            slot->wait_to_consume_us = current_us - slot->last_state_change_us;
            slot->last_state_change_us = current_us;
            
            // Sync data for consumers
            sync_for_consumer(slot);
        }
        
        // Add this consumer
        slot->ref_count++;
        slot->consumers.insert(consumer_id);
        stats_.consuming_buffers++;
        
        return true;
    }

    // 消费者释放缓冲区
    void return_consume_buffer(BufferSlot* slot, const std::string& consumer_name) {
        std::lock_guard<std::mutex> lock(mutex_);

        // 减少引用计数
        slot->ref_count--;
        slot->consumers.erase(consumer_name);
        stats_.consuming_buffers--;

        // 检查是否所有消费者都已释放
        if (slot->ref_count == 0) {
            slot->state = BufferState::PENDING_RECYCLE;
            uint64_t current_us = get_current_us();
            slot->consumed_cost_us = current_us - slot->last_state_change_us;
            slot->last_state_change_us = current_us;
            handle_zero_ref_count(slot);
        }
    }

    void return_buffer_from_remote(uint64_t buffer_id, const std::string& consumer_name) {
        auto *slot = nullptr;
        {
            std::lock_guard<std::mutex> lock(mutex_);
            for (auto& slot_tmp : ready_queue_) {
                if (slot_tmp->buffer_id == buffer_id) {
                    slot = slot_tmp;
                    break;
                }
            }
        }

        // 释放缓冲区
        if (slot) {
            return_consume_buffer(slot, consumer_name);
        }
    }
    
    // Release all buffers for a specific consumer (used when consumer disconnects)
    void return_all_from_remote(const std::string& consumer_name) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Go through all buffers and release those held by this consumer
        for (auto& slot_ptr : ring_buffer_) {
            auto* slot = slot_ptr.get();
            if (slot->consumers.find(consumer_name) != slot->consumers.end()) {
                // This slot is being used by the disconnected consumer
                slot->ref_count--;
                slot->consumers.erase(consumer_name);
                stats_.consuming_buffers--;
                
                // Check if all consumers have released this buffer
                if (slot->ref_count == 0) {
                    slot->state = BufferState::PENDING_RECYCLE;
                    uint64_t current_us = get_current_us();
                    slot->consumed_cost_us = current_us - slot->last_state_change_us;
                    slot->last_state_change_us = current_us;
                    handle_zero_ref_count(slot);
                }
            }
        }
    }

    
    // Clean up buffers that have been in READY state for too long without any consumers
    void cleanup_unused_ready_buffers(uint64_t timeout_us = 5000000) { // 5 seconds default
        std::lock_guard<std::mutex> lock(mutex_);
        
        uint64_t current_us = get_current_us();
        auto it = ready_queue_.begin();
        
        while (it != ready_queue_.end()) {
            auto* slot = *it;
            
            // Check if buffer has been in READY state for too long
            if (slot->state == BufferState::READY && 
                slot->ref_count == 0 && 
                (current_us - slot->last_state_change_us) > timeout_us) {
                
                // Move back to free queue for reuse
                it = ready_queue_.erase(it);
                slot->state = BufferState::FREE;
                free_queue_.push_back(slot);
                
                stats_.ready_buffers--;
                stats_.free_buffers++;
                
                LOG_D("Cleaned up unused ready buffer %lu after %lu us", 
                      slot->buffer_id, current_us - slot->last_state_change_us);
            } else {
                ++it;
            }
        }
    }

private:
    // 分配缓冲区资源
    bool allocate_buffer(BufferSlot* slot) {
        if (buffer_type_ == BufferType::DMA) {
            slot->fd = alloc_dma_buffer(buffer_size_);
        } else {
            slot->fd = alloc_shmem_buffer(buffer_size_);
        }
        if (slot->fd < 0) {
            return false;
        }

        // 映射内存
        int prot = PROT_READ;
        if (slot->type == BufferType::SHMEM) {
            prot |= PROT_WRITE;
        }
        
        slot->addr = mmap(nullptr, buffer_size_, prot, MAP_SHARED, slot->fd, 0);
        if (slot->addr == MAP_FAILED) {
            close(slot->fd);
            slot->fd = -1;
            return false;
        }
        
        return true;
    }

    // 处理引用计数归零
    void handle_zero_ref_count(BufferSlot* slot) {
        // 根据策略决定回收还是重用
        if (should_recycle_buffer(slot)) {
            recycle_buffer(slot);
            stats_.recycle_buffers++;
        } else {
            prepare_for_reuse(slot);
            stats_.reused_buffers++;
        }
    }

    // ------------------------------------------------------------------
    // 检测 dma-buf / 共享内存是否损坏
    // ------------------------------------------------------------------
    static inline bool is_buffer_corrupted(BufferManager::BufferSlot* slot)
    {
        // 只对 DMA 类型做检测；SHMEM 暂认为无损坏
        if (slot->type != BufferType::DMA || slot->fd < 0)
            return false;

        struct dma_buf_sync sync = {};
        sync.flags = DMA_BUF_SYNC_START | DMA_BUF_SYNC_READ |
                    DMA_BUF_SYNC_VALIDATE_RANGE;
        sync.offset = 0;
        sync.len    = slot->meta.data_size;

        int ret = ioctl(slot->fd, DMA_BUF_IOCTL_SYNC, &sync);
        return ret == -EIO;   // -EIO 在内核表示“底层对象已失效”
    }

    // ------------------------------------------------------------------
    // 计算系统内存压力 0-100
    // ------------------------------------------------------------------
    static inline int system_memory_pressure()
    {
        std::ifstream meminfo("/proc/meminfo");
        if (!meminfo)
            return 50;                       // 保守值：压力中等

        long mem_total = 0, mem_avail = 0;
        std::string line;
        while (std::getline(meminfo, line)) {
            std::istringstream iss(line);
            std::string key; long val; std::string unit;
            iss >> key >> val >> unit;
            if (key == "MemTotal:")  mem_total = val;
            if (key == "MemAvailable:") {
                mem_avail = val;
                break;                       // 找到即可退出
            }
        }

        if (mem_total <= 0) return 50;

        int percent = 100 - (mem_avail * 100 / mem_total);
        return std::max(0, std::min(100, percent));
    }

    bool should_recycle_buffer(BufferSlot* slot) {
        // 检查缓冲区健康状况
        if (is_buffer_corrupted(slot)) {
            return true;
        }
        
        // 计算缓冲区年龄
        // auto age = std::chrono::duration_cast<std::chrono::seconds>(
        //     std::chrono::steady_clock::now() - std::chrono::microseconds(slot->last_used_us));
        
        // // 自定义回收策略
        // bool should_recycle = false;
        
        // // 策略1: 缓冲区太旧
        // if (age > MAX_BUFFER_AGE) {
        //     should_recycle = true;
        // }
        // 策略2: 内存压力大
        // else if (system_memory_pressure() > MEMORY_PRESSURE_THRESHOLD) {
        //     should_recycle = true;
        // }
        return false;
    }

    // 准备缓冲区重用
    void prepare_for_reuse(BufferSlot* slot) {
        slot->state = BufferState::FREE;
        slot->ref_count = 0;
        slot->consumers.clear();
        // 从就绪队列中移除该slot，放到空闲队列的尾部
        ready_queue_.remove(slot);
        free_queue_.push_back(slot);
        stats_.free_buffers++;
        uint64_t current_us = get_current_us();
        slot->cycle_cost_us = current_us - slot->first_create_us;
        slot->first_create_us = current_us;
        cv_.notify_one();
        // print all time cost, 生产者、消费者、循环一次时间
        LOG_D("Buffer %lu produced_cost_us %lu us, wait_to_consume_us %lu us, consumed_cost_us %lu us, cycle_cost_us %lu us", 
            slot->buffer_id, slot->produced_cost_us, slot->wait_to_consume_us, slot->consumed_cost_us, slot->cycle_cost_us);
    }

    // 回收缓冲区资源
    void recycle_buffer(BufferSlot* slot) {
        // 取消映射
        if (slot->addr != MAP_FAILED && slot->addr != nullptr) {
            munmap(slot->addr, slot->size);
            slot->addr = nullptr;
        }
        
        // 关闭文件描述符
        if (slot->fd != -1) {
            close(slot->fd);
            slot->fd = -1;
        }

        // 放回空闲队列
        prepare_for_reuse(slot);
    }

    // 分配DMA缓冲区
    int alloc_dma_buffer(size_t size) {
        int fd = -1;
        
        // 尝试系统DMA堆分配
        fd = open("/dev/dma_heap/system", O_RDWR | O_CLOEXEC);
        if (fd >= 0) {
            struct dma_heap_allocation_data alloc = {
                .len = size,
                .fd_flags = O_RDWR | O_CLOEXEC
            };
            
            if (ioctl(fd, DMA_HEAP_IOCTL_ALLOC, &alloc) == 0) {
                int dma_fd = alloc.fd;
                close(fd);
                return dma_fd;
            }
            close(fd);
        }        
        return -1;
    }

    // 分配共享内存缓冲区
    int alloc_shmem_buffer(size_t size) {
        int fd = memfd_create("shmem_buffer", MFD_CLOEXEC | MFD_ALLOW_SEALING);
        if (fd < 0) return -1;
        
        if (ftruncate(fd, size) < 0) {
            close(fd);
            return -1;
        }
        
        // 添加写保护
        fcntl(fd, F_ADD_SEALS, F_SEAL_SHRINK | F_SEAL_GROW | F_SEAL_WRITE);
        
        return fd;
    }

    void sync_for_producer(BufferSlot* slot) {
        if (slot->type != BufferType::DMA) return;
        
        struct dma_buf_sync sync = {0};
        
        // 使用现代Linux的同步标志
        sync.flags = DMA_BUF_SYNC_END | DMA_BUF_SYNC_WRITE | 
                    DMA_BUF_SYNC_VALIDATE_RANGE;
        
        // 仅同步有效数据区域
        sync.offset = 0;
        sync.len = slot->meta.data_size;
        
        if (ioctl(slot->fd, DMA_BUF_IOCTL_SYNC, &sync) < 0) {
            handle_sync_error(slot, "Producer sync failed");
        }
    }
        
    void sync_for_consumer(BufferSlot* slot) {
        if (slot->type != BufferType::DMA) return;
        
        struct dma_buf_sync sync = {0};
        
        // 使用现代Linux的同步标志
        sync.flags = DMA_BUF_SYNC_START | DMA_BUF_SYNC_READ | 
                    DMA_BUF_SYNC_VALIDATE_RANGE;
        
        // 仅同步有效数据区域
        sync.offset = 0;
        sync.len = slot->meta.data_size;
        
        if (ioctl(slot->fd, DMA_BUF_IOCTL_SYNC, &sync) < 0) {
            handle_sync_error(slot, "Consumer sync failed");
        }
    }

    const int MAX_BUFFER_AGE = 2;
    const int MEMORY_PRESSURE_THRESHOLD = 70;
    const BufferType buffer_type_;
    const size_t buffer_size_;
    const size_t ring_size_;
    std::vector<std::unique_ptr<BufferSlot>> ring_buffer_;
    std::list<BufferSlot*> free_queue_;    // 只放 FREE 状态的 slot
    std::list<BufferSlot*> ready_queue_;   // 只放 READY 状态的 slot
    mutable std::mutex mutex_;
    std::condition_variable cv_;
    struct Statistics stats_ = {0, 0, 0, 0, 0, 0};
    uint64_t next_buffer_id_ = 0;
    
    // 辅助函数
    inline uint64_t get_current_us() {
        struct timeval tv;
        gettimeofday(&tv, nullptr);
        return tv.tv_sec * 1000000ULL + tv.tv_usec;
    }
    
    void handle_sync_error(BufferSlot* slot, const char* error_msg) {
        LOG_E("%s for buffer %lu: %s", error_msg, slot->buffer_id, strerror(errno));
        // 可以在这里添加更多的错误恢复逻辑
    }
};

}
#endif // BUFFER_MANAGER_H