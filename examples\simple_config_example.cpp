#include "../include/config/video_service_config.h"
#include "../include/transport/video_transport_interface.h"
#include "../include/capture/v4l2_capture_interface.h"
#include <iostream>

using namespace video_config;
using namespace video_transport;
using namespace V4L2Capture;

void fastdds_example() {
    std::cout << "=== FastDDS Configuration Example ===" << std::endl;
    
    // 使用预设配置
    auto params = create_fastdds_session("my_fastdds");
    
    // 自定义参数
    params.v4l2.width = 1280;
    params.v4l2.height = 720;
    params.v4l2.fps = 60;
    params.transport.domain_id = 1;
    
    // 兼容性检查
    if (!params.is_compatible()) {
        std::cout << "Configuration incompatible!" << std::endl;
        return;
    }
    
    // 转换为现有接口
    auto v4l2_config = params.to_v4l2_config();
    auto transport_config = params.to_transport_config();
    
    std::cout << "FastDDS session: " << params.name << std::endl;
    std::cout << "Resolution: " << params.v4l2.width << "x" << params.v4l2.height << std::endl;
    std::cout << "Use DMABUF: " << (params.v4l2.use_dmabuf ? "Yes" : "No") << std::endl;
    std::cout << "Transport: FastDDS" << std::endl;
}

void dma_example() {
    std::cout << "\n=== DMA Configuration Example ===" << std::endl;
    
    // 使用预设配置
    auto params = create_dma_session("my_dma");
    
    // 兼容性检查
    if (!params.is_compatible()) {
        std::cout << "Configuration incompatible!" << std::endl;
        return;
    }
    
    std::cout << "DMA session: " << params.name << std::endl;
    std::cout << "Use DMABUF: " << (params.v4l2.use_dmabuf ? "Yes" : "No") << std::endl;
    std::cout << "Buffer count: " << params.v4l2.buffer_count << std::endl;
    std::cout << "Transport: DMA" << std::endl;
}

void shmem_example() {
    std::cout << "\n=== SHMEM Configuration Example ===" << std::endl;
    
    // 使用预设配置
    auto params = create_shmem_session("my_shmem");
    
    // 兼容性检查 - SHMEM不能使用DMABUF
    if (!params.is_compatible()) {
        std::cout << "Configuration incompatible!" << std::endl;
        return;
    }
    
    std::cout << "SHMEM session: " << params.name << std::endl;
    std::cout << "Use DMABUF: " << (params.v4l2.use_dmabuf ? "Yes" : "No") << std::endl;
    std::cout << "Transport: SHMEM" << std::endl;
    std::cout << "✓ SHMEM correctly uses MMAP mode" << std::endl;
}

void custom_example() {
    std::cout << "\n=== Custom Configuration Example ===" << std::endl;
    
    // 手动配置
    SessionParams params;
    params.name = "custom_session";
    
    // V4L2配置
    params.v4l2.device_path = "/dev/video1";
    params.v4l2.width = 640;
    params.v4l2.height = 480;
    params.v4l2.fps = 30;
    params.v4l2.use_dmabuf = false;
    
    // 传输配置
    params.transport.type = TransportType::SHMEM;
    params.transport.topic_name = "custom_video";
    params.transport.buffer_size = params.v4l2.width * params.v4l2.height * 2;
    
    // 兼容性检查
    if (params.is_compatible()) {
        std::cout << "✓ Custom configuration is compatible" << std::endl;
        
        // 转换并使用
        auto v4l2_config = params.to_v4l2_config();
        auto transport_config = params.to_transport_config();
        
        std::cout << "Device: " << v4l2_config.device_path << std::endl;
        std::cout << "Resolution: " << v4l2_config.width << "x" << v4l2_config.height << std::endl;
    } else {
        std::cout << "✗ Custom configuration has compatibility issues" << std::endl;
    }
}

int main() {
    std::cout << "=== Simple Video Configuration Examples ===\n" << std::endl;
    
    fastdds_example();
    dma_example();
    shmem_example();
    custom_example();
    
    std::cout << "\n=== Examples completed ===" << std::endl;
    return 0;
}