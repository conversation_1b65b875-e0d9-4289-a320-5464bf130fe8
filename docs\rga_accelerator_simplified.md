# RGA Accelerator 简化实现说明

## 简化原则

根据用户反馈，我们简化了RGA Accelerator的实现，减少了不必要的`#ifdef HAVE_RGA`条件编译判断。

## 简化前后对比

### 简化前（过度使用条件编译）
```cpp
inline bool RGAAccelerator::resize(const Frame& src, Frame& dst, int target_width, int target_height) {
#ifdef HAVE_RGA
    if (!rga_available_) {
        return false;
    }
    
    // 保持原格式，只进行缩放
    return convert_and_scale(src, dst, target_width, target_height, src.format);
#else
    return false;
#endif
}
```

### 简化后（合理使用条件编译）
```cpp
inline bool RGAAccelerator::resize(const Frame& src, Frame& dst, int target_width, int target_height) {
    if (!rga_available_) {
        return false;
    }
    
    // 保持原格式，只进行缩放
    return convert_and_scale(src, dst, target_width, target_height, src.format);
}
```

## 简化策略

### 1. 头文件级别的条件编译
```cpp
#ifdef HAVE_RGA
#include <im2d.hpp>
#include <RgaUtils.h>
#include <rga.h>
#else
// 当没有RGA支持时的空定义
typedef struct {
    int width;
    int height;
    int format;
    int size;
    void* vir_addr;
} rga_buffer_t;

typedef struct {
    int x, y, width, height;
} im_rect;

#define IM_STATUS_SUCCESS 0
#define RK_FORMAT_RGB_888 1
#endif
```

**优势**:
- 在头文件级别处理依赖
- 提供fallback定义
- 避免在每个方法中重复判断

### 2. 运行时检查替代编译时检查
```cpp
// 使用运行时检查
if (!rga_available_) {
    return false;
}

// 而不是编译时检查
#ifdef HAVE_RGA
// ... 实现
#else
return false;
#endif
```

**优势**:
- 代码更简洁
- 逻辑更清晰
- 减少条件编译分支

### 3. 关键操作处的条件编译
```cpp
#ifdef HAVE_RGA
    // 执行RGA操作：格式转换 + 缩放
    int ret = imresize(src_buf_, dst_buf_);
    if (ret != IM_STATUS_SUCCESS) {
        LOG_E("RGA resize operation failed: %d", ret);
        return false;
    }
#else
    // 软件fallback - 简单复制数据
    LOG_W("RGA not available, using software fallback");
    if (dst.data.size() >= src.data.size()) {
        memcpy(dst.data.data(), src.data.data(), std::min(src.data.size(), dst.data.size()));
    }
#endif
```

**优势**:
- 只在真正需要RGA API的地方使用条件编译
- 提供软件fallback
- 保持功能完整性

## 简化后的代码结构

### 类定义（无条件编译）
```cpp
class RGAAccelerator {
private:
    bool rga_available_ = false;
    rga_buffer_t src_buf_;
    rga_buffer_t dst_buf_;

public:
    bool init();
    bool convert_and_scale(const Frame& src, Frame& dst, int target_width, int target_height, int32_t target_format);
    bool resize(const Frame& src, Frame& dst, int target_width, int target_height);
    bool crop_and_resize(const Frame& src, Frame& dst, int crop_x, int crop_y, int crop_w, int crop_h, int target_width, int target_height);
    bool format_convert(const Frame& src, Frame& dst, int32_t target_format);
    void cleanup();
};
```

### 初始化方法（必要的条件编译）
```cpp
inline bool RGAAccelerator::init() {
#ifdef HAVE_RGA
    int ret = c_RkRgaInit();
    if (ret) {
        LOG_E("Failed to initialize RGA: %d", ret);
        return false;
    }
    rga_available_ = true;
    LOG_I("RGA accelerator initialized successfully");
    return true;
#else
    LOG_W("RGA support not compiled in");
    return false;
#endif
}
```

### 工具方法（无条件编译）
```cpp
inline bool RGAAccelerator::setup_rga_buffer(rga_buffer_t& buf, const Frame& frame) {
    memset(&buf, 0, sizeof(buf));
    
    buf.width = frame.width;
    buf.height = frame.height;
    buf.format = v4l2_to_rga_format(frame.format);
    buf.size = frame.data.size();
    buf.vir_addr = (void*)frame.data.data();
    
    return true;
}
```

## 简化的好处

### 1. 代码可读性提升
- 减少了大量的`#ifdef`分支
- 逻辑流程更清晰
- 更容易理解和维护

### 2. 编译简化
- 减少了条件编译的复杂性
- 更少的编译分支
- 更好的IDE支持

### 3. 调试友好
- 运行时检查更容易调试
- 错误信息更明确
- 日志输出更一致

### 4. 扩展性更好
- 更容易添加新功能
- 更容易添加软件fallback
- 更容易进行单元测试

## 使用示例

### 编译时有RGA支持
```cpp
RGAAccelerator rga;
if (rga.init()) {  // 成功初始化RGA硬件
    rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24);  // 使用硬件加速
}
```

### 编译时无RGA支持
```cpp
RGAAccelerator rga;
if (rga.init()) {  // 初始化失败，但不会崩溃
    // 不会执行到这里
} else {
    // 可以使用其他方法或软件fallback
}
```

### 运行时RGA不可用
```cpp
RGAAccelerator rga;
if (rga.init()) {  // 可能因为设备问题初始化失败
    rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24);  // 会使用软件fallback
}
```

## 最佳实践

### 1. 条件编译的使用原则
- **头文件级别**: 处理依赖和类型定义
- **初始化方法**: 检查硬件可用性
- **核心操作**: 提供硬件/软件两种实现
- **工具方法**: 尽量避免条件编译

### 2. 错误处理
```cpp
// 好的做法：运行时检查 + 明确的错误信息
if (!rga_available_) {
    LOG_W("RGA not available, operation skipped");
    return false;
}

// 避免的做法：过度的条件编译
#ifdef HAVE_RGA
    // 实现
#else
    return false;
#endif
```

### 3. 软件fallback
```cpp
#ifdef HAVE_RGA
    // 硬件实现
    int ret = imresize(src_buf_, dst_buf_);
    if (ret != IM_STATUS_SUCCESS) {
        return false;
    }
#else
    // 软件fallback
    LOG_W("Using software fallback");
    // 简单的软件实现
#endif
```

## 总结

通过减少不必要的`#ifdef HAVE_RGA`判断，我们实现了：

1. **更清晰的代码结构**
2. **更好的可读性和可维护性**
3. **保持了功能完整性**
4. **提供了合理的软件fallback**

这种简化的实现方式既保持了对RGA硬件的完整支持，又提供了在没有RGA支持时的优雅降级，是一个更好的工程实践。
