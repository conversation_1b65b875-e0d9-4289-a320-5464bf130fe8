# GStreamer Encoder Pipeline 结构

## 概述

新的 `GStreamerEncoder` 类为每种编码格式创建了优化的pipeline，包含正确的caps过滤器和videoconvert元素。

## Pipeline 结构

### 通用结构
```
appsrc → queue → videoconvert → format-specific-caps → encoder → output-caps → appsink
```

### H264 编码器 Pipeline
```
appsrc name=source is-live=false do-timestamp=false 
caps="video/x-raw,format=RGB,width=1280,height=720,framerate=30/1" 
! queue max-size-buffers=2 
! videoconvert 
! video/x-raw,format=NV12,width=1280,height=720,framerate=30/1 
! mpph264enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=baseline gop=15 rc-mode=1 
! video/x-h264,profile=baseline,stream-format=byte-stream,alignment=au,width=1280,height=720 
! appsink name=sink
```

### H265 编码器 Pipeline
```
appsrc name=source is-live=false do-timestamp=false 
caps="video/x-raw,format=RGB,width=1280,height=720,framerate=30/1" 
! queue max-size-buffers=2 
! videoconvert 
! video/x-raw,format=NV12,width=1280,height=720,framerate=30/1 
! mpph265enc bps=2000000 bps-min=1000000 bps-max=4000000 profile=main gop=15 rc-mode=1 
! video/x-h265,profile=main,stream-format=byte-stream,alignment=au,width=1280,height=720 
! appsink name=sink
```

### JPEG 编码器 Pipeline
```
appsrc name=source is-live=false do-timestamp=false 
caps="video/x-raw,format=RGB,width=1280,height=720,framerate=30/1" 
! queue max-size-buffers=2 
! videoconvert 
! video/x-raw,format=I420,width=1280,height=720,framerate=30/1 
! mppjpegenc q-factor=85 
! image/jpeg,width=1280,height=720 
! appsink name=sink
```

## 关键特性

### 1. 输入 Caps
- **格式**: RGB (24位)
- **尺寸**: 动态配置 (width × height)
- **帧率**: 动态配置 (framerate/1)

### 2. 格式转换
- **H264/H265**: RGB → NV12 (硬件编码器优化格式)
- **JPEG**: RGB → I420 (JPEG编码器兼容格式)

### 3. 编码器参数
- **H264**: baseline profile, GOP=15, 比特率控制
- **H265**: main profile, GOP=15, 比特率控制  
- **JPEG**: 质量因子控制

### 4. 输出 Caps
- **H264**: byte-stream格式，AU对齐
- **H265**: byte-stream格式，AU对齐
- **JPEG**: 标准JPEG格式

## 使用示例

### 创建不同类型的编码器
```cpp
// H264 编码器 - 1080p 30fps 4Mbps
GStreamerEncoder h264_encoder(EncoderType::H264, 4000000, 1920, 1080, 30);

// H265 编码器 - 720p 30fps 2Mbps  
GStreamerEncoder h265_encoder(EncoderType::H265, 2000000, 1280, 720, 30);

// JPEG 编码器 - 640x480 质量90
GStreamerEncoder jpeg_encoder(EncoderType::JPEG, 90, 640, 480, 30);
```

### 编码流程
```cpp
if (encoder.init()) {
    Frame input_frame;  // RGB格式输入
    Frame output_frame; // 编码后输出
    
    if (encoder.encode(input_frame, output_frame)) {
        // 编码成功，output_frame包含编码后的数据
    }
}
```

## 性能优化

### 1. Pipeline 重用
- 每个编码器实例只创建一次pipeline
- 避免重复的pipeline创建/销毁开销

### 2. 缓冲区管理
- queue元素限制缓冲区数量 (max-size-buffers=2)
- 减少内存使用和延迟

### 3. 硬件加速
- 使用MPP硬件编码器 (mpph264enc, mpph265enc, mppjpegenc)
- 优化的像素格式转换 (NV12, I420)

## 错误处理

### 1. Pipeline 创建失败
- 检查GStreamer插件是否可用
- 验证编码器参数是否有效

### 2. 格式不兼容
- 确保输入数据为RGB格式
- 检查视频尺寸是否合理

### 3. 编码失败
- 监控appsrc/appsink状态
- 处理数据推送/拉取错误

## 扩展性

### 添加新的编码格式
1. 在 `EncoderType` 枚举中添加新类型
2. 在 `create_pipeline_desc()` 中添加对应的pipeline描述
3. 设置适当的输入/输出caps

### 支持不同输入格式
1. 修改appsrc caps设置
2. 调整videoconvert配置
3. 更新格式转换逻辑

这种设计提供了灵活性和性能的平衡，确保每种编码格式都有优化的pipeline配置。
