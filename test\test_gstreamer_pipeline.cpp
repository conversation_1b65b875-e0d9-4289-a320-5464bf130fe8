#include "gstreamer_encoder.h"
#include "common.h"
#include <iostream>
#include <gst/gst.h>

// 测试pipeline描述的正确性
void test_pipeline_descriptions() {
    std::cout << "=== Testing GStreamer Pipeline Descriptions ===" << std::endl;
    
    // 测试H264编码器pipeline
    {
        std::cout << "\n--- H264 Encoder Pipeline ---" << std::endl;
        GStreamerEncoder h264_encoder(EncoderType::H264, 2000000, 1280, 720, 30);
        
        // 获取pipeline描述（需要添加一个公共方法来获取）
        std::cout << "H264 Encoder Type: " << h264_encoder.get_encoder_name() << std::endl;
        std::cout << "Resolution: " << h264_encoder.get_width() << "x" << h264_encoder.get_height() << std::endl;
        std::cout << "Framerate: " << h264_encoder.get_framerate() << " fps" << std::endl;
        std::cout << "Bitrate: " << h264_encoder.get_bitrate() << " bps" << std::endl;
        
        // 测试初始化
        if (h264_encoder.init()) {
            std::cout << "✓ H264 encoder pipeline created successfully" << std::endl;
            h264_encoder.cleanup();
        } else {
            std::cout << "✗ H264 encoder pipeline creation failed" << std::endl;
        }
    }
    
    // 测试H265编码器pipeline
    {
        std::cout << "\n--- H265 Encoder Pipeline ---" << std::endl;
        GStreamerEncoder h265_encoder(EncoderType::H265, 4000000, 1920, 1080, 30);
        
        std::cout << "H265 Encoder Type: " << h265_encoder.get_encoder_name() << std::endl;
        std::cout << "Resolution: " << h265_encoder.get_width() << "x" << h265_encoder.get_height() << std::endl;
        std::cout << "Framerate: " << h265_encoder.get_framerate() << " fps" << std::endl;
        std::cout << "Bitrate: " << h265_encoder.get_bitrate() << " bps" << std::endl;
        
        if (h265_encoder.init()) {
            std::cout << "✓ H265 encoder pipeline created successfully" << std::endl;
            h265_encoder.cleanup();
        } else {
            std::cout << "✗ H265 encoder pipeline creation failed" << std::endl;
        }
    }
    
    // 测试JPEG编码器pipeline
    {
        std::cout << "\n--- JPEG Encoder Pipeline ---" << std::endl;
        GStreamerEncoder jpeg_encoder(EncoderType::JPEG, 85, 640, 480, 30);
        
        std::cout << "JPEG Encoder Type: " << jpeg_encoder.get_encoder_name() << std::endl;
        std::cout << "Resolution: " << jpeg_encoder.get_width() << "x" << jpeg_encoder.get_height() << std::endl;
        std::cout << "Framerate: " << jpeg_encoder.get_framerate() << " fps" << std::endl;
        std::cout << "Quality: " << jpeg_encoder.get_quality() << std::endl;
        
        if (jpeg_encoder.init()) {
            std::cout << "✓ JPEG encoder pipeline created successfully" << std::endl;
            jpeg_encoder.cleanup();
        } else {
            std::cout << "✗ JPEG encoder pipeline creation failed" << std::endl;
        }
    }
}

// 测试pipeline元素的可用性
void test_gstreamer_elements() {
    std::cout << "\n=== Testing GStreamer Elements Availability ===" << std::endl;
    
    gst_init(nullptr, nullptr);
    
    // 检查必需的元素
    const char* required_elements[] = {
        "appsrc",
        "appsink", 
        "queue",
        "videoconvert",
        "mpph264enc",
        "mpph265enc",
        "mppjpegenc"
    };
    
    for (const char* element_name : required_elements) {
        GstElementFactory* factory = gst_element_factory_find(element_name);
        if (factory) {
            std::cout << "✓ " << element_name << " is available" << std::endl;
            gst_object_unref(factory);
        } else {
            std::cout << "✗ " << element_name << " is NOT available" << std::endl;
        }
    }
}

// 测试caps的正确性
void test_caps_compatibility() {
    std::cout << "\n=== Testing Caps Compatibility ===" << std::endl;
    
    gst_init(nullptr, nullptr);
    
    // 测试输入caps
    {
        GstCaps* input_caps = gst_caps_from_string("video/x-raw,format=RGB,width=640,height=480,framerate=30/1");
        if (input_caps) {
            std::cout << "✓ Input caps are valid: " << gst_caps_to_string(input_caps) << std::endl;
            gst_caps_unref(input_caps);
        } else {
            std::cout << "✗ Input caps are invalid" << std::endl;
        }
    }
    
    // 测试NV12 caps
    {
        GstCaps* nv12_caps = gst_caps_from_string("video/x-raw,format=NV12,width=640,height=480,framerate=30/1");
        if (nv12_caps) {
            std::cout << "✓ NV12 caps are valid: " << gst_caps_to_string(nv12_caps) << std::endl;
            gst_caps_unref(nv12_caps);
        } else {
            std::cout << "✗ NV12 caps are invalid" << std::endl;
        }
    }
    
    // 测试I420 caps
    {
        GstCaps* i420_caps = gst_caps_from_string("video/x-raw,format=I420,width=640,height=480,framerate=30/1");
        if (i420_caps) {
            std::cout << "✓ I420 caps are valid: " << gst_caps_to_string(i420_caps) << std::endl;
            gst_caps_unref(i420_caps);
        } else {
            std::cout << "✗ I420 caps are invalid" << std::endl;
        }
    }
    
    // 测试H264输出caps
    {
        GstCaps* h264_caps = gst_caps_from_string("video/x-h264,profile=baseline,stream-format=byte-stream,alignment=au");
        if (h264_caps) {
            std::cout << "✓ H264 output caps are valid: " << gst_caps_to_string(h264_caps) << std::endl;
            gst_caps_unref(h264_caps);
        } else {
            std::cout << "✗ H264 output caps are invalid" << std::endl;
        }
    }
}

int main() {
    std::cout << "GStreamer Encoder Pipeline Test" << std::endl;
    
    // 初始化GStreamer
    gst_init(nullptr, nullptr);
    
    // 运行测试
    test_gstreamer_elements();
    test_caps_compatibility();
    test_pipeline_descriptions();
    
    std::cout << "\nTest completed!" << std::endl;
    return 0;
}
