#ifndef SIMPLIFIED_VIDEO_TRANSPORT_H
#define SIMPLIFIED_VIDEO_TRANSPORT_H

#include "simplified_buffer_manager.h"
#include "video_transport_interface.h"
#include <liburing.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/mman.h>
#include <atomic>
#include <thread>
#include <unordered_map>
#include <functional>
#include <cstring>

namespace simplified_transport {

using namespace video_transport;

// Simple release message
struct ReleaseMessage {
    uint64_t buffer_id;
    uint32_t msg_type = 1; // Release message type
};

// Message structure for io_uring operations
struct TransportMessage {
    msghdr hdr;
    iovec iov[1];
    char ctrl_buf[CMSG_SPACE(sizeof(int))];
    FrameMetadata metadata;
    
    TransportMessage() {
        memset(&hdr, 0, sizeof(msghdr));
        iov[0].iov_base = &metadata;
        iov[0].iov_len = sizeof(FrameMetadata);
        hdr.msg_iov = iov;
        hdr.msg_iovlen = 1;
        hdr.msg_control = ctrl_buf;
        hdr.msg_controllen = sizeof(ctrl_buf);
        
        // Initialize control message for fd passing
        struct cmsghdr* cmsg = CMSG_FIRSTHDR(&hdr);
        cmsg->cmsg_level = SOL_SOCKET;
        cmsg->cmsg_type = SCM_RIGHTS;
        cmsg->cmsg_len = CMSG_LEN(sizeof(int));
    }
};

// Simplified Producer - leverages kernel fd management
class SimplifiedProducer : public IVideoPublisher {
public:
    SimplifiedProducer() : initialized_(false), running_(false), server_fd_(-1) {}
    ~SimplifiedProducer() override { cleanup(); }

    bool initialize(const TransportConfig& config) override {
        if (initialized_.load()) return true;
        
        try {
            config_ = config;
            socket_path_ = config.topic_name;
            
            BufferType buffer_type = (config.type == TransportType::DMA) ? 
                                   BufferType::DMA : BufferType::SHMEM;
            
            buffer_manager_ = std::make_unique<SimplifiedBufferManager>(
                buffer_type, config.buffer_size, config.ring_buffer_size);
            
            setup_server_socket();
            setup_io_uring();
            
            running_ = true;
            event_thread_ = std::thread(&SimplifiedProducer::process_events, this);
            
            initialized_.store(true);
            return true;
        } catch (const std::exception& e) {
            return false;
        }
    }

    void cleanup() override {
        running_ = false;
        if (event_thread_.joinable()) {
            event_thread_.join();
        }
        
        if (server_fd_ >= 0) {
            close(server_fd_);
            unlink(socket_path_.c_str());
        }
        
        io_uring_queue_exit(&ring_);
        initialized_.store(false);
    }

    BufferResult acquire_buffer(BufferHandle& handle) override {
        if (!initialized_.load()) return BufferResult::TRANSPORT_ERROR;
        
        auto* buffer = buffer_manager_->acquire_buffer();
        if (!buffer) return BufferResult::BUFFER_NOT_AVAILABLE;
        
        handle.data = buffer->mapped_addr;
        handle.size = buffer->size;
        handle.used_size = 0;
        handle.buffer_id = buffer->buffer_id;
        handle.transport_data.ptr = buffer; // Store buffer pointer
        handle.transport_type = (buffer->type == BufferType::DMA) ? 
                               TransportType::DMA : TransportType::SHMEM;
        handle.is_valid = true;
        
        return BufferResult::SUCCESS;
    }

    BufferResult publish_buffer(BufferHandle& handle) override {
        if (!handle.is_valid || !handle.transport_data.ptr) {
            return BufferResult::INVALID_DATA;
        }
        
        auto* buffer = static_cast<SimplifiedBufferManager::SharedBuffer*>(handle.transport_data.ptr);
        
        // Update metadata
        buffer->metadata.data_size = handle.used_size;
        buffer->metadata.width = handle.metadata.width;
        buffer->metadata.height = handle.metadata.height;
        buffer->metadata.format = handle.metadata.format;
        buffer->metadata.timestamp = handle.metadata.timestamp;
        
        // Publish buffer (sets ready flag and read-only protection)
        buffer_manager_->publish_buffer(buffer);
        
        // Send to all connected consumers
        send_to_all_consumers(buffer);
        
        handle.is_valid = false;
        return BufferResult::SUCCESS;
    }

    int get_dma_fd(const BufferHandle& handle) override {
        if (!handle.is_valid || !handle.transport_data.ptr) return -1;
        auto* buffer = static_cast<SimplifiedBufferManager::SharedBuffer*>(handle.transport_data.ptr);
        return buffer->fd;
    }

    bool publish_frame(const fastdds::video::Frame& frame) override {
        BufferHandle handle;
        if (acquire_buffer(handle) != BufferResult::SUCCESS) return false;
        
        if (frame.data.size() > handle.size) return false;
        
        memcpy(handle.data, frame.data.data(), frame.data.size());
        handle.used_size = frame.data.size();
        handle.metadata.width = frame.width;
        handle.metadata.height = frame.height;
        handle.metadata.format = frame.format;
        handle.metadata.timestamp = frame.timestamp;
        
        return publish_buffer(handle) == BufferResult::SUCCESS;
    }

    bool has_subscribers() const override {
        return !connections_.empty();
    }

    TransportStats get_stats() const override { return stats_; }
    void reset_stats() override { stats_ = TransportStats{}; }
    std::string get_status() const override {
        return initialized_.load() ? "Running" : "Stopped";
    }
    bool supports_v4l2_zero_copy() const override { return true; }

private:
    void setup_server_socket() {
        server_fd_ = socket(AF_UNIX, SOCK_SEQPACKET, 0);
        if (server_fd_ < 0) throw std::runtime_error("Failed to create socket");
        
        unlink(socket_path_.c_str());
        
        struct sockaddr_un addr = {};
        addr.sun_family = AF_UNIX;
        strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);
        
        if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
            throw std::runtime_error("Failed to bind socket");
        }
        
        if (listen(server_fd_, 10) < 0) {
            throw std::runtime_error("Failed to listen on socket");
        }
    }

    void setup_io_uring() {
        if (io_uring_queue_init(256, &ring_, 0)) {
            throw std::runtime_error("Failed to setup io_uring");
        }
        submit_accept();
    }

    void submit_accept() {
        struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
        if (!sqe) return;
        
        io_uring_prep_accept(sqe, server_fd_, nullptr, nullptr, 0);
        io_uring_sqe_set_data(sqe, reinterpret_cast<void*>(1)); // Accept marker
        io_uring_submit(&ring_);
    }

    void send_to_all_consumers(SimplifiedBufferManager::SharedBuffer* buffer) {
        if (connections_.empty()) return;
        
        for (auto& [fd, _] : connections_) {
            submit_send_frame(fd, buffer);
        }
    }

    void submit_send_frame(int client_fd, SimplifiedBufferManager::SharedBuffer* buffer) {
        struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
        if (!sqe) return;
        
        auto* msg = new TransportMessage();
        msg->metadata = buffer->metadata;
        
        // Set DMA fd in control message
        struct cmsghdr* cmsg = CMSG_FIRSTHDR(&msg->hdr);
        *((int*)CMSG_DATA(cmsg)) = buffer->fd;
        
        io_uring_prep_sendmsg(sqe, client_fd, &msg->hdr, 0);
        io_uring_sqe_set_data(sqe, msg);
        io_uring_submit(&ring_);
    }

    void process_events() {
        while (running_.load()) {
            struct io_uring_cqe* cqe;
            if (io_uring_wait_cqe(&ring_, &cqe) < 0) continue;
            
            void* data = io_uring_cqe_get_data(cqe);
            int result = cqe->res;
            
            if (data == reinterpret_cast<void*>(1)) {
                // Accept event
                if (result >= 0) {
                    connections_[result] = true;
                    submit_recv(result);
                }
                if (running_.load()) submit_accept();
            } else if (data == reinterpret_cast<void*>(2)) {
                // Receive event - handle release messages
                if (result > 0) {
                    // Process release message (simplified)
                }
            } else {
                // Send event - cleanup message
                delete static_cast<TransportMessage*>(data);
            }
            
            io_uring_cqe_seen(&ring_, cqe);
        }
    }

    void submit_recv(int client_fd) {
        struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
        if (!sqe) return;
        
        // Simplified receive for release messages
        io_uring_prep_recv(sqe, client_fd, nullptr, sizeof(ReleaseMessage), 0);
        io_uring_sqe_set_data(sqe, reinterpret_cast<void*>(2)); // Recv marker
        io_uring_submit(&ring_);
    }

    std::atomic<bool> initialized_;
    std::atomic<bool> running_;
    std::string socket_path_;
    int server_fd_;
    io_uring ring_;
    std::thread event_thread_;
    std::unordered_map<int, bool> connections_;
    
    std::unique_ptr<SimplifiedBufferManager> buffer_manager_;
    TransportConfig config_;
    TransportStats stats_;
};

// Simplified Consumer - leverages kernel fd management
// Performance optimization: Maintains persistent buffer mappings
// to avoid expensive mmap/munmap cycles on every frame
class SimplifiedConsumer : public IVideoSubscriber {
public:
    SimplifiedConsumer() : initialized_(false), connected_(false), running_(false), server_fd_(-1) {}
    ~SimplifiedConsumer() override { cleanup(); }

    bool initialize(const TransportConfig& config) override {
        if (initialized_.load()) return true;
        
        try {
            config_ = config;
            socket_path_ = config.topic_name;
            
            // Create buffer manager for consumer-side memory protection
            BufferType buffer_type = (config.type == TransportType::DMA) ? 
                                   BufferType::DMA : BufferType::SHMEM;
            buffer_manager_ = std::make_unique<SimplifiedBufferManager>(
                buffer_type, config.buffer_size, config.ring_buffer_size);
            
            connect_to_server();
            setup_io_uring();
            submit_recv();
            
            running_ = true;
            event_thread_ = std::thread(&SimplifiedConsumer::process_events, this);
            
            initialized_.store(true);
            connected_.store(true);
            return true;
        } catch (const std::exception& e) {
            return false;
        }
    }

    void cleanup() override {
        running_ = false;
        if (event_thread_.joinable()) {
            event_thread_.join();
        }
        
        // Cleanup all persistent mapped buffers - this is where munmap happens
        // We maintain persistent mappings during operation for performance
        for (auto& [buffer_id, info] : mapped_buffers_) {
            if (info.addr && info.addr != MAP_FAILED) {
                munmap(info.addr, info.size);
            }
            // Kernel will handle fd cleanup when we close it
        }
        mapped_buffers_.clear();
        
        if (server_fd_ >= 0) {
            close(server_fd_);
        }
        
        io_uring_queue_exit(&ring_);
        initialized_.store(false);
        connected_.store(false);
    }

    BufferResult receive_frame_buffer(BufferHandle& handle, int timeout_ms) override {
        // Wait for frame with timeout
        std::unique_lock<std::mutex> lock(frame_mutex_);
        bool success = frame_cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                                         [this]() { return !pending_frames_.empty(); });
        
        if (!success) return BufferResult::TIMEOUT;
        
        auto frame_info = pending_frames_.front();
        pending_frames_.pop();
        lock.unlock();
        
        // Check if we already have this buffer mapped (persistent mapping)
        void* mapped_addr = nullptr;
        {
            std::lock_guard<std::mutex> map_lock(mapped_mutex_);
            auto it = mapped_buffers_.find(frame_info.metadata.buffer_id);
            if (it != mapped_buffers_.end()) {
                // Buffer already mapped, reuse existing mapping
                mapped_addr = it->second.addr;
                // Update fd if different (shouldn't happen but be safe)
                if (it->second.fd != frame_info.fd) {
                    close(it->second.fd);
                    it->second.fd = frame_info.fd;
                }
            } else {
                // First time seeing this buffer, create persistent mapping
                mapped_addr = mmap(nullptr, frame_info.metadata.data_size, 
                                 PROT_READ, MAP_SHARED, frame_info.fd, 0);
                
                if (mapped_addr == MAP_FAILED) {
                    close(frame_info.fd);
                    return BufferResult::TRANSPORT_ERROR;
                }
                
                // Store persistent mapping
                mapped_buffers_[frame_info.metadata.buffer_id] = {
                    mapped_addr, frame_info.metadata.data_size, frame_info.fd
                };
            }
        }
        
        // Setup handle
        handle.data = mapped_addr;
        handle.size = frame_info.metadata.data_size;
        handle.used_size = frame_info.metadata.data_size;
        handle.buffer_id = frame_info.metadata.buffer_id;
        handle.metadata = frame_info.metadata;
        handle.transport_type = (frame_info.metadata.type == BufferType::DMA) ? 
                               TransportType::DMA : TransportType::SHMEM;
        handle.is_valid = true;
        
        return BufferResult::SUCCESS;
    }

    BufferResult return_frame_buffer(BufferHandle& handle) override {
        if (!handle.is_valid) return BufferResult::INVALID_DATA;
        
        // Send release message to producer (optional - kernel handles cleanup)
        // But good practice to notify producer that we're done with this frame
        ReleaseMessage msg{handle.buffer_id};
        send(server_fd_, &msg, sizeof(msg), 0);
        
        // Important: Do NOT munmap here - keep persistent mapping
        // The mapping will be cleaned up in cleanup() method
        // This avoids expensive mmap/munmap cycles
        
        handle.is_valid = false;
        return BufferResult::SUCCESS;
    }

    bool receive_frame(fastdds::video::Frame& frame, int timeout_ms) override {
        BufferHandle handle;
        if (receive_frame_buffer(handle, timeout_ms) != BufferResult::SUCCESS) {
            return false;
        }
        
        // Copy data to FastDDS frame
        frame.width = handle.metadata.width;
        frame.height = handle.metadata.height;
        frame.format = handle.metadata.format;
        frame.timestamp = handle.metadata.timestamp;
        frame.data.resize(handle.used_size);
        memcpy(frame.data.data(), handle.data, handle.used_size);
        
        return_frame_buffer(handle);
        return true;
    }

    void set_frame_callback(std::function<void(const fastdds::video::Frame&)> callback) override {
        frame_callback_ = callback;
    }

    void set_buffer_callback(std::function<void(BufferHandle&)> callback) override {
        buffer_callback_ = callback;
    }

    bool is_connected() const override { return connected_.load(); }
    TransportStats get_stats() const override { return stats_; }
    void reset_stats() override { stats_ = TransportStats{}; }
    std::string get_status() const override {
        return connected_.load() ? "Connected" : "Disconnected";
    }

private:
    struct FrameInfo {
        FrameMetadata metadata;
        int fd;
    };

    struct MappedBufferInfo {
        void* addr;
        size_t size;
        int fd;
    };

    void connect_to_server() {
        server_fd_ = socket(AF_UNIX, SOCK_SEQPACKET, 0);
        if (server_fd_ < 0) throw std::runtime_error("Failed to create socket");
        
        struct sockaddr_un addr = {};
        addr.sun_family = AF_UNIX;
        strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);
        
        if (connect(server_fd_, (struct sockaddr*)&addr, sizeof(addr))) {
            throw std::runtime_error("Failed to connect to server");
        }
    }

    void setup_io_uring() {
        if (io_uring_queue_init(256, &ring_, 0)) {
            throw std::runtime_error("Failed to setup io_uring");
        }
    }

    void submit_recv() {
        struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
        if (!sqe) return;
        
        auto* msg = new TransportMessage();
        io_uring_prep_recvmsg(sqe, server_fd_, &msg->hdr, 0);
        io_uring_sqe_set_data(sqe, msg);
        io_uring_submit(&ring_);
    }

    void process_events() {
        while (running_.load()) {
            struct io_uring_cqe* cqe;
            if (io_uring_wait_cqe(&ring_, &cqe) < 0) continue;
            
            auto* msg = static_cast<TransportMessage*>(io_uring_cqe_get_data(cqe));
            int result = cqe->res;
            
            if (result > 0 && msg) {
                // Extract fd from control message
                struct cmsghdr* cmsg = CMSG_FIRSTHDR(&msg->hdr);
                if (cmsg && cmsg->cmsg_type == SCM_RIGHTS) {
                    int fd = *((int*)CMSG_DATA(cmsg));
                    
                    // Add to pending frames
                    {
                        std::lock_guard<std::mutex> lock(frame_mutex_);
                        pending_frames_.emplace(FrameInfo{msg->metadata, fd});
                    }
                    frame_cv_.notify_one();
                    
                    // Trigger callbacks if set
                    if (buffer_callback_ || frame_callback_) {
                        trigger_callbacks(msg->metadata, fd);
                    }
                }
            }
            
            delete msg;
            io_uring_cqe_seen(&ring_, cqe);
            
            if (running_.load()) submit_recv();
        }
    }

    void trigger_callbacks(const FrameMetadata& metadata, int fd) {
        if (buffer_callback_) {
            BufferHandle handle;
            handle.buffer_id = metadata.buffer_id;
            handle.metadata = metadata;
            // Note: callback should handle mapping/unmapping
            buffer_callback_(handle);
        }
        
        if (frame_callback_) {
            // For frame callback, we need to map and copy
            void* addr = mmap(nullptr, metadata.data_size, PROT_READ, MAP_SHARED, fd, 0);
            if (addr != MAP_FAILED) {
                fastdds::video::Frame frame;
                frame.width = metadata.width;
                frame.height = metadata.height;
                frame.format = metadata.format;
                frame.timestamp = metadata.timestamp;
                frame.data.resize(metadata.data_size);
                memcpy(frame.data.data(), addr, metadata.data_size);
                
                munmap(addr, metadata.data_size);
                frame_callback_(frame);
            }
        }
    }

    std::atomic<bool> initialized_;
    std::atomic<bool> connected_;
    std::atomic<bool> running_;
    std::string socket_path_;
    int server_fd_;
    io_uring ring_;
    std::thread event_thread_;
    
    std::unique_ptr<SimplifiedBufferManager> buffer_manager_;  // For memory protection
    TransportConfig config_;
    TransportStats stats_;
    
    std::queue<FrameInfo> pending_frames_;
    std::mutex frame_mutex_;
    std::condition_variable frame_cv_;
    
    std::unordered_map<uint64_t, MappedBufferInfo> mapped_buffers_;
    std::mutex mapped_mutex_;
    
    std::function<void(const fastdds::video::Frame&)> frame_callback_;
    std::function<void(BufferHandle&)> buffer_callback_;
};

} // namespace simplified_transport

#endif // SIMPLIFIED_VIDEO_TRANSPORT_H