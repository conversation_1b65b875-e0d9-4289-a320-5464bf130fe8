{"global": {"enable_stats_logging": true, "stats_logging_interval_ms": 5000, "log_level": "INFO", "log_file": "/tmp/video_transport.log"}, "publishers": [{"enabled": true, "name": "main_camera_fastdds", "description": "Main camera publisher using FastDDS", "transport": {"transport_type": "fastdds", "topic_name": "main_video_frames", "domain_id": 0, "max_samples": 5, "timeout_ms": 1000}}, {"enabled": false, "name": "main_camera_dma", "description": "Main camera publisher using DMA shared memory", "transport": {"transport_type": "dma_shared", "socket_path": "/tmp/main_video_transport.sock", "buffer_size": 6220800, "ring_buffer_size": 10, "timeout_ms": 1000}}], "subscribers": [{"enabled": true, "name": "ai_processor_fastdds", "description": "AI processor subscriber using FastDDS", "subscribed_topics": ["main_video_frames", "thermal_video_frames"], "transport": {"transport_type": "fastdds", "topic_name": "main_video_frames", "domain_id": 0, "max_samples": 5, "timeout_ms": 1000}}, {"enabled": false, "name": "ai_processor_dma", "description": "AI processor subscriber using DMA shared memory", "subscribed_topics": ["main_video_frames"], "transport": {"transport_type": "dma_shared", "socket_path": "/tmp/main_video_transport.sock", "buffer_size": 6220800, "ring_buffer_size": 10, "timeout_ms": 1000}}, {"enabled": true, "name": "rtsp_server_fastdds", "description": "RTSP server subscriber using FastDDS", "subscribed_topics": ["main_video_frames"], "transport": {"transport_type": "fastdds", "topic_name": "main_video_frames", "domain_id": 0, "max_samples": 3, "timeout_ms": 500}}]}