# SharedBufferSubscriber 优化文档

## 概述

`shared_buffer_subcriber.h` 文件实现了 `IVideoSubscriber` 接口，通过 io_uring 接收 DMA 或共享内存的文件描述符，使用完后发送 ReleaseMessage 通知 IVideoPublisher。为了避免昂贵的 mmap/munmap 操作，只有在断开连接时才进行 munmap。

## 主要优化内容

### 1. 代码结构重组

**问题**：原代码中类的实现在前，声明在后，导致结构混乱。

**优化**：
- 将类声明移到前面，实现移到后面
- 统一了头文件保护宏名称：`SHARED_BUFFER_SUBSCRIBER_H`
- 添加了必要的头文件包含

### 2. 消息池优化

**问题**：原消息池使用动态分配和复杂的批量分配逻辑。

**优化**：
```cpp
class MessagePool {
    // 使用 unique_ptr 管理内存，避免内存泄漏
    std::vector<std::unique_ptr<Message>> messages_;
    std::vector<Message*> free_list_;
    
    // 构造函数中预设控制消息头，避免重复设置
    Message() {
        // 预设控制消息头
        struct cmsghdr* cmsg = CMSG_FIRSTHDR(&hdr);
        if (cmsg) {
            cmsg->cmsg_level = SOL_SOCKET;
            cmsg->cmsg_type = SCM_RIGHTS;
            cmsg->cmsg_len = CMSG_LEN(sizeof(int));
        }
    }
};
```

### 3. 错误处理改进

**问题**：原代码中存在一些错误的函数调用和不完整的错误处理。

**优化**：
- 修复了 `handle_recv` 中错误的 `return_frame_buffer(handle)` 调用
- 添加了连接状态检查，避免在断开连接后继续操作
- 改进了异常处理，在初始化失败时调用 cleanup()

### 4. 连接管理优化

**问题**：连接断开时没有及时通知等待的线程。

**优化**：
```cpp
inline void IoUringConsumer::cleanup() {
    running_ = false;
    connected_.store(false);
    
    // 唤醒等待的线程
    sync_buffer_cv_.notify_all();
    
    // 清理所有持久映射的缓冲区 - 只有断开时才munmap
    {
        std::lock_guard<std::mutex> lock(mapped_buffers_mutex_);
        for (auto& [buffer_id, info] : mapped_buffers_) {
            if (info.addr && info.addr != MAP_FAILED) {
                munmap(info.addr, info.size);
            }
            if (info.fd >= 0) {
                close(info.fd);
            }
        }
        mapped_buffers_.clear();
    }
}
```

### 5. 持久映射优化

**核心特性**：避免昂贵的 mmap/munmap 操作

**实现**：
```cpp
// 检查是否已经映射此缓冲区（持久映射避免昂贵的mmap/munmap）
void* mapped_addr = nullptr;
{
    std::lock_guard<std::mutex> map_lock(mapped_buffers_mutex_);
    auto it = mapped_buffers_.find(sync_buffer.metadata.buffer_id);
    if (it != mapped_buffers_.end()) {
        // 缓冲区已映射，重用现有映射
        mapped_addr = it->second.addr;
    } else {
        // 首次看到此缓冲区，创建持久映射
        mapped_addr = mmap(nullptr, sync_buffer.metadata.data_size, 
                            PROT_READ, MAP_SHARED, sync_buffer.fd, 0);
        
        // 存储持久映射
        mapped_buffers_[sync_buffer.metadata.buffer_id] = {
            mapped_addr, sync_buffer.metadata.data_size, sync_buffer.fd
        };
    }
}
```

### 6. 线程安全改进

**优化**：
- 使用 `std::move` 优化回调函数设置
- 改进了条件变量的使用，添加了连接状态检查
- 统一了锁的使用模式

### 7. 性能优化

**优化点**：
- 减少了 io_uring 队列大小从 1024 到 256，降低内存使用
- 使用线程本地存储避免 ReleaseMessage 的重复分配
- 添加了非阻塞 socket 模式设置
- 优化了事件处理循环的错误处理

### 8. 代码清理

**移除的过时逻辑**：
- 删除了重复的类声明
- 移除了不必要的回调触发逻辑
- 简化了消息池的内存管理
- 统一了注释风格为中文

## 核心设计原则

1. **避免昂贵的 mmap/munmap**：通过持久映射机制，只在连接断开时才进行 munmap
2. **线程安全**：所有共享数据都有适当的锁保护
3. **资源管理**：使用 RAII 和智能指针管理资源
4. **错误处理**：完善的错误处理和状态管理
5. **性能优化**：减少不必要的内存分配和系统调用

## 测试覆盖

创建了完整的单元测试文件 `test_shared_buffer_subscriber.cpp`，覆盖：
- 基本初始化功能
- 消息池功能
- 数据结构正确性
- 回调设置
- 错误处理

## 使用示例

```cpp
#include "transport/shared_buffer_subcriber.h"

// 创建消费者
IoUringConsumer consumer;

// 配置传输
TransportConfig config(TransportType::DMA, "/tmp/video_socket");

// 初始化
if (consumer.initialize(config)) {
    // 设置回调
    consumer.set_frame_callback([](const fastdds::video::Frame& frame) {
        // 处理接收到的帧
    });
    
    // 或者使用同步模式
    BufferHandle handle;
    if (consumer.receive_frame_buffer(handle, 1000) == BufferResult::SUCCESS) {
        // 处理缓冲区数据
        consumer.return_frame_buffer(handle);
    }
}
```

## 总结

优化后的 `shared_buffer_subcriber.h` 文件具有更好的代码结构、更强的错误处理能力、更高的性能和更好的可维护性。核心的持久映射机制有效避免了昂贵的 mmap/munmap 操作，提高了视频传输的效率。
