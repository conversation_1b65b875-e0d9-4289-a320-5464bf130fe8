# Video Transport Abstraction Library
cmake_minimum_required(VERSION 3.16)
project(video_transport)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)

# FastDDS
find_package(fastcdr REQUIRED)
find_package(fastrtps REQUIRED)

# JsonCpp
pkg_check_modules(JSONCPP jsoncpp)

# liburing (optional for DMA transport)
find_path(LIBURING_INCLUDE_DIR liburing.h)
find_library(LIBURING_LIBRARY uring)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/dds_video_frame
)

# Video Transport Abstraction Library
set(VIDEO_TRANSPORT_SOURCES
    src/video_transport_factory.cpp
    src/fastdds_video_transport.cpp
)

set(VIDEO_TRANSPORT_HEADERS
    include/video_transport_interface.h
    include/fastdds_video_transport.h
)

# Add DMA transport if liburing is available
if(LIBURING_INCLUDE_DIR AND LIBURING_LIBRARY)
    list(APPEND VIDEO_TRANSPORT_SOURCES src/dma_video_transport.cpp)
    list(APPEND VIDEO_TRANSPORT_HEADERS include/dma_video_transport.h include/dma_distribution.h include/dma_buffer_manager.h)
    add_definitions(-DHAVE_LIBURING)
    message(STATUS "Building with DMA transport support (liburing found)")
else()
    message(WARNING "liburing not found, building without DMA transport support")
endif()

# Create the library
add_library(video_transport SHARED ${VIDEO_TRANSPORT_SOURCES})

# Link libraries
target_link_libraries(video_transport
    fastcdr
    fastrtps
    ${JSONCPP_LIBRARIES}
)

# Add liburing if available
if(LIBURING_INCLUDE_DIR AND LIBURING_LIBRARY)
    target_include_directories(video_transport PRIVATE ${LIBURING_INCLUDE_DIR})
    target_link_libraries(video_transport ${LIBURING_LIBRARY})
endif()

# Include directories for the library
target_include_directories(video_transport
    PUBLIC 
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${JSONCPP_INCLUDE_DIRS}
)

# Compiler flags
target_compile_options(video_transport PRIVATE ${JSONCPP_CFLAGS_OTHER})

# Example programs
add_executable(video_transport_example examples/video_transport_example.cpp)
target_link_libraries(video_transport_example video_transport)

# Install targets
install(TARGETS video_transport video_transport_example
    EXPORT video_transport_targets
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(FILES ${VIDEO_TRANSPORT_HEADERS}
    DESTINATION include/video_transport
)

install(FILES config/video_transport_example.json
    DESTINATION share/video_transport/config
)

# Export targets
install(EXPORT video_transport_targets
    FILE video_transport-config.cmake
    DESTINATION lib/cmake/video_transport
)

# Create version file
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    video_transport-config-version.cmake
    VERSION 1.0.0
    COMPATIBILITY SameMajorVersion
)

install(FILES "${CMAKE_CURRENT_BINARY_DIR}/video_transport-config-version.cmake"
    DESTINATION lib/cmake/video_transport
)

# Tests (optional)
option(BUILD_TESTS "Build test programs" OFF)
if(BUILD_TESTS)
    enable_testing()
    
    # Basic transport test
    add_executable(test_transport_basic test/test_transport_basic.cpp)
    target_link_libraries(test_transport_basic video_transport)
    add_test(NAME transport_basic COMMAND test_transport_basic)
    
    # Configuration test
    add_executable(test_transport_config test/test_transport_config.cpp)
    target_link_libraries(test_transport_config video_transport)
    add_test(NAME transport_config COMMAND test_transport_config)
endif()

# Print configuration summary
message(STATUS "Video Transport Configuration:")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  FastDDS support: YES")
message(STATUS "  DMA transport: ${LIBURING_LIBRARY}")
message(STATUS "  JsonCpp: ${JSONCPP_LIBRARIES}")
message(STATUS "  Build tests: ${BUILD_TESTS}")