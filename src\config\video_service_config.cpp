#include "../../include/config/video_service_config.h"
#include <iostream>

namespace video_transport {

// ============================================================================
// TransportConfigLoader Implementation
// ============================================================================

bool TransportConfigLoader::load_from_file(const std::string& config_file, TransportConfig& config) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Failed to open config file: " << config_file << std::endl;
        return false;
    }
    
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;
    
    if (!Json::parseFromStream(builder, file, &root, &errors)) {
        std::cerr << "Failed to parse JSON from file: " << errors << std::endl;
        return false;
    }
    
    return load_from_json_object(root, config);
}

bool TransportConfigLoader::load_from_json_string(const std::string& json_str, TransportConfig& config) {
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;
    std::istringstream json_stream(json_str);
    
    if (!Json::parseFromStream(builder, json_stream, &root, &errors)) {
        std::cerr << "Failed to parse JSON string: " << errors << std::endl;
        return false;
    }
    
    return load_from_json_object(root, config);
}

bool TransportConfigLoader::load_from_json_object(const Json::Value& json_obj, TransportConfig& config) {
    try {
        // Parse transport type
        if (!json_obj.isMember(FIELD_TRANSPORT_TYPE)) {
            std::cerr << "Missing required field: " << FIELD_TRANSPORT_TYPE << std::endl;
            return false;
        }
        
        std::string type_str = json_obj[FIELD_TRANSPORT_TYPE].asString();
        if (!parse_transport_type(type_str, config.type)) {
            std::cerr << "Invalid transport type: " << type_str << std::endl;
            return false;
        }
        
        // Parse based on transport type
        if (config.type == TransportType::FASTDDS) {
            config.topic_name = json_obj.get(FIELD_TOPIC_NAME, "video_frames").asString();
            config.domain_id = json_obj.get(FIELD_DOMAIN_ID, 0).asInt();
            config.max_samples = json_obj.get(FIELD_MAX_SAMPLES, 5).asInt();
            config.buffer_size = 0;
            config.ring_buffer_size = 0;
        } else if (config.type == TransportType::DMA || config.type == TransportType::SHMEM) {
            config.topic_name = json_obj.get(FIELD_SOCKET_PATH, "/tmp/video_transport.sock").asString();
            config.domain_id = 0;
            config.max_samples = 0;
            config.buffer_size = json_obj.get(FIELD_BUFFER_SIZE, 1920*1080*3).asUInt64();
            config.ring_buffer_size = json_obj.get(FIELD_RING_BUFFER_SIZE, 10).asUInt64();
        }
        
        config.timeout_ms = json_obj.get(FIELD_TIMEOUT_MS, 1000).asInt();
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Exception parsing transport config: " << e.what() << std::endl;
        return false;
    }
}

bool TransportConfigLoader::save_to_file(const std::string& config_file, const TransportConfig& config) {
    Json::Value json_obj = config_to_json_object(config);
    
    std::ofstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << config_file << std::endl;
        return false;
    }
    
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "  ";
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    
    writer->write(json_obj, &file);
    return true;
}

std::string TransportConfigLoader::config_to_json_string(const TransportConfig& config) {
    Json::Value json_obj = config_to_json_object(config);
    
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "  ";
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    
    std::ostringstream oss;
    writer->write(json_obj, &oss);
    return oss.str();
}

Json::Value TransportConfigLoader::config_to_json_object(const TransportConfig& config) {
    Json::Value json_obj;
    
    json_obj[FIELD_TRANSPORT_TYPE] = transport_type_to_string(config.type);
    json_obj[FIELD_TIMEOUT_MS] = config.timeout_ms;
    
    if (config.type == TransportType::FASTDDS) {
        json_obj[FIELD_TOPIC_NAME] = config.topic_name;
        json_obj[FIELD_DOMAIN_ID] = config.domain_id;
        json_obj[FIELD_MAX_SAMPLES] = config.max_samples;
    } else if (config.type == TransportType::DMA || config.type == TransportType::SHMEM) {
        json_obj[FIELD_SOCKET_PATH] = config.topic_name;
        json_obj[FIELD_BUFFER_SIZE] = static_cast<Json::UInt64>(config.buffer_size);
        json_obj[FIELD_RING_BUFFER_SIZE] = static_cast<Json::UInt64>(config.ring_buffer_size);
    }
    
    return json_obj;
}

TransportConfig TransportConfigLoader::create_default_fastdds_config(const std::string& topic_name) {
    return TransportConfig(TransportType::FASTDDS, topic_name, 0, 5, 1000);
}

TransportConfig TransportConfigLoader::create_default_dma_config(const std::string& socket_path) {
    return TransportConfig(TransportType::DMA, socket_path, 1920*1080*3, 10, 1000);
}

TransportConfig TransportConfigLoader::create_default_shmem_config(const std::string& socket_path) {
    return TransportConfig(TransportType::SHMEM, socket_path, 1920*1080*3, 10, 1000);
}

bool TransportConfigLoader::validate_config(const TransportConfig& config, std::string& error_msg) {
    if (config.type == TransportType::FASTDDS) {
        if (config.topic_name.empty()) {
            error_msg = "FastDDS topic name cannot be empty";
            return false;
        }
        if (config.domain_id < 0 || config.domain_id > 255) {
            error_msg = "FastDDS domain ID must be between 0 and 255";
            return false;
        }
        if (config.max_samples <= 0) {
            error_msg = "Max samples must be positive";
            return false;
        }
    } else if (config.type == TransportType::DMA || config.type == TransportType::SHMEM) {
        if (config.topic_name.empty()) {
            error_msg = "Socket path cannot be empty";
            return false;
        }
        if (config.buffer_size == 0) {
            error_msg = "Buffer size must be positive";
            return false;
        }
        if (config.ring_buffer_size == 0) {
            error_msg = "Ring buffer size must be positive";
            return false;
        }
    }
    
    if (config.timeout_ms < 0) {
        error_msg = "Timeout must be non-negative (-1 for infinite)";
        return false;
    }
    
    return true;
}

bool TransportConfigLoader::parse_transport_type(const std::string& type_str, TransportType& type) {
    if (type_str == "fastdds" || type_str == "FASTDDS" || type_str == "FastDDS") {
        type = TransportType::FASTDDS;
        return true;
    } else if (type_str == "dma" || type_str == "DMA") {
        type = TransportType::DMA;
        return true;
    } else if (type_str == "shmem" || type_str == "SHMEM") {
        type = TransportType::SHMEM;
        return true;
    }
    return false;
}

std::string TransportConfigLoader::transport_type_to_string(TransportType type) {
    switch (type) {
        case TransportType::FASTDDS:
            return "fastdds";
        case TransportType::DMA:
            return "dma";
        case TransportType::SHMEM:
            return "shmem";
        default:
            return "unknown";
    }
}

// ============================================================================
// VideoServiceConfigLoader Implementation
// ============================================================================

bool VideoServiceConfigLoader::load_from_file(const std::string& config_file, VideoServiceConfig& config) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Failed to open config file: " << config_file << std::endl;
        return false;
    }
    
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;
    
    if (!Json::parseFromStream(builder, file, &root, &errors)) {
        std::cerr << "Failed to parse JSON from file: " << errors << std::endl;
        return false;
    }
    
    try {
        // Clear existing configuration
        config.publishers.clear();
        config.subscribers.clear();
        
        // Load global configuration
        if (root.isMember("global")) {
            const Json::Value& global = root["global"];
            config.global.enable_stats_logging = global.get("enable_stats_logging", true).asBool();
            config.global.stats_logging_interval_ms = global.get("stats_logging_interval_ms", 5000).asInt();
            config.global.log_level = global.get("log_level", "INFO").asString();
            config.global.log_file = global.get("log_file", "").asString();
        }
        
        // Load publisher configurations
        if (root.isMember("publishers")) {
            const Json::Value& publishers = root["publishers"];
            if (publishers.isArray()) {
                for (const auto& pub_json : publishers) {
                    VideoServiceConfig::PublisherConfig pub_config;
                    if (load_publisher_config(pub_json, pub_config)) {
                        config.publishers.push_back(pub_config);
                    }
                }
            }
        }
        
        // Load subscriber configurations
        if (root.isMember("subscribers")) {
            const Json::Value& subscribers = root["subscribers"];
            if (subscribers.isArray()) {
                for (const auto& sub_json : subscribers) {
                    VideoServiceConfig::SubscriberConfig sub_config;
                    if (load_subscriber_config(sub_json, sub_config)) {
                        config.subscribers.push_back(sub_config);
                    }
                }
            }
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Exception parsing video service config: " << e.what() << std::endl;
        return false;
    }
}

bool VideoServiceConfigLoader::save_to_file(const std::string& config_file, const VideoServiceConfig& config) {
    Json::Value root;
    
    // Save global configuration
    Json::Value global;
    global["enable_stats_logging"] = config.global.enable_stats_logging;
    global["stats_logging_interval_ms"] = config.global.stats_logging_interval_ms;
    global["log_level"] = config.global.log_level;
    global["log_file"] = config.global.log_file;
    root["global"] = global;
    
    // Save publisher configurations
    Json::Value publishers(Json::arrayValue);
    for (const auto& pub_config : config.publishers) {
        publishers.append(publisher_config_to_json(pub_config));
    }
    root["publishers"] = publishers;
    
    // Save subscriber configurations
    Json::Value subscribers(Json::arrayValue);
    for (const auto& sub_config : config.subscribers) {
        subscribers.append(subscriber_config_to_json(sub_config));
    }
    root["subscribers"] = subscribers;
    
    std::ofstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << config_file << std::endl;
        return false;
    }
    
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "  ";
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    
    writer->write(root, &file);
    return true;
}

VideoServiceConfig VideoServiceConfigLoader::create_default_config() {
    VideoServiceConfig config;
    
    // Default global configuration
    config.global.enable_stats_logging = true;
    config.global.stats_logging_interval_ms = 5000;
    config.global.log_level = "INFO";
    config.global.log_file = "";
    
    // Default FastDDS publisher
    VideoServiceConfig::PublisherConfig fastdds_pub;
    fastdds_pub.enabled = true;
    fastdds_pub.name = "fastdds_publisher";
    fastdds_pub.description = "Default FastDDS video publisher";
    fastdds_pub.transport_config = TransportConfigLoader::create_default_fastdds_config("video_frames");
    config.publishers.push_back(fastdds_pub);
    
    // Default FastDDS subscriber
    VideoServiceConfig::SubscriberConfig fastdds_sub;
    fastdds_sub.enabled = true;
    fastdds_sub.name = "fastdds_subscriber";
    fastdds_sub.description = "Default FastDDS video subscriber";
    fastdds_sub.transport_config = TransportConfigLoader::create_default_fastdds_config("video_frames");
    fastdds_sub.subscribed_topics = {"video_frames"};
    config.subscribers.push_back(fastdds_sub);
    
    return config;
}

bool VideoServiceConfigLoader::validate_config(const VideoServiceConfig& config, std::string& error_msg) {
    // Validate publisher configurations
    for (size_t i = 0; i < config.publishers.size(); ++i) {
        const auto& pub_config = config.publishers[i];
        
        if (pub_config.name.empty()) {
            error_msg = "Publisher " + std::to_string(i) + " name cannot be empty";
            return false;
        }
        
        std::string transport_error;
        if (!TransportConfigLoader::validate_config(pub_config.transport_config, transport_error)) {
            error_msg = "Publisher " + pub_config.name + " transport config error: " + transport_error;
            return false;
        }
    }
    
    // Validate subscriber configurations
    for (size_t i = 0; i < config.subscribers.size(); ++i) {
        const auto& sub_config = config.subscribers[i];
        
        if (sub_config.name.empty()) {
            error_msg = "Subscriber " + std::to_string(i) + " name cannot be empty";
            return false;
        }
        
        std::string transport_error;
        if (!TransportConfigLoader::validate_config(sub_config.transport_config, transport_error)) {
            error_msg = "Subscriber " + sub_config.name + " transport config error: " + transport_error;
            return false;
        }
    }
    
    return true;
}

bool VideoServiceConfigLoader::load_publisher_config(const Json::Value& json_obj, VideoServiceConfig::PublisherConfig& config) {
    config.enabled = json_obj.get("enabled", true).asBool();
    config.name = json_obj.get("name", "").asString();
    config.description = json_obj.get("description", "").asString();
    
    if (json_obj.isMember("transport")) {
        return TransportConfigLoader::load_from_json_object(json_obj["transport"], config.transport_config);
    }
    
    return false;
}

bool VideoServiceConfigLoader::load_subscriber_config(const Json::Value& json_obj, VideoServiceConfig::SubscriberConfig& config) {
    config.enabled = json_obj.get("enabled", true).asBool();
    config.name = json_obj.get("name", "").asString();
    config.description = json_obj.get("description", "").asString();
    
    // Load subscribed topics list
    config.subscribed_topics.clear();
    if (json_obj.isMember("subscribed_topics")) {
        const Json::Value& topics = json_obj["subscribed_topics"];
        if (topics.isArray()) {
            for (const auto& topic : topics) {
                config.subscribed_topics.push_back(topic.asString());
            }
        }
    }
    
    if (json_obj.isMember("transport")) {
        return TransportConfigLoader::load_from_json_object(json_obj["transport"], config.transport_config);
    }
    
    return false;
}

Json::Value VideoServiceConfigLoader::publisher_config_to_json(const VideoServiceConfig::PublisherConfig& config) {
    Json::Value json_obj;
    json_obj["enabled"] = config.enabled;
    json_obj["name"] = config.name;
    json_obj["description"] = config.description;
    json_obj["transport"] = TransportConfigLoader::config_to_json_object(config.transport_config);
    return json_obj;
}

Json::Value VideoServiceConfigLoader::subscriber_config_to_json(const VideoServiceConfig::SubscriberConfig& config) {
    Json::Value json_obj;
    json_obj["enabled"] = config.enabled;
    json_obj["name"] = config.name;
    json_obj["description"] = config.description;
    
    Json::Value topics(Json::arrayValue);
    for (const auto& topic : config.subscribed_topics) {
        topics.append(topic);
    }
    json_obj["subscribed_topics"] = topics;
    
    json_obj["transport"] = TransportConfigLoader::config_to_json_object(config.transport_config);
    return json_obj;
}

// ============================================================================
// VideoConfigManager Implementation (Modern SessionParams JSON support)
// ============================================================================

bool VideoConfigManager::save_session_to_file(const std::string& config_file, const video_config::SessionParams& session) {
    Json::Value json_obj = session_to_json(session);
    
    std::ofstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << config_file << std::endl;
        return false;
    }
    
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "  ";
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    
    writer->write(json_obj, &file);
    return true;
}

bool VideoConfigManager::load_session_from_file(const std::string& config_file, video_config::SessionParams& session) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Failed to open config file: " << config_file << std::endl;
        return false;
    }
    
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;
    
    if (!Json::parseFromStream(builder, file, &root, &errors)) {
        std::cerr << "Failed to parse JSON from file: " << errors << std::endl;
        return false;
    }
    
    return json_to_session(root, session);
}

std::string VideoConfigManager::session_to_json_string(const video_config::SessionParams& session) {
    Json::Value json_obj = session_to_json(session);
    
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "  ";
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    
    std::ostringstream oss;
    writer->write(json_obj, &oss);
    return oss.str();
}

bool VideoConfigManager::session_from_json_string(const std::string& json_str, video_config::SessionParams& session) {
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;
    std::istringstream json_stream(json_str);
    
    if (!Json::parseFromStream(builder, json_stream, &root, &errors)) {
        std::cerr << "Failed to parse JSON string: " << errors << std::endl;
        return false;
    }
    
    return json_to_session(root, session);
}

Json::Value VideoConfigManager::session_to_json(const video_config::SessionParams& session) {
    Json::Value json_obj;
    
    json_obj["session_name"] = session.name;
    
    // V4L2 configuration
    Json::Value v4l2;
    v4l2["device_path"] = session.v4l2.device_path;
    v4l2["width"] = session.v4l2.width;
    v4l2["height"] = session.v4l2.height;
    v4l2["pixel_format"] = session.v4l2.pixel_format;
    v4l2["fps"] = session.v4l2.fps;
    v4l2["buffer_count"] = session.v4l2.buffer_count;
    v4l2["use_dmabuf"] = session.v4l2.use_dmabuf;
    v4l2["timeout_ms"] = session.v4l2.timeout_ms;
    json_obj["v4l2"] = v4l2;
    
    // Transport configuration
    Json::Value transport;
    transport["type"] = TransportConfigLoader::transport_type_to_string(session.transport.type);
    transport["topic_name"] = session.transport.topic_name;
    transport["timeout_ms"] = session.transport.timeout_ms;
    transport["domain_id"] = session.transport.domain_id;
    transport["max_samples"] = session.transport.max_samples;
    transport["buffer_size"] = static_cast<Json::UInt64>(session.transport.buffer_size);
    transport["ring_buffer_size"] = static_cast<Json::UInt64>(session.transport.ring_buffer_size);
    transport["socket_path"] = session.transport.socket_path;
    transport["shm_name"] = session.transport.shm_name;
    json_obj["transport"] = transport;
    
    return json_obj;
}

bool VideoConfigManager::json_to_session(const Json::Value& json, video_config::SessionParams& session) {
    try {
        session.name = json.get("session_name", "default_session").asString();
        
        // Load V4L2 configuration
        if (json.isMember("v4l2")) {
            const Json::Value& v4l2 = json["v4l2"];
            session.v4l2.device_path = v4l2.get("device_path", "/dev/video0").asString();
            session.v4l2.width = v4l2.get("width", 1920).asUInt();
            session.v4l2.height = v4l2.get("height", 1080).asUInt();
            session.v4l2.pixel_format = v4l2.get("pixel_format", V4L2_PIX_FMT_YUYV).asUInt();
            session.v4l2.fps = v4l2.get("fps", 30).asUInt();
            session.v4l2.buffer_count = v4l2.get("buffer_count", 4).asUInt();
            session.v4l2.use_dmabuf = v4l2.get("use_dmabuf", false).asBool();
            session.v4l2.timeout_ms = v4l2.get("timeout_ms", 1000).asInt();
        }
        
        // Load transport configuration
        if (json.isMember("transport")) {
            const Json::Value& transport = json["transport"];
            std::string type_str = transport.get("type", "FASTDDS").asString();
            TransportConfigLoader::parse_transport_type(type_str, session.transport.type);
            session.transport.topic_name = transport.get("topic_name", "video_frames").asString();
            session.transport.timeout_ms = transport.get("timeout_ms", 1000).asInt();
            session.transport.domain_id = transport.get("domain_id", 0).asInt();
            session.transport.max_samples = transport.get("max_samples", 10).asInt();
            session.transport.buffer_size = transport.get("buffer_size", 1920 * 1080 * 2).asUInt64();
            session.transport.ring_buffer_size = transport.get("ring_buffer_size", 8).asUInt64();
            session.transport.socket_path = transport.get("socket_path", "/tmp/video_transport.sock").asString();
            session.transport.shm_name = transport.get("shm_name", "video_service_shm").asString();
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Exception parsing session config: " << e.what() << std::endl;
        return false;
    }
}

} // namespace video_transport