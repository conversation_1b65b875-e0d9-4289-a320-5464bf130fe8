#ifndef VIDEO_TRANSPORT_INTERFACE_H
#define VIDEO_TRANSPORT_INTERFACE_H

#include "../common.h"
#include "buffer_manager.h"
#include <memory>
#include <functional>
#include <atomic>
#include <mutex>

namespace video_transport {

// 前向声明
using namespace buffer_manager;

// 传输类型
enum class TransportType {
    FASTDDS,        // FastDDS - 完整帧数据复制传输
    DMA,            // DMA缓冲区 - io_uring + Unix socket DMA传输
    SHMEM           // 共享内存 - io_uring + Unix socket SHMEM传输
};

// 传输配置
struct TransportConfig {
    TransportType type;
    std::string topic_name;
    
    // FastDDS specific
    int domain_id = 0;
    int max_samples = 10;
    int timeout_ms = 1000;
    
    // DMA specific
    size_t buffer_size = 0;      // DMA缓冲区大小
    size_t ring_buffer_size = 8; // 环形缓冲区大小
    
    // 构造函数
    TransportConfig(TransportType t, const std::string& name) 
        : type(t), topic_name(name) {}
    
    TransportConfig(TransportType t, const std::string& name, int domain, int samples, int timeout)
        : type(t), topic_name(name), domain_id(domain), max_samples(samples), timeout_ms(timeout) {}
        
    TransportConfig(TransportType t, const std::string& name, size_t buf_size, size_t ring_size, int timeout = 1000)
        : type(t), topic_name(name), buffer_size(buf_size), ring_buffer_size(ring_size), timeout_ms(timeout) {}
};

// 传输统计信息
struct TransportStats {
    std::atomic<uint64_t> frames_sent{0};
    std::atomic<uint64_t> frames_received{0};
    std::atomic<uint64_t> bytes_sent{0};
    std::atomic<uint64_t> bytes_received{0};
    std::atomic<uint64_t> dropped_frames{0};
    std::atomic<uint64_t> failed_operations{0};
    std::atomic<uint64_t> last_update_time{0};
};

// 缓冲区处理结果
enum class BufferResult {
    SUCCESS,
    BUFFER_NOT_AVAILABLE,
    TRANSPORT_ERROR,
    TIMEOUT,
    INVALID_DATA
};

// 缓冲区句柄 - 抽象化不同传输方式的缓冲区管理
struct BufferHandle {
    void* data = nullptr;                    // 数据指针
    size_t size = 0;                        // 缓冲区大小
    size_t used_size = 0;                   // 实际使用大小
    uint64_t buffer_id = 0;                 // 缓冲区ID
    FrameMetadata metadata;                 // 帧元数据
    
    // 传输特定数据
    union {
        struct {
            BufferManager::BufferSlot* slot;    // DMA缓冲区槽（原有系统）
            bool borrowed_from_v4l2;           // 是否从V4L2借出
        } dma;
        struct {
            std::vector<uint8_t>* frame_data;  // FastDDS帧数据
        } fastdds;
        void* ptr;                              // 通用指针（简化系统使用）
    } transport_data;
    
    TransportType transport_type;
    bool is_valid = false;
    
    BufferHandle() { memset(&transport_data, 0, sizeof(transport_data)); }
};

// 视频帧发布者接口
class IVideoPublisher {
public:
    virtual ~IVideoPublisher() = default;
    
    // 初始化
    virtual bool initialize(const TransportConfig& config) = 0;
    virtual void cleanup() = 0;
    
    // **Core Buffer Management - Only 2 methods needed**
    // 获取缓冲区用于填充帧数据
    virtual BufferResult acquire_buffer(BufferHandle& handle) = 0;
    
    // 发布已填充的缓冲区
    virtual BufferResult publish_buffer(BufferHandle& handle) = 0;
    
    // V4L2 DMABUF集成必需的DMA文件描述符访问
    virtual int get_dma_fd(const BufferHandle& handle) = 0;
    
    // 状态查询
    virtual bool has_subscribers() const = 0;
    virtual TransportStats get_stats() const = 0;
    virtual void reset_stats() = 0;
    virtual std::string get_status() const = 0;
    
    // **NEW: Check if this publisher supports V4L2 zero-copy integration**
    virtual bool supports_v4l2_zero_copy() const = 0;
};

// 视频帧订阅者接口
class IVideoSubscriber {
public:
    virtual ~IVideoSubscriber() = default;
    
    // 初始化
    virtual bool initialize(const TransportConfig& config) = 0;
    virtual void cleanup() = 0;
    
    // 接收并返回缓冲区 - 同步模式：receive -> process -> return
    virtual BufferResult receive_frame_buffer(BufferHandle& handle, int timeout_ms = 1000) = 0;
    virtual BufferResult return_frame_buffer(BufferHandle& handle) = 0;
    
    // 回调接口- 异步模式: receive -> process (自动return)
    virtual void set_buffer_callback(std::function<void(BufferHandle&)> callback) = 0;
    
    // 状态查询
    virtual bool is_connected() const = 0;
    virtual TransportStats get_stats() const = 0;
    virtual void reset_stats() = 0;
    virtual std::string get_status() const = 0;
};

// V4L2缓冲区适配器 - 将V4L2缓冲区包装为BufferHandle
class V4L2BufferAdapter {
public:
    // 将V4L2缓冲区转换为DMA BufferHandle（对于借出的V4L2缓冲区）
    static BufferResult wrap_v4l2_buffer(const V4L2Capture::V4L2Frame& v4l2_frame, 
                                        BufferHandle& handle) {
        // 注意：这里假设V4L2缓冲区已经是DMA缓冲区
        if (!v4l2_frame.buffer.is_valid) {
            return BufferResult::INVALID_DATA;
        }
        
        // 使用第一个平面（大多数情况下足够）
        if (v4l2_frame.buffer.planes.empty()) {
            return BufferResult::INVALID_DATA;
        }
        
        const auto& plane = v4l2_frame.buffer.planes[0];
        
        handle.data = plane.addr;
        handle.size = plane.size;
        handle.used_size = plane.bytes_used;
        handle.buffer_id = v4l2_frame.buffer.index;
        
        // 填充元数据
        handle.metadata.width = v4l2_frame.width;
        handle.metadata.height = v4l2_frame.height;
        handle.metadata.format = v4l2_frame.format;
        handle.metadata.timestamp = v4l2_frame.capture_time_us;
        handle.metadata.data_size = plane.bytes_used;
        
        handle.transport_data.dma.slot = nullptr; // V4L2缓冲区不是DMA管理器的槽
        handle.transport_data.dma.borrowed_from_v4l2 = true;
        handle.transport_type = TransportType::DMA;
        handle.is_valid = true;
        
        return BufferResult::SUCCESS;
    }
    
    // 从BufferHandle复制数据到V4L2缓冲区
    static BufferResult copy_to_v4l2_buffer(const BufferHandle& handle, 
                                           V4L2Capture::V4L2Frame& v4l2_frame) {
        if (!handle.is_valid || v4l2_frame.buffer.planes.empty()) {
            return BufferResult::INVALID_DATA;
        }
        
        auto& plane = v4l2_frame.buffer.planes[0];
        size_t copy_size = std::min(handle.used_size, plane.size);
        
        memcpy(plane.addr, handle.data, copy_size);
        plane.bytes_used = copy_size;
        
        // 更新帧信息
        v4l2_frame.width = handle.metadata.width;
        v4l2_frame.height = handle.metadata.height;
        v4l2_frame.format = handle.metadata.format;
        v4l2_frame.capture_time_us = handle.metadata.timestamp;
        
        return BufferResult::SUCCESS;
    }
};

// 工厂类
class VideoTransportFactory {
public:
    static std::unique_ptr<IVideoPublisher> create_publisher(TransportType type);
    static std::unique_ptr<IVideoSubscriber> create_subscriber(TransportType type);
    
    static std::unique_ptr<IVideoPublisher> create_publisher(const TransportConfig& config);
    static std::unique_ptr<IVideoSubscriber> create_subscriber(const TransportConfig& config);
    
    static std::vector<TransportType> get_supported_types();
};

} // namespace video_transport

#endif // VIDEO_TRANSPORT_INTERFACE_H