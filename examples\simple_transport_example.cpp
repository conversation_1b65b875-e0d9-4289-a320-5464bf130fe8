#include "../include/transport/video_transport_interface.h"
#include "../include/transport/video_transport_factory.h"
#include "../include/capture/v4l2_capture_interface.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace video_transport;
using namespace V4L2Capture;

/**
 * Video Transport Integration Examples
 * Demonstrates proper V4L2 integration with different transport types
 */

// ========================================
// Example 1: V4L2 -> FastDDS Network Transport
// ========================================

void v4l2_to_fastdds_example() {
    std::cout << "=== V4L2 -> FastDDS Network Transport Example ===" << std::endl;
    
    // 1. Create FastDDS publisher (no buffer provider needed for FastDDS)
    TransportConfig fastdds_config(TransportType::FASTDDS, "VideoTopic", 0, 10, 1000);
    auto publisher = VideoTransportFactory::create_publisher(fastdds_config);
    if (!publisher) {
        std::cerr << "Failed to create FastDDS publisher" << std::endl;
        return;
    }
    
    // 2. Create V4L2 device using factory method (MMAP mode for FastDDS)
    V4L2DeviceConfig v4l2_config;
    v4l2_config.device_path = "/dev/video0";
    v4l2_config.width = 1920;
    v4l2_config.height = 1080;
    v4l2_config.pixel_format = V4L2_PIX_FMT_YUYV;
    v4l2_config.fps = 30;
    v4l2_config.buffer_count = 4;
    v4l2_config.use_dmabuf = false;  // Use MMAP for FastDDS
    v4l2_config.buffer_provider = nullptr;  // No buffer provider needed for MMAP
    
    auto v4l2_device = V4L2DeviceFactory::create_and_configure(v4l2_config);
    if (!v4l2_device) {
        std::cerr << "Failed to create and configure V4L2 device" << std::endl;
        return;
    }
    
    v4l2_device->start_streaming();
    std::cout << "Started V4L2 capture and FastDDS publishing..." << std::endl;
    
    // 3. Capture and publish loop (data copy mode)
    for (int i = 0; i < 100; ++i) {
        V4L2Frame v4l2_frame;
        if (v4l2_device->capture_frame(v4l2_frame, 1000)) {
            // Convert V4L2 frame to fastdds::video::Frame
            fastdds::video::Frame dds_frame;
            dds_frame.width = v4l2_frame.width;
            dds_frame.height = v4l2_frame.height;
            dds_frame.format = v4l2_frame.format;
            dds_frame.timestamp = v4l2_frame.capture_time_us;
            
            // Copy data from V4L2 buffer to DDS frame
            size_t data_size = v4l2_frame.buffer.planes[0].bytes_used;
            dds_frame.data.resize(data_size);
            memcpy(dds_frame.data.data(), v4l2_frame.buffer.planes[0].addr, data_size);
            
            // Publish frame
            if (publisher->publish_frame(dds_frame)) {
                std::cout << "Published FastDDS frame " << i << ": " << v4l2_frame.width << "x" << v4l2_frame.height << std::endl;
            }
            
            v4l2_device->release_frame(v4l2_frame);
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(33)); // 30fps
    }
    
    std::cout << "FastDDS capture completed" << std::endl;
}

// ========================================
// Example 2: V4L2 -> DMA Zero-Copy Transport
// ========================================

void v4l2_to_dma_example() {
    std::cout << "=== V4L2 -> DMA Zero-Copy Transport Example ===" << std::endl;
    
    // 1. Create DMA transport publisher
    TransportConfig dma_config(TransportType::DMA, "/tmp/video_dma_stream", 1920*1080*2, 8, 1000);
    auto publisher = VideoTransportFactory::create_publisher(dma_config);
    if (!publisher) {
        std::cerr << "Failed to create DMA publisher" << std::endl;
        return;
    }
    
    // 2. Check if publisher supports V4L2 zero-copy
    if (!publisher->supports_v4l2_zero_copy()) {
        std::cerr << "Publisher does not support V4L2 zero-copy integration" << std::endl;
        return;
    }
    
    // 3. Create V4L2 device with DMA buffer publisher
    V4L2DeviceConfig v4l2_config;
    v4l2_config.device_path = "/dev/video0";
    v4l2_config.width = 1920;
    v4l2_config.height = 1080;
    v4l2_config.pixel_format = V4L2_PIX_FMT_YUYV;
    v4l2_config.fps = 30;
    v4l2_config.buffer_count = 4;
    v4l2_config.use_dmabuf = true;  // Use DMABUF for zero-copy
    v4l2_config.video_publisher = publisher.get();
    
    auto v4l2_device = V4L2DeviceFactory::create_and_configure(v4l2_config);
    if (!v4l2_device) {
        std::cerr << "Failed to create and configure V4L2 device with DMA" << std::endl;
        return;
    }
    
    v4l2_device->start_streaming();
    std::cout << "Started V4L2 capture with DMA transport..." << std::endl;
    
    // 4. Zero-copy capture and publish loop
    for (int i = 0; i < 100; ++i) {
        V4L2Frame v4l2_frame;
        if (v4l2_device->capture_frame(v4l2_frame, 1000)) {
            // Convert V4L2 frame to BufferHandle for transport
            BufferHandle handle;
            if (V4L2BufferAdapter::wrap_v4l2_buffer(v4l2_frame, handle) == BufferResult::SUCCESS) {
                // Publish using zero-copy
                if (publisher->publish_buffer(handle) == BufferResult::SUCCESS) {
                    std::cout << "Published DMA frame " << i << ": " << v4l2_frame.width << "x" << v4l2_frame.height << std::endl;
                }
            }
            
            v4l2_device->release_frame(v4l2_frame);
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(33)); // 30fps
    }
    
    std::cout << "DMA capture completed" << std::endl;
}

// ========================================
// Example 3: V4L2 -> SHMEM Transport (using memfd_create)
// ========================================

void v4l2_to_shmem_example() {
    std::cout << "=== V4L2 -> SHMEM Transport Example ===" << std::endl;
    
    // 1. Create SHMEM transport publisher (using memfd_create)
    TransportConfig shmem_config(TransportType::SHMEM, "/tmp/video_shmem_stream", 1920*1080*2, 8, 1000);
    auto publisher = VideoTransportFactory::create_publisher(shmem_config);
    if (!publisher) {
        std::cerr << "Failed to create SHMEM publisher" << std::endl;
        return;
    }
    
    // 2. Check if publisher supports V4L2 zero-copy
    if (!publisher->supports_v4l2_zero_copy()) {
        std::cerr << "Publisher does not support V4L2 zero-copy integration" << std::endl;
        return;
    }
    
    // 3. Create V4L2 device with SHMEM buffer publisher
    V4L2DeviceConfig v4l2_config;
    v4l2_config.device_path = "/dev/video0";
    v4l2_config.width = 1920;
    v4l2_config.height = 1080;
    v4l2_config.pixel_format = V4L2_PIX_FMT_YUYV;
    v4l2_config.fps = 30;
    v4l2_config.buffer_count = 4;
    v4l2_config.use_dmabuf = false; // Use MMAP mode, then copy to SHMEM for transport
    v4l2_config.video_publisher = publisher.get();
    
    auto v4l2_device = V4L2DeviceFactory::create_and_configure(v4l2_config);
    if (!v4l2_device) {
        std::cerr << "Failed to create and configure V4L2 device with SHMEM" << std::endl;
        return;
    }
    
    v4l2_device->start_streaming();
    std::cout << "Started V4L2 capture with SHMEM transport..." << std::endl;
    
    // 4. V4L2 MMAP capture and copy to SHMEM transport loop
    for (int i = 0; i < 100; ++i) {
        V4L2Frame v4l2_frame;
        if (v4l2_device->capture_frame(v4l2_frame, 1000)) {
            // Convert V4L2 frame to BufferHandle for transport
            BufferHandle handle;
            if (V4L2BufferAdapter::wrap_v4l2_buffer(v4l2_frame, handle) == BufferResult::SUCCESS) {
                // Publish using SHMEM distribution (copy-based)
                if (publisher->publish_buffer(handle) == BufferResult::SUCCESS) {
                    std::cout << "Distributed SHMEM frame " << i << ": " << v4l2_frame.width << "x" << v4l2_frame.height << std::endl;
                }
            }
            
            v4l2_device->release_frame(v4l2_frame);
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(33)); // 30fps
    }
    
    std::cout << "SHMEM capture completed" << std::endl;
}


// ========================================
// Example 4: FastDDS Consumer
// ========================================

void fastdds_consumer_example() {
    std::cout << "=== FastDDS Consumer Example ===" << std::endl;
    
    // Create FastDDS subscriber
    TransportConfig fastdds_config(TransportType::FASTDDS, "VideoTopic", 0, 10, 1000);
    auto subscriber = VideoTransportFactory::create_subscriber(fastdds_config);
    if (!subscriber) {
        std::cerr << "Failed to create FastDDS subscriber" << std::endl;
        return;
    }
    
    std::cout << "Started FastDDS frame reception..." << std::endl;
    
    // Receive frames synchronously
    for (int i = 0; i < 50; ++i) {
        fastdds::video::Frame frame;
        if (subscriber->receive_frame(frame, 1000)) {
            std::cout << "Received FastDDS frame " << i 
                     << ": " << frame.width << "x" << frame.height 
                     << ", data size: " << frame.data.size() << std::endl;
            
            // Process frame data
            // ... your processing logic ...
        }
    }
    
    std::cout << "FastDDS consumer completed" << std::endl;
}

// ========================================
// Example 5: DMA Consumer
// ========================================

void dma_consumer_example() {
    std::cout << "=== DMA Consumer Example ===" << std::endl;
    
    // Create DMA subscriber
    TransportConfig dma_config(TransportType::DMA, "/tmp/video_dma_stream", 1920*1080*2, 8, 1000);
    auto subscriber = VideoTransportFactory::create_subscriber(dma_config);
    if (!subscriber) {
        std::cerr << "Failed to create DMA subscriber" << std::endl;
        return;
    }
    
    std::cout << "Started DMA frame reception..." << std::endl;
    
    // Method 1: Synchronous reception
    for (int i = 0; i < 25; ++i) {
        BufferHandle handle;
        if (subscriber->receive_frame_buffer(handle, 1000) == BufferResult::SUCCESS) {
            std::cout << "Received DMA frame " << handle.buffer_id 
                     << ": " << handle.metadata.width << "x" << handle.metadata.height 
                     << ", data size: " << handle.used_size << std::endl;
                     
            // Process frame data (zero-copy access)
            // ... your processing logic ...
            
            // Return frame when done (important for zero-copy)
            subscriber->return_frame_buffer(handle);
        }
    }
    
    // Method 2: Asynchronous callback
    std::cout << "Switching to asynchronous mode..." << std::endl;
    
    std::atomic<int> received_count{0};
    
    subscriber->set_buffer_callback([&](BufferHandle& handle) {
        std::cout << "Callback received DMA frame " << handle.buffer_id 
                 << ": " << handle.metadata.width << "x" << handle.metadata.height << std::endl;
        
        // Process frame data
        // ... your processing logic ...
        
        received_count++;
        // Note: In callback mode, frame is automatically returned
    });
    
    // Wait for more frames
    while (received_count.load() < 25) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    std::cout << "DMA consumer completed, received " << received_count.load() << " frames" << std::endl;
}

// ========================================
// Example 6: Combined Usage - V4L2 + Multiple Consumers
// ========================================

void combined_example() {
    std::cout << "=== Combined Example: V4L2 + Multiple Consumers ===" << std::endl;
    
    // Producer thread: V4L2 capture -> DMA transport
    std::thread producer_thread([]() {
        TransportConfig dma_config(TransportType::DMA, "/tmp/multi_stream", 640*480*2, 8, 1000);
        auto publisher = VideoTransportFactory::create_publisher(dma_config);
        if (!publisher) return;
        
        if (!publisher->supports_v4l2_zero_copy()) return;
        
        V4L2DeviceConfig v4l2_config;
        v4l2_config.device_path = "/dev/video0";
        v4l2_config.width = 640;
        v4l2_config.height = 480;
        v4l2_config.pixel_format = V4L2_PIX_FMT_YUYV;
        v4l2_config.fps = 30;
        v4l2_config.buffer_count = 4;
        v4l2_config.use_dmabuf = true;
        v4l2_config.video_publisher = publisher.get();
        
        auto v4l2_device = V4L2DeviceFactory::create_and_configure(v4l2_config);
        if (!v4l2_device) return;
        
        v4l2_device->start_streaming();
        
        for (int i = 0; i < 100; ++i) {
            V4L2Frame v4l2_frame;
            if (v4l2_device->capture_frame(v4l2_frame)) {
                BufferHandle handle;
                if (V4L2BufferAdapter::wrap_v4l2_buffer(v4l2_frame, handle) == BufferResult::SUCCESS) {
                    publisher->publish_buffer(handle);
                }
                v4l2_device->release_frame(v4l2_frame);
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(33));
        }
    });
    
    // Consumer 1: AI processing
    std::thread ai_consumer_thread([]() {
        TransportConfig dma_config(TransportType::DMA, "/tmp/multi_stream", 0, 8, 1000);
        auto subscriber = VideoTransportFactory::create_subscriber(dma_config);
        if (!subscriber) return;
        
        subscriber->set_buffer_callback([](BufferHandle& handle) {
            std::cout << "[AI Processing] Frame " << handle.buffer_id 
                     << " - Simulating AI inference" << std::endl;
            // AI inference here...
        });
        
        std::this_thread::sleep_for(std::chrono::seconds(4));
    });
    
    // Consumer 2: Recording
    std::thread record_consumer_thread([]() {
        TransportConfig dma_config(TransportType::DMA, "/tmp/multi_stream", 0, 8, 1000);
        auto subscriber = VideoTransportFactory::create_subscriber(dma_config);
        if (!subscriber) return;
        
        subscriber->set_buffer_callback([](BufferHandle& handle) {
            std::cout << "[Recording] Frame " << handle.buffer_id 
                     << " - Simulating file write" << std::endl;
            // File writing here...
        });
        
        std::this_thread::sleep_for(std::chrono::seconds(4));
    });
    
    // Wait for all threads to complete
    producer_thread.join();
    ai_consumer_thread.join();
    record_consumer_thread.join();
    
    std::cout << "Combined example completed" << std::endl;
}

// ========================================
// Main Function
// ========================================

int main() {
    std::cout << "Video Transport Integration Examples" << std::endl;
    std::cout << "====================================" << std::endl;
    
    try {
        // Example 1: V4L2 -> FastDDS Network Transport
        std::cout << "\n1. V4L2 -> FastDDS Network Transport" << std::endl;
        v4l2_to_fastdds_example();
        
        // Example 2: V4L2 -> DMA Zero-Copy
        std::cout << "\n2. V4L2 -> DMA Zero-Copy Transport" << std::endl;
        v4l2_to_dma_example();
        
        // Example 3: V4L2 -> SHMEM Transport
        std::cout << "\n3. V4L2 -> SHMEM Transport" << std::endl;
        v4l2_to_shmem_example();
        
        // Example 4: FastDDS Consumer
        std::cout << "\n4. FastDDS Consumer Example" << std::endl;
        fastdds_consumer_example();
        
        // Example 5: DMA Consumer
        std::cout << "\n5. DMA Consumer Example" << std::endl;
        dma_consumer_example();
        
        // Example 6: Combined Usage
        std::cout << "\n6. Combined Example" << std::endl;
        combined_example();
        
        std::cout << "\nAll examples completed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}

/*
Compilation and Running:
g++ -std=c++17 -I. simple_transport_example.cpp \  
    ../src/transport/video_transport_factory.cpp \  
    -o simple_transport_example -pthread -luring -lfastdds
    
./simple_transport_example

Key Features Demonstrated:

1. **Proper V4L2 Integration**: 
   - Uses V4L2DeviceFactory::create_and_configure() with proper buffer provider setup
   - Demonstrates MMAP mode for FastDDS (data copy) vs DMABUF mode for DMA (zero-copy)
   - Shows proper resource management and error handling

2. **Three Transport Types**:
   - FastDDS: Network transport with data copying for compatibility
   - DMA: Hardware DMA buffers with zero-copy for high performance
   - SHMEM: Shared memory (memfd_create) with zero-copy for process isolation

3. **Zero-Copy Architecture**:
   - Both DMA and SHMEM use io_uring + Unix socket for fd transmission
   - V4L2 DMABUF buffers provided by transport layer
   - Direct buffer sharing between V4L2 and transport without copying
   - Proper buffer lifecycle management through IBufferProvider interface

4. **Multi-Consumer Support**:
   - Single producer (V4L2 capture) feeding multiple consumers
   - Each consumer receives zero-copy access to the same frame data
   - Automatic buffer return when all consumers finish processing

5. **Architectural Compliance**:
   - V4L2 capture uses IBufferProvider interface instead of direct DMA manager access
   - Proper separation of concerns between capture and transport layers
   - Clean abstraction that allows testing with mock buffer providers
*/