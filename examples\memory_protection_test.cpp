#include "include/transport/simplified_buffer_manager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <sys/mman.h>

using namespace simplified_transport;

void producer_thread(SimplifiedBufferManager* buffer_manager) {
    std::cout << "Producer: Acquiring buffer for writing\n";
    
    // Acquire buffer for writing
    auto* buffer = buffer_manager->acquire_buffer();
    if (!buffer) {
        std::cerr << "Producer: Failed to acquire buffer\n";
        return;
    }
    
    std::cout << "Producer: Buffer acquired, writing data\n";
    
    // Write data to buffer (this should work as we have write permission)
    char* data = static_cast<char*>(buffer->mapped_addr);
    for (size_t i = 0; i < std::min(buffer->size, size_t(100)); ++i) {
        data[i] = static_cast<char>(i % 256);
    }
    
    std::cout << "Producer: Data written, publishing buffer\n";
    
    // Publish buffer (this should make it read-only)
    buffer_manager->publish_buffer(buffer);
    
    std::cout << "Producer: <PERSON>uffer published, sleeping to allow consumer access\n";
    
    // Sleep to allow consumer to access
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Try to write again (this should fail or cause a segfault if protection is working)
    std::cout << "Producer: Attempting to write to published buffer (should be read-only)\n";
    try {
        data[0] = 'x';  // This might cause a segfault if protection is working
        std::cout << "Producer: Write succeeded (protection may not be working)\n";
    } catch (...) {
        std::cout << "Producer: Write failed as expected (protection is working)\n";
    }
    
    // Release buffer back to pool
    buffer_manager->release_buffer(buffer);
    std::cout << "Producer: Buffer released\n";
}

void consumer_thread(SimplifiedBufferManager* buffer_manager) {
    // Wait a bit for producer to start
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    std::cout << "Consumer: Trying to access buffer\n";
    
    // In a real scenario, we would receive a buffer from the producer
    // For this test, we'll simulate by finding a buffer
    // Note: In real usage, the consumer would receive the buffer via the transport mechanism
    
    std::cout << "Consumer: Reading data from buffer (should work)\n";
    
    // In a real implementation, we would have received a buffer
    // For this test, we're just demonstrating the concept
    
    std::cout << "Consumer: Read operation completed\n";
}

int main() {
    std::cout << "Memory Protection Test\n";
    std::cout << "=====================\n";
    
    try {
        // Create buffer manager
        SimplifiedBufferManager buffer_manager(BufferType::SHMEM, 1024, 2);
        
        std::cout << "Buffer manager created\n";
        
        // Get initial stats
        auto stats = buffer_manager.get_stats();
        std::cout << "Initial stats - Total: " << stats.total_buffers 
                  << ", Free: " << stats.free_buffers 
                  << ", Allocated: " << stats.allocated_buffers << "\n";
        
        // Create producer and consumer threads
        std::thread prod(producer_thread, &buffer_manager);
        std::thread cons(consumer_thread, &buffer_manager);
        
        // Wait for threads to complete
        prod.join();
        cons.join();
        
        // Get final stats
        stats = buffer_manager.get_stats();
        std::cout << "Final stats - Total: " << stats.total_buffers 
                  << ", Free: " << stats.free_buffers 
                  << ", Allocated: " << stats.allocated_buffers << "\n";
        
        std::cout << "Test completed successfully!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << "\n";
        return 1;
    }
    
    return 0;
}