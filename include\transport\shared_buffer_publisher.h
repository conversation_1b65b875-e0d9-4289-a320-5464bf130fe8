#ifndef SHARED_BUFFER_PUBLISHER_H
#define SHARED_BUFFER_PUBLISHER_H

#include <liburing.h>
#include <liburing/io_uring.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <atomic>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <thread>
#include <iostream>
#include <mutex>
#include <condition_variable>
#include <cstring>
#include <chrono>
#include <queue>
#include <functional>
#include <sstream>
#include "shared_buffer_manager.h"
#include "video_transport_interface.h"

namespace video_transport {
// 前向声明
class SharedBufferPublisher;
// 生产者服务器 - 实现 IVideoPublisher 接口
class SharedBufferPublisher : public IVideoPublisher {
public:
    enum class ConnectionState { ACCEPTING, CONNECTED, CLOSING };

    struct Connection {
        int fd;
        ConnectionState state;
        std::unordered_map<int, int> buffer_counts; // <ID, REFERENCE_COUNT>
    };
    
    enum class OperationType { ACCEPT, RECV, SEND, CLOSE };
    
    struct OperationData {
        OperationType type;
        int fd;
        MessagePool::Message* msg;
        BufferSlot* slot;
    };

    SharedBufferPublisher() : initialized_(false), has_active_subscribers_(false) {}
    ~SharedBufferPublisher() override { cleanup(); }
    
    // IVideoPublisher接口实现
    bool initialize(const TransportConfig& config) override;
    void cleanup() override;
    BufferResult acquire_buffer(BufferHandle& handle) override;
    BufferResult publish_buffer(BufferHandle& handle) override;
    int get_dma_fd(const BufferHandle& handle) override;
    bool has_subscribers() const override;
    TransportStats get_stats() const override;
    void reset_stats() override;
    std::string get_status() const override;
    
    // **NEW: 明确的V4L2集成支持**
    bool supports_v4l2_zero_copy() const override { return true; }

private:
    void setup_server_socket();
    void setup_io_uring();
    void process_events();
    void broadcast_frame(BufferSlot* slot);
    void submit_accept();
    void submit_send_frame(int client_fd, BufferSlot* slot);
    void submit_recv(int client_fd);
    void handle_accept(struct io_uring_cqe* cqe, OperationData* data);
    void handle_recv(struct io_uring_cqe* cqe, OperationData* data);
    void handle_send(struct io_uring_cqe* cqe, OperationData* data);
    void handle_close(struct io_uring_cqe* cqe, OperationData* data);
    void close_connection(int client_fd, OperationData* data);
    void update_stats_sent(size_t bytes, bool success);

    // 核心组件
    std::string socket_path_;
    SharedBufferManager buffer_manager_;
    MessagePool msg_pool_;
    
    // 网络和IO
    int server_fd_ = -1;
    io_uring ring_;
    std::unordered_map<int, Connection> connections_;
    std::thread event_thread_;
    std::atomic<bool> running_;
    std::atomic<bool> has_active_subscribers_;
    
    // Cleanup timing
    std::chrono::steady_clock::time_point last_cleanup_time_;
    static constexpr std::chrono::seconds CLEANUP_INTERVAL{5};  // Clean up every 5 seconds
    
    // IVideoPublisher required members
    TransportConfig config_;
    std::atomic<bool> initialized_;
    mutable std::mutex stats_mutex_;
    TransportStats stats_;
};

// ========================================
// SharedBufferPublisher 实现
// ========================================

inline bool SharedBufferPublisher::initialize(const TransportConfig& config) {
    if (initialized_.load()) {
        return true;
    }
    
    if (config.type != TransportType::DMA && config.type != TransportType::SHMEM) {
        return false;
    }
    
    try {
        config_ = config;
        socket_path_ = config.topic_name;
        
        // Determine buffer type based on transport type
        BufferType buffer_type = (config.type == TransportType::DMA) ? 
                               BufferType::DMA : BufferType::SHMEM;
        
        // Initialize core components with appropriate buffer type
        buffer_manager_ = SharedBufferManager(buffer_type, config.buffer_size, config.ring_buffer_size);
        msg_pool_ = MessagePool(config.ring_buffer_size);
        
        setup_server_socket();
        setup_io_uring();
        submit_accept();
        running_ = true;
        event_thread_ = std::thread(&SharedBufferPublisher::process_events, this);
        
        initialized_.store(true);
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

inline void SharedBufferPublisher::cleanup() {
    running_ = false;
    if (event_thread_.joinable()) {
        io_uring_submit_and_wait(&ring_, 1);
        event_thread_.join();
    }
    
    for (auto& [fd, conn] : connections_) close(fd);
    if (server_fd_ >= 0) {
        close(server_fd_);
        server_fd_ = -1;
    }
    if (!socket_path_.empty()) {
        unlink(socket_path_.c_str());
    }
    io_uring_queue_exit(&ring_);
    initialized_.store(false);
}

inline BufferResult SharedBufferPublisher::acquire_buffer(BufferHandle& handle) {
    if (!initialized_.load()) {
        return BufferResult::TRANSPORT_ERROR;
    }
    
    try {
        auto* slot = buffer_manager_.acquire_buffer();
        if (!slot) {
            return BufferResult::BUFFER_NOT_AVAILABLE;
        }
        
        // 设置BufferHandle
        handle.data = slot->addr;
        handle.size = slot->size;
        handle.used_size = 0;
        handle.buffer_id = slot->buffer_id;
        handle.transport_data.dma.slot = slot;
        handle.transport_data.dma.borrowed_from_v4l2 = false;
        handle.transport_type = (slot->type == BufferType::DMA) ? TransportType::DMA : TransportType::SHMEM;
        handle.is_valid = true;
        
        return BufferResult::SUCCESS;
    } catch (const std::exception& e) {
        return BufferResult::TRANSPORT_ERROR;
    }
}


inline int SharedBufferPublisher::get_dma_fd(const BufferHandle& handle) {
    if (!handle.is_valid || !handle.transport_data.dma.slot) {
        return -1;
    }
    return handle.transport_data.dma.slot->fd;
}

inline BufferResult SharedBufferPublisher::publish_buffer(BufferHandle& handle) {
    if (!handle.is_valid || !handle.transport_data.dma.slot) {
        return BufferResult::INVALID_DATA;
    }
    
    try {
        auto* slot = handle.transport_data.dma.slot;
        
        // 更新元数据
        slot->meta.data_size = handle.used_size;
        slot->meta.width = handle.metadata.width;
        slot->meta.height = handle.metadata.height;
        slot->meta.format = handle.metadata.format;
        slot->meta.timestamp = handle.metadata.timestamp;
        
        // 释放到buffer manager
        buffer_manager_.publish_buffer(slot);
        
        // 分发给所有连接的消费者
        broadcast_frame(slot);
        
        handle.is_valid = false;
        
        update_stats_sent(handle.used_size, true);
        return BufferResult::SUCCESS;
    } catch (const std::exception& e) {
        update_stats_sent(0, false);
        return BufferResult::TRANSPORT_ERROR;
    }
}

inline void SharedBufferPublisher::broadcast_frame(BufferSlot* slot) {
    if (!slot || connections_.empty()) {
        return;
    }
    
    // Send frame notification to all connected consumers
    for (const auto& [fd, conn] : connections_) {
        if (conn.state == ConnectionState::CONNECTED) {
            submit_send_frame(fd, slot);
        }
    }
}

inline void SharedBufferPublisher::submit_send_frame(int client_fd, BufferSlot* slot) {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;
    
    auto* msg = msg_pool_.acquire();
    auto* data = new OperationData{OperationType::SEND, client_fd, msg, slot};
    
    // Copy metadata to message (including buffer_id for confirmation)
    msg->meta = slot->meta;
    msg->meta.buffer_id = slot->buffer_id;  // Ensure buffer_id is set
    
    // Set DMA fd in control message
    struct cmsghdr* cmsg = CMSG_FIRSTHDR(&msg->hdr);
    *((int*)CMSG_DATA(cmsg)) = slot->fd;
    
    io_uring_prep_sendmsg(sqe, client_fd, &msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline bool SharedBufferPublisher::has_subscribers() const {
    return has_active_subscribers_.load();
}

inline TransportStats SharedBufferPublisher::get_stats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

inline void SharedBufferPublisher::reset_stats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = TransportStats{};
}

inline std::string SharedBufferPublisher::get_status() const {
    std::ostringstream oss;
    oss << "SharedBufferPublisher: " 
        << (initialized_.load() ? "initialized" : "not initialized")
        << ", subscribers: " << (has_active_subscribers_.load() ? "yes" : "no")
        << ", socket: " << socket_path_;
    return oss.str();
}

// Private method implementations for SharedBufferPublisher
inline void SharedBufferPublisher::setup_server_socket() {
    server_fd_ = socket(AF_UNIX, SOCK_SEQPACKET, 0);
    if (server_fd_ < 0) {
        throw std::runtime_error("Failed to create server socket");
    }
    
    // Remove existing socket file
    unlink(socket_path_.c_str());
    
    struct sockaddr_un addr = {};
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path)-1);
    
    if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        close(server_fd_);
        throw std::runtime_error("Failed to bind server socket");
    }
    
    if (listen(server_fd_, 10) < 0) {
        close(server_fd_);
        throw std::runtime_error("Failed to listen on server socket");
    }
}

inline void SharedBufferPublisher::setup_io_uring() {
    struct io_uring_params params = {};
    if (io_uring_queue_init_params(1024, &ring_, &params)) {
        throw std::runtime_error("Failed to initialize io_uring");
    }
}

inline void SharedBufferPublisher::submit_accept() {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;
    
    auto* data = new OperationData{OperationType::ACCEPT, server_fd_, nullptr, nullptr};
    
    io_uring_prep_accept(sqe, server_fd_, nullptr, nullptr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void SharedBufferPublisher::process_events() {
    last_cleanup_time_ = std::chrono::steady_clock::now();
    
    while (running_.load()) {
        struct io_uring_cqe* cqe;
        
        // Check for cleanup periodically
        auto now = std::chrono::steady_clock::now();
        if (now - last_cleanup_time_ >= CLEANUP_INTERVAL) {
            buffer_manager_.cleanup_unused_ready_buffers();
            last_cleanup_time_ = now;
        }
        
        int ret = io_uring_wait_cqe(&ring_, &cqe);
        
        if (ret < 0) {
            if (errno == EINTR) continue;
            break;
        }
        
        auto* data = static_cast<OperationData*>(io_uring_cqe_get_data(cqe));
        if (!data) {
            io_uring_cqe_seen(&ring_, cqe);
            continue;
        }
        
        switch (data->type) {
            case OperationType::ACCEPT:
                handle_accept(cqe, data);
                break;
            case OperationType::RECV:
                handle_recv(cqe, data);
                break;
            case OperationType::SEND:
                handle_send(cqe, data);
                break;
            case OperationType::CLOSE:
                handle_close(cqe, data);
                break;
        }
        
        io_uring_cqe_seen(&ring_, cqe);
    }
}

inline void SharedBufferPublisher::handle_accept(struct io_uring_cqe* cqe, OperationData* data) {
    int client_fd = cqe->res;
    delete data;
    
    if (client_fd < 0) {
        if (running_.load()) {
            submit_accept();  // Continue accepting
        }
        return;
    }
    
    // Add new connection
    connections_[client_fd] = {client_fd, ConnectionState::CONNECTED};
    has_active_subscribers_.store(true);
    
    // Start receiving from this client
    submit_recv(client_fd);
    
    // Continue accepting new connections
    if (running_.load()) {
        submit_accept();
    }
}

inline void SharedBufferPublisher::submit_recv(int client_fd) {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;
    
    auto* msg = msg_pool_.acquire();
    
    auto* data = new OperationData{OperationType::RECV, client_fd, msg, nullptr};
    
    io_uring_prep_recvmsg(sqe, client_fd, &msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void SharedBufferPublisher::handle_recv(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_received = cqe->res;
    int client_fd = data->fd;
    
    msg_pool_.release(data->msg);
    delete data;
    
    if (bytes_received <= 0) {
        // Client disconnected or error
        close_connection(client_fd, data);
        return;
    }
    
    // Handle release message
    if (bytes_received == sizeof(ReleaseMessage)) {
        ReleaseMessage release_msg;
        memcpy(&release_msg, data->msg->iov[0].iov_base, sizeof(ReleaseMessage));
        
        auto* slot = buffer_manager_.find_buffer(release_msg.buffer_id);
        if (slot) {
            // Decrease reference count for this buffer
            connections_[client_fd].buffer_counts[release_msg.buffer_id]--;
            buffer_manager_.release_from_reading(slot);
        }
    }
    // Continue receiving from this client
    submit_recv(client_fd);
}

inline void SharedBufferPublisher::handle_send(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_sent = cqe->res;
    int client_fd = data->fd;
    auto* slot = data->slot;
    
    msg_pool_.release(data->msg);
    delete data;
    
    if (bytes_sent < 0) {
        // Send failed, close connection
        close_connection(client_fd, data);
        update_stats_sent(0, false);
    } else {
        if (slot) {
            connections_[client_fd].buffer_counts[slot->buffer_id]++;  // increase reference count for this buffer, buffer_manager_.acquire_for_reading(slot) for each buffer_id in buffer_consumers.
            buffer_manager_.acquire_for_reading(slot);  // increase reference count for this buffer, buffer_manager_.acquire_for_reading(slot) for each buffer_id in buffer_consumers.
        }
        update_stats_sent(slot ? slot->meta.data_size : 0, true);
    }
}

inline void SharedBufferPublisher::handle_close(struct io_uring_cqe* cqe, OperationData* data) {
    int client_fd = data->fd;
    close_connection(client_fd, data);
    delete data;
}

inline void SharedBufferPublisher::close_connection(int client_fd, OperationData* data) {
    auto it = connections_.find(client_fd);
    if (it != connections_.end()) {
        // Clean up all buffer references for this consumer
        {
            auto buffer_it = it->second.buffer_counts.begin();  // buffer_consumers is a map of buffer_id to reference count for this consumer. buffer_it is an iterator to the first element in the map.
            while (buffer_it != it->second.buffer_counts.end()) {  // buffer_it is an iterator to the last element in the map.
                buffer_it = it->second.buffer_counts.erase(buffer_it);  // erase the current element from the map and return an iterator to the next element.
                buffer_manager_.release_from_reading(buffer_it->first, buffer_it->second); // decrease reference count for this buffer, release_from_reading(buffer_id, reference count)
            }
        }
        
        connections_.erase(it);
        close(client_fd);  // Close the client socket.
        
        // Update subscriber status
        has_active_subscribers_.store(!connections_.empty());
    }
}

inline void SharedBufferPublisher::update_stats_sent(size_t bytes, bool success) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (success) {
        stats_.frames_sent.fetch_add(1);
        stats_.bytes_sent.fetch_add(bytes);
    } else {
        stats_.failed_operations.fetch_add(1);
    }
    stats_.last_update_time.store(std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
}


} // namespace video_transport

#endif // BUFFER_SHARE_VIDEO_TRANSPORT_H