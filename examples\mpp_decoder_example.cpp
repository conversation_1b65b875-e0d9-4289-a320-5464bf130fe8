/**
 * MPP Decoder Usage Example
 * 
 * 展示新的MPP解码器API的使用方式：
 * - 每个解码器实例只支持一种编码格式
 * - 编码格式在构造时确定，中途不可修改
 * - 支持MJPEG、H264、H265格式
 * - 每个package都是完整的frame
 * - 禁用多线程，优化内存管理
 */

#include "mpp_decoder.h"
#include "common.h"
#include <iostream>
#include <vector>
#include <fstream>

// 示例：创建和使用MJPEG解码器
void example_mjpeg_decoder() {
    std::cout << "=== MJPEG Decoder Example ===" << std::endl;
    
    // 1. 创建MJPEG解码器（编码格式在构造时确定）
    MPPDecoder mjpeg_decoder(MPP_DECODER_TYPE_MJPEG);
    
    // 2. 初始化解码器（只需要指定分辨率）
    if (!mjpeg_decoder.init(1280, 720)) {
        std::cout << "Failed to initialize MJPEG decoder" << std::endl;
        return;
    }
    
    std::cout << "MJPEG decoder initialized successfully" << std::endl;
    std::cout << "Decoder type: " << mjpeg_decoder.get_decoder_type_name() << std::endl;
    
    // 3. 准备测试数据（实际使用中应该是真实的MJPEG数据）
    Frame input_frame;
    input_frame.width = 1280;
    input_frame.height = 720;
    input_frame.format = V4L2_PIX_FMT_MJPEG;
    input_frame.data.resize(1024);  // 模拟数据
    
    // 4. 解码
    Frame output_frame;
    if (mjpeg_decoder.decode_frame(input_frame, output_frame)) {
        std::cout << "Decode successful!" << std::endl;
        std::cout << "Output: " << output_frame.width << "x" << output_frame.height 
                  << ", format: 0x" << std::hex << output_frame.format << std::dec << std::endl;
    } else {
        std::cout << "Decode failed (expected without real MJPEG data)" << std::endl;
    }
    
    // 5. 清理资源（析构函数会自动调用，但也可以手动调用）
    mjpeg_decoder.cleanup();
    
    std::cout << std::endl;
}

// 示例：创建和使用H264解码器
void example_h264_decoder() {
    std::cout << "=== H264 Decoder Example ===" << std::endl;
    
    // 1. 创建H264解码器
    MPPDecoder h264_decoder(MPP_DECODER_TYPE_H264);
    
    // 2. 初始化解码器
    if (!h264_decoder.init(1920, 1080)) {
        std::cout << "Failed to initialize H264 decoder" << std::endl;
        return;
    }
    
    std::cout << "H264 decoder initialized successfully" << std::endl;
    std::cout << "Decoder type: " << h264_decoder.get_decoder_type_name() << std::endl;
    
    // 3. 准备测试数据
    Frame input_frame;
    input_frame.width = 1920;
    input_frame.height = 1080;
    input_frame.format = V4L2_PIX_FMT_H264;
    input_frame.data.resize(2048);  // 模拟数据
    
    // 4. 解码
    Frame output_frame;
    if (h264_decoder.decode_frame(input_frame, output_frame)) {
        std::cout << "Decode successful!" << std::endl;
        std::cout << "Output: " << output_frame.width << "x" << output_frame.height 
                  << ", format: 0x" << std::hex << output_frame.format << std::dec << std::endl;
    } else {
        std::cout << "Decode failed (expected without real H264 data)" << std::endl;
    }
    
    std::cout << std::endl;
}

// 示例：使用V4L2兼容接口
void example_v4l2_compatibility() {
    std::cout << "=== V4L2 Compatibility Example ===" << std::endl;
    
    // 1. 使用V4L2格式创建解码器
    MPPDecoder decoder(V4L2_PIX_FMT_H265);
    
    // 2. 检查解码器状态
    if (decoder.get_state() == MPP_DECODER_STATE_ERROR) {
        std::cout << "Failed to create decoder with V4L2 format" << std::endl;
        return;
    }
    
    std::cout << "Decoder created with V4L2 format" << std::endl;
    std::cout << "Decoder type: " << decoder.get_decoder_type_name() << std::endl;
    
    // 3. 初始化
    if (decoder.init(3840, 2160)) {
        std::cout << "H265 decoder initialized for 4K resolution" << std::endl;
    } else {
        std::cout << "Failed to initialize H265 decoder" << std::endl;
    }
    
    std::cout << std::endl;
}

// 示例：错误处理
void example_error_handling() {
    std::cout << "=== Error Handling Example ===" << std::endl;
    
    // 1. 尝试创建不支持的格式
    try {
        MPPDecoder unsupported_decoder(V4L2_PIX_FMT_YUYV);
        if (unsupported_decoder.get_state() == MPP_DECODER_STATE_ERROR) {
            std::cout << "✓ Unsupported format correctly rejected at construction" << std::endl;
        }
    } catch (...) {
        std::cout << "✓ Exception thrown for unsupported format" << std::endl;
    }
    
    // 2. 重复初始化
    MPPDecoder decoder(MPP_DECODER_TYPE_MJPEG);
    bool first_init = decoder.init(640, 480);
    bool second_init = decoder.init(1280, 720);  // 应该失败
    
    if (first_init && !second_init) {
        std::cout << "✓ Duplicate initialization correctly rejected" << std::endl;
    } else {
        std::cout << "✗ Duplicate initialization handling failed" << std::endl;
    }
    
    // 3. 无效分辨率
    MPPDecoder test_decoder(MPP_DECODER_TYPE_H264);
    bool invalid_init = test_decoder.init(0, 480);  // 无效宽度
    
    if (!invalid_init) {
        std::cout << "✓ Invalid resolution correctly rejected" << std::endl;
    } else {
        std::cout << "✗ Invalid resolution should be rejected" << std::endl;
    }
    
    std::cout << std::endl;
}

// 示例：性能监控
void example_performance_monitoring() {
    std::cout << "=== Performance Monitoring Example ===" << std::endl;
    
    MPPDecoder decoder(MPP_DECODER_TYPE_MJPEG);
    
    if (decoder.init(1280, 720)) {
        std::cout << "Decoder initialized successfully" << std::endl;
        
        // 模拟一些解码操作
        Frame input_frame, output_frame;
        input_frame.width = 1280;
        input_frame.height = 720;
        input_frame.format = V4L2_PIX_FMT_MJPEG;
        input_frame.data.resize(1024);
        
        for (int i = 0; i < 5; i++) {
            decoder.decode_frame(input_frame, output_frame);
        }
        
        // 获取性能信息
        std::cout << "Performance Statistics:" << std::endl;
        std::cout << "  Frame count: " << decoder.get_frame_count() << std::endl;
        std::cout << "  Max memory usage: " << decoder.get_max_memory_usage() << " bytes" << std::endl;
    }
    
    std::cout << std::endl;
}

int main() {
    std::cout << "MPP Decoder API Examples" << std::endl;
    std::cout << "========================" << std::endl;
    std::cout << std::endl;
    
    // 运行所有示例
    example_mjpeg_decoder();
    example_h264_decoder();
    example_v4l2_compatibility();
    example_error_handling();
    example_performance_monitoring();
    
    std::cout << "All examples completed!" << std::endl;
    std::cout << "Note: Decode operations may fail on systems without MPP hardware support." << std::endl;
    
    return 0;
}
