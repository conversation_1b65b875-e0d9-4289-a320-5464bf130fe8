# MPP Decoder MJPEG vs 非 MJPEG 处理差异

## 📋 概述

根据用户要求，对 `mpp_decoder.h` 进行了修改，以区分 MJPEG 和非 MJPEG 格式的不同缓冲区管理策略：

- **MJPEG**: 在初始化时设置 frame、buffer group 和 frame buf
- **非 MJPEG (H264/H265)**: 只在信息变化时设置 buffer group

## 🔄 主要修改

### 1. MJPEG 初始化时的缓冲区设置 ✅

#### 新增 setup_mjpeg_buffers 函数
```cpp
inline bool MPPDecoder::setup_mjpeg_buffers() {
    MPP_RET ret = MPP_OK;
    
    // 为 MJPEG 创建 buffer group
    ret = mpp_buffer_group_get_internal(&loop_data_.frm_grp, MPP_BUFFER_TYPE_ION);
    if (ret != MPP_OK) {
        LOG_E("Failed to get buffer group for MJPEG: %d", ret);
        return false;
    }
    
    // 为 MJPEG 分配 frame buffer
    RK_U32 hor_stride = MPP_ALIGN(loop_data_.width, 16);
    RK_U32 ver_stride = MPP_ALIGN(loop_data_.height, 16);
    RK_U32 buf_size = hor_stride * ver_stride * 4;  // 足够大的缓冲区
    
    ret = mpp_buffer_get(loop_data_.frm_grp, &loop_data_.frm_buf, buf_size);
    if (ret != MPP_OK) {
        LOG_E("Failed to get frame buffer for MJPEG: %d", ret);
        return false;
    }
    
    // 将 buffer 设置到 frame
    mpp_frame_set_buffer(loop_data_.frame, loop_data_.frm_buf);
    
    return true;
}
```

#### 在初始化时调用
```cpp
// 对于 MJPEG，需要在初始化时设置 buffer group 和 frame buf
if (decoder_type_ == MPP_DECODER_TYPE_MJPEG) {
    if (!setup_mjpeg_buffers()) {
        LOG_E("Failed to setup MJPEG buffers");
        return false;
    }
}
```

### 2. 信息变化处理的区分 ✅

#### 修改后的 handle_info_change 逻辑
```cpp
// 根据格式类型处理缓冲区设置
if (decoder_type_ == MPP_DECODER_TYPE_MJPEG) {
    // MJPEG 模式：缓冲区已在初始化时设置，这里不需要重新设置
    LOG_D("MJPEG mode: buffers already setup during initialization");
} else {
    // 非 MJPEG 模式（H264/H265）：只在信息变化时设置外部缓冲区组
    if (!setup_buffer_group(buf_size)) {
        LOG_E("Failed to setup buffer group for non-MJPEG");
        return false;
    }
    LOG_D("Non-MJPEG mode: external buffer group setup completed");
}
```

## 🔍 MJPEG vs 非 MJPEG 的关键差异

### MJPEG 特点
1. **预分配缓冲区**: 在初始化时就分配好所有必要的缓冲区
2. **固定大小**: 使用足够大的缓冲区（w*h*4）来处理不同的 YUV 格式
3. **静态管理**: 缓冲区在整个解码过程中保持不变
4. **内部绑定**: frame 和 buffer 在初始化时就绑定

### 非 MJPEG (H264/H265) 特点
1. **动态分配**: 只在信息变化时分配缓冲区
2. **按需大小**: 根据实际解码信息确定缓冲区大小
3. **动态管理**: 缓冲区可能在解码过程中重新分配
4. **外部管理**: 使用外部缓冲区组管理

## 📊 处理流程对比

| 阶段 | MJPEG | 非 MJPEG (H264/H265) |
|------|-------|----------------------|
| **初始化** | ✅ 创建 buffer group<br>✅ 分配 frame buffer<br>✅ 绑定 frame 和 buffer | ❌ 不分配缓冲区 |
| **信息变化** | ❌ 不重新设置缓冲区<br>📝 使用已有缓冲区 | ✅ 创建外部 buffer group<br>✅ 设置到解码器 |
| **解码过程** | 📝 使用预分配的缓冲区 | 📝 使用动态分配的缓冲区 |
| **清理** | ✅ 释放 buffer group 和 frame buffer | ✅ 释放 buffer group |

## 🎯 设计理由

### MJPEG 预分配的原因
1. **格式特性**: MJPEG 每帧都是独立的 JPEG 图像，格式相对固定
2. **性能考虑**: 避免频繁的缓冲区分配/释放
3. **内存稳定**: 预分配确保内存使用的可预测性
4. **兼容性**: 支持不同的 YUV 格式（420/422）

### 非 MJPEG 动态分配的原因
1. **格式变化**: H264/H265 可能在解码过程中改变分辨率或格式
2. **内存效率**: 只在需要时分配，节省内存
3. **灵活性**: 可以根据实际需要调整缓冲区大小
4. **标准做法**: 符合 MPP 对这些格式的标准处理方式

## 🔧 缓冲区大小计算

### MJPEG 缓冲区大小
```cpp
RK_U32 hor_stride = MPP_ALIGN(loop_data_.width, 16);
RK_U32 ver_stride = MPP_ALIGN(loop_data_.height, 16);
RK_U32 buf_size = hor_stride * ver_stride * 4;
```

**计算说明**:
- **对齐**: 宽高都对齐到 16 字节边界
- **倍数**: 使用 4 倍大小确保支持所有 YUV 格式
  - YUV420: 需要 w*h*1.5
  - YUV422: 需要 w*h*2
  - 安全系数: 使用 4 倍确保足够

### 非 MJPEG 缓冲区大小
```cpp
RK_U32 buf_size = mpp_frame_get_buf_size(frame);
```

**计算说明**:
- **动态获取**: 从解码器获取实际需要的大小
- **精确分配**: 只分配实际需要的内存
- **格式适应**: 自动适应不同的编码格式

## ⚠️ 注意事项

### 1. 内存使用
- **MJPEG**: 可能会过度分配内存，但保证兼容性
- **非 MJPEG**: 内存使用更精确，但需要处理动态变化

### 2. 性能影响
- **MJPEG**: 初始化时间稍长，但解码过程更稳定
- **非 MJPEG**: 初始化快，但信息变化时有额外开销

### 3. 错误处理
- **MJPEG**: 初始化失败会导致整个解码器不可用
- **非 MJPEG**: 信息变化时的失败可以重试

## 🧪 测试建议

### MJPEG 测试
```cpp
// 测试 MJPEG 初始化
MPPDecoder mjpeg_decoder(MPP_DECODER_TYPE_MJPEG);
bool init_result = mjpeg_decoder.init(1280, 720);
// 应该成功，并且缓冲区已分配

// 测试解码
Frame src, dst;
generate_test_mjpeg_frame(src, 1280, 720);
bool decode_result = mjpeg_decoder.decode_frame(src, dst);
```

### 非 MJPEG 测试
```cpp
// 测试 H264 初始化
MPPDecoder h264_decoder(MPP_DECODER_TYPE_H264);
bool init_result = h264_decoder.init(1920, 1080);
// 应该成功，但缓冲区未分配

// 测试解码（会触发信息变化）
Frame src, dst;
generate_test_h264_frame(src, 1920, 1080);
bool decode_result = h264_decoder.decode_frame(src, dst);
// 第一次解码会触发缓冲区分配
```

## 📁 相关文件

- **核心文件**: `include/mpp_decoder.h` - 完成所有修改
- **文档文件**: `docs/MPP_DECODER_MJPEG_VS_NON_MJPEG.md` - 本文档

## ✅ 修改清单

- [x] 添加 `setup_mjpeg_buffers()` 函数
- [x] MJPEG 在初始化时设置缓冲区
- [x] 修改 `handle_info_change()` 逻辑
- [x] 非 MJPEG 只在信息变化时设置缓冲区
- [x] 保持对外接口不变
- [x] 添加详细的日志输出
- [x] 错误处理逻辑完善

## 🎉 总结

通过区分 MJPEG 和非 MJPEG 的缓冲区管理策略，解码器现在能够：

1. **针对性优化**: 根据不同格式的特点采用最适合的缓冲区策略
2. **提高性能**: MJPEG 避免动态分配，非 MJPEG 节省内存
3. **增强稳定性**: 减少运行时的内存分配失败风险
4. **保持兼容**: 对外接口完全不变，现有代码无需修改

这种差异化处理方式更符合各种编码格式的特点，为不同类型的视频解码提供了更优化的解决方案。
