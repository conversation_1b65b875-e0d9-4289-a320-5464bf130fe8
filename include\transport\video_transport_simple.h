#ifndef VIDEO_TRANSPORT_SIMPLE_H
#define VIDEO_TRANSPORT_SIMPLE_H

#include "../common.h"
#include "dma_buffer_manager.h"
#include "../capture/v4l2_capture_interface.h"
#include <memory>
#include <functional>
#include <atomic>

namespace video_transport {

// 前向声明
using namespace buffer_manager;
using namespace V4L2Capture;

// 传输类型
enum class TransportType {
    FASTDDS,      // 完整帧复制传输
    DMA_ZERO_COPY // DMA零拷贝传输
};

// 传输配置 - 简化版本
struct TransportConfig {
    TransportType type;
    std::string topic_name;           // DDS topic 或 DMA socket路径
    
    // FastDDS配置
    int domain_id = 0;
    int max_samples = 10;
    
    // DMA配置  
    size_t buffer_size = 0;
    size_t buffer_count = 8;
    
    // 通用配置
    int timeout_ms = 1000;
    
    // 构造函数
    TransportConfig(TransportType t, const std::string& name) : type(t), topic_name(name) {}
};

// 视频帧数据 - 统一的帧表示
struct VideoFrame {
    uint64_t frame_id = 0;
    uint64_t timestamp_us = 0;
    uint32_t width = 0;
    uint32_t height = 0;
    uint32_t format = 0;             // V4L2格式
    
    // 数据访问
    void* data = nullptr;            // 数据指针
    size_t data_size = 0;            // 数据大小
    
    // 内部管理（用户不需要关心）
    struct {
        TransportType transport_type;
        BufferManager::BufferSlot* dma_slot = nullptr;
        std::vector<uint8_t>* fastdds_data = nullptr;
        bool borrowed = false;
    } _internal;
    
    bool is_valid() const { return data != nullptr && data_size > 0; }
};

// 简化的发布者接口
class VideoPublisher {
private:
    struct Impl;
    std::unique_ptr<Impl> impl_;

public:
    VideoPublisher();
    ~VideoPublisher();
    
    // 初始化
    bool initialize(const TransportConfig& config);
    void cleanup();
    
    // 主要接口 - 非常简单直观
    
    /**
     * 获取一个空的帧缓冲区用于填充数据
     * @param frame 输出的帧对象
     * @return 成功返回true
     */
    bool get_frame(VideoFrame& frame);
    
    /**
     * 发布已填充的帧
     * @param frame 要发布的帧
     * @return 成功返回true
     */
    bool publish(VideoFrame& frame);
    
    /**
     * 直接从V4L2帧发布（最常用的场景）
     * @param v4l2_frame V4L2捕获的帧
     * @return 成功返回true
     */
    bool publish_v4l2_frame(const V4L2Frame& v4l2_frame);
    
    /**
     * 直接发布原始数据（用于测试或非V4L2源）
     * @param data 帧数据
     * @param size 数据大小
     * @param width 帧宽度
     * @param height 帧高度
     * @param format 像素格式
     * @return 成功返回true
     */
    bool publish_raw_data(const void* data, size_t size, uint32_t width, uint32_t height, uint32_t format);
    
    // 状态查询
    bool has_subscribers() const;
    std::string get_status() const;
};

// 简化的订阅者接口
class VideoSubscriber {
private:
    struct Impl;
    std::unique_ptr<Impl> impl_;

public:
    VideoSubscriber();
    ~VideoSubscriber();
    
    // 初始化
    bool initialize(const TransportConfig& config);
    void cleanup();
    
    // 主要接口 - 非常简单直观
    
    /**
     * 接收一帧数据（阻塞式）
     * @param frame 输出的帧对象
     * @param timeout_ms 超时时间，-1表示永远等待
     * @return 成功返回true
     */
    bool receive(VideoFrame& frame, int timeout_ms = 1000);
    
    /**
     * 归还帧（用完后必须调用，对于零拷贝模式）
     * @param frame 要归还的帧
     */
    void return_frame(VideoFrame& frame);
    
    /**
     * 设置帧接收回调（异步模式）
     * @param callback 回调函数，接收帧后自动调用return_frame
     */
    void set_callback(std::function<void(const VideoFrame&)> callback);
    
    // 状态查询
    bool is_connected() const;
    std::string get_status() const;
};

// 超级简单的工厂函数
namespace simple {

/**
 * 创建DMA零拷贝发布者（用于V4L2捕获）
 * @param socket_path Unix域套接字路径
 * @param buffer_size 缓冲区大小（0=自动）
 * @return 发布者对象
 */
inline std::unique_ptr<VideoPublisher> create_dma_publisher(
    const std::string& socket_path, size_t buffer_size = 0) {
    auto publisher = std::make_unique<VideoPublisher>();
    TransportConfig config(TransportType::DMA_ZERO_COPY, socket_path);
    config.buffer_size = buffer_size;
    if (publisher->initialize(config)) {
        return publisher;
    }
    return nullptr;
}

/**
 * 创建DMA零拷贝订阅者
 * @param socket_path Unix域套接字路径
 * @return 订阅者对象
 */
inline std::unique_ptr<VideoSubscriber> create_dma_subscriber(
    const std::string& socket_path) {
    auto subscriber = std::make_unique<VideoSubscriber>();
    TransportConfig config(TransportType::DMA_ZERO_COPY, socket_path);
    if (subscriber->initialize(config)) {
        return subscriber;
    }
    return nullptr;
}

/**
 * 创建FastDDS发布者（用于网络传输）
 * @param topic_name DDS主题名
 * @param domain_id DDS域ID
 * @return 发布者对象
 */
inline std::unique_ptr<VideoPublisher> create_fastdds_publisher(
    const std::string& topic_name, int domain_id = 0) {
    auto publisher = std::make_unique<VideoPublisher>();
    TransportConfig config(TransportType::FASTDDS, topic_name);
    config.domain_id = domain_id;
    if (publisher->initialize(config)) {
        return publisher;
    }
    return nullptr;
}

/**
 * 创建FastDDS订阅者
 * @param topic_name DDS主题名
 * @param domain_id DDS域ID
 * @return 订阅者对象
 */
inline std::unique_ptr<VideoSubscriber> create_fastdds_subscriber(
    const std::string& topic_name, int domain_id = 0) {
    auto subscriber = std::make_unique<VideoSubscriber>();
    TransportConfig config(TransportType::FASTDDS, topic_name);
    config.domain_id = domain_id;
    if (subscriber->initialize(config)) {
        return subscriber;
    }
    return nullptr;
}

} // namespace simple

} // namespace video_transport

#endif // VIDEO_TRANSPORT_SIMPLE_H