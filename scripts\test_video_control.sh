#!/bin/bash

# Video Control Service Test Script
# This script tests the video_control service compilation and basic functionality

set -e

echo "=== Video Control Service Test Script ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}[OK]${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[WARN]${NC} $message"
    else
        echo -e "${RED}[ERROR]${NC} $message"
    fi
}

# Check if we're in the project root
if [ ! -f "CMakeLists.txt" ]; then
    print_status "ERROR" "Please run this script from the project root directory"
    exit 1
fi

print_status "OK" "Found project root directory"

# Check required files
echo -e "\n--- Checking Video Control Service Files ---"

required_files=(
    "include/video_control.h"
    "src/video_control.cpp"
    "src/video_control_main.cpp"
    "config/video_control.json"
    "test/test_video_control.cpp"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "OK" "Found $file"
    else
        print_status "ERROR" "Missing $file"
        exit 1
    fi
done

# Check dependencies
echo -e "\n--- Checking Dependencies ---"

# Check for GStreamer
if pkg-config --exists gstreamer-1.0; then
    gst_version=$(pkg-config --modversion gstreamer-1.0)
    print_status "OK" "GStreamer found: $gst_version"
else
    print_status "ERROR" "GStreamer not found"
    exit 1
fi

# Check for FastDDS
if pkg-config --exists fastdds; then
    fastdds_version=$(pkg-config --modversion fastdds)
    print_status "OK" "FastDDS found: $fastdds_version"
else
    print_status "WARN" "FastDDS not found via pkg-config (may still be available)"
fi

# Check for JsonCpp
if pkg-config --exists jsoncpp; then
    jsoncpp_version=$(pkg-config --modversion jsoncpp)
    print_status "OK" "JsonCpp found: $jsoncpp_version"
else
    print_status "ERROR" "JsonCpp not found"
    exit 1
fi

# Create build directory
echo -e "\n--- Setting up Build Environment ---"

if [ ! -d "build" ]; then
    mkdir build
    print_status "OK" "Created build directory"
else
    print_status "OK" "Build directory exists"
fi

cd build

# Configure with CMake
echo -e "\n--- Configuring with CMake ---"
if cmake .. -DCMAKE_BUILD_TYPE=Debug; then
    print_status "OK" "CMake configuration successful"
else
    print_status "ERROR" "CMake configuration failed"
    exit 1
fi

# Build the project
echo -e "\n--- Building Video Control Service ---"
if make video_control -j$(nproc); then
    print_status "OK" "Video Control Service built successfully"
else
    print_status "ERROR" "Build failed"
    exit 1
fi

# Check if executable was created
if [ -f "video_control" ]; then
    print_status "OK" "video_control executable created"
    
    # Get file size and permissions
    file_info=$(ls -lh video_control | awk '{print $5, $1}')
    print_status "OK" "Executable info: $file_info"
else
    print_status "ERROR" "video_control executable not found"
    exit 1
fi

# Test help output
echo -e "\n--- Testing Help Output ---"
if ./video_control --help > /dev/null 2>&1; then
    print_status "OK" "Help command works"
else
    print_status "WARN" "Help command failed (may be due to missing dependencies)"
fi

# Build test executable if possible
echo -e "\n--- Building Test Executable ---"
if make test_video_control -j$(nproc) 2>/dev/null; then
    print_status "OK" "Test executable built successfully"
    
    # Run basic tests
    echo -e "\n--- Running Basic Tests ---"
    if ./test_video_control; then
        print_status "OK" "Basic tests passed"
    else
        print_status "WARN" "Some tests failed (may be due to missing runtime dependencies)"
    fi
else
    print_status "WARN" "Test executable build failed (may be due to missing test dependencies)"
fi

# Check configuration file syntax
echo -e "\n--- Validating Configuration File ---"
cd ..
if python3 -m json.tool config/video_control.json > /dev/null 2>&1; then
    print_status "OK" "Configuration file JSON syntax is valid"
else
    print_status "ERROR" "Configuration file has invalid JSON syntax"
fi

# Create test directories
echo -e "\n--- Creating Test Directories ---"
test_dirs=(
    "/tmp/test_video_control"
    "/tmp/test_video_control/photos"
    "/tmp/test_video_control/videos"
)

for dir in "${test_dirs[@]}"; do
    if mkdir -p "$dir" 2>/dev/null; then
        print_status "OK" "Created test directory: $dir"
    else
        print_status "WARN" "Failed to create test directory: $dir"
    fi
done

# Test UDP port availability
echo -e "\n--- Testing UDP Port Availability ---"
test_port=14551
if netstat -ln 2>/dev/null | grep -q ":$test_port "; then
    print_status "WARN" "UDP port $test_port is already in use"
else
    print_status "OK" "UDP port $test_port is available"
fi

# Summary
echo -e "\n=== Test Summary ==="
print_status "OK" "Video Control Service compilation test completed"

echo -e "\nNext steps:"
echo "1. Install the service: sudo make install"
echo "2. Configure SD card mount point in config/video_control.json"
echo "3. Start the service: ./build/video_control"
echo "4. Test with Mavlink commands on UDP port 14551"

echo -e "\nFor debugging:"
echo "- Use --debug flag for verbose logging"
echo "- Check system logs: journalctl -f"
echo "- Monitor with: kill -USR1 \$(pidof video_control)"

echo -e "\nConfiguration file: config/video_control.json"
echo "Documentation: docs/video_control_service.md"

print_status "OK" "Test script completed successfully"
