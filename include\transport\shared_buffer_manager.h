#ifndef SIMPLIFIED_BUFFER_MANAGER_H
#define SIMPLIFIED_BUFFER_MANAGER_H

#include <sys/mman.h>
#include <fcntl.h>
#include <unistd.h>
#include <linux/dma-buf.h>
#include <atomic>
#include <vector>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <stdexcept>
#include <chrono>

// Forward declaration for memfd_create
int memfd_create(const char *name, unsigned int flags);

#ifndef MFD_CLOEXEC
#define MFD_CLOEXEC 0x0001U
#endif
#ifndef MFD_ALLOW_SEALING
#define MFD_ALLOW_SEALING 0x0002U
#endif
#ifndef F_ADD_SEALS
#define F_ADD_SEALS 1033
#endif
#ifndef F_SEAL_SHRINK
#define F_SEAL_SHRINK 0x0002
#endif
#ifndef F_SEAL_GROW
#define F_SEAL_GROW 0x0004
#endif
#ifndef F_SEAL_WRITE
#define F_SEAL_WRITE 0x0008
#endif

namespace video_transport {

// Buffer types
enum class BufferType {
    DMA,    // Hardware DMA buffer
    SHMEM   // Shared memory buffer
};

// Frame metadata for business logic
struct FrameMetadata {
    uint64_t buffer_id;     // Business logic buffer ID
    uint64_t timestamp;     // Frame timestamp (microseconds)
    uint32_t width;         // Frame width
    uint32_t height;        // Frame height
    uint32_t stride;        // Row stride
    uint32_t format;        // Pixel format
    uint32_t data_size;     // Valid data size
    BufferType type;        // Buffer type
};

struct BufferSlot {
    int fd;                         // File descriptor (kernel manages lifecycle)
    void* addr;                     // Mapped address
    size_t size;                    // Buffer size
    uint64_t buffer_id;             // Business logic ID
    BufferType type;                // Buffer type
    std::atomic<bool> ready;        // Simple ready flag for synchronization
    std::atomic<int> readers;       // Reader count for read/write coordination
    FrameMetadata meta;             // Business meta
    
    BufferSlot() : fd(-1), addr(nullptr), size(0), buffer_id(0), 
                    type(BufferType::SHMEM), ready(false), readers(0) {}
};

// Simplified buffer management - let kernel handle fd lifecycle
class SharedBufferManager {
public:
    SharedBufferManager(BufferType type, size_t buffer_size, size_t pool_size)
        : buffer_type_(type), buffer_size_(buffer_size), next_buffer_id_(1) {
        // Pre-allocate buffer pool
        for (size_t i = 0; i < pool_size; ++i) {
            auto buffer = std::make_unique<BufferSlot>();
            buffer->buffer_id = next_buffer_id_++;
            buffer->size = buffer_size;
            buffer->type = type;
            
            buffer_pool_.push_back(std::move(buffer));
            free_buffers_.push(buffer_pool_.back().get());
        }
    }

    ~SharedBufferManager() {
        cleanup();
    }

    // Acquire a free buffer for production with write protection
    BufferSlot* acquire_buffer() {
        std::unique_lock<std::mutex> lock(pool_mutex_);
        
        // Wait for available buffer
        pool_cv_.wait(lock, [this]() { return !free_buffers_.empty(); });
        
        auto* buffer = free_buffers_.front();
        free_buffers_.pop();
        
        // Lazy allocation - only allocate when actually needed
        if (buffer->fd == -1) {
            if (!allocate_buffer(buffer)) {
                // Allocation failed, return to pool
                free_buffers_.push(buffer);
                return nullptr;
            }
        }
        
        // Set up write protection: only producer can write
        buffer->ready.store(false);
        buffer->readers.store(0);      // No readers yet
        
        // Re-map with write permissions for producer
        if (!set_memory_protection(buffer, true, false)) {
            free_buffers_.push(buffer);
            return nullptr;
        }
        
        return buffer;
    }

    // Publish buffer as ready (producer done) with read-only protection
    void publish_buffer(BufferSlot* buffer) {
        if (!buffer) return;

        // Change memory protection to read-only for consumers
        set_memory_protection(buffer, false, true);
        
        // Mark as ready for consumers
        buffer->ready.store(true);
        
        // Notify potential waiters
        std::lock_guard<std::mutex> lock(pool_mutex_);
        pool_cv_.notify_all();
    }

    // Get buffer by ID (for remote release)
    BufferSlot* find_buffer(uint64_t buffer_id) {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        for (auto& buffer : buffer_pool_) {
            if (buffer->buffer_id == buffer_id) {
                return buffer.get();
            }
        }
        return nullptr;
    }

    // Acquire buffer for reading (consumer) - PUBLIC METHOD
    bool acquire_for_reading(BufferSlot* buffer) {
        if (!buffer || !buffer->ready.load()) {
            return false;
        }

        // Increment reader count atomically
        buffer->readers.fetch_add(1);
        return true;
    }
    
    // Release buffer from reading (consumer done) - PUBLIC METHOD
    void release_from_reading(BufferSlot* buffer) {
        if (!buffer) return;
        
        // Decrement reader count
        int remaining_readers = buffer->readers.fetch_sub(1) - 1;
        
        // If this was the last reader, buffer can be recycled
        if (remaining_readers == 0) {
            // All readers done - can safely recycle
            release_buffer(buffer);
        }
    }

    // Statistics
    struct Stats {
        size_t total_buffers;
        size_t free_buffers;
        size_t allocated_buffers;
    };

    Stats get_stats() const {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        Stats stats;
        stats.total_buffers = buffer_pool_.size();
        stats.free_buffers = free_buffers_.size();
        stats.allocated_buffers = stats.total_buffers - stats.free_buffers;
        return stats;
    }

private:
    bool allocate_buffer(BufferSlot* buffer) {
        if (buffer_type_ == BufferType::DMA) {
            buffer->fd = allocate_dma_buffer(buffer_size_);
        } else {
            buffer->fd = allocate_shmem_buffer(buffer_size_, &buffer->addr);
        }
        
        if (buffer->fd < 0) {
            return false;
        } else {
            return true;
        }
    }

    int allocate_dma_buffer(size_t size) {
        // Try to open DMA heap
        int heap_fd = open("/dev/dma_heap/system", O_RDWR | O_CLOEXEC);
        if (heap_fd >= 0) {
            struct dma_heap_allocation_data alloc = {
                .len = size,
                .fd_flags = O_RDWR | O_CLOEXEC,
                .heap_flags = 0,
                .fd = 0,
                .reserved = 0
            };
            
            if (ioctl(heap_fd, DMA_HEAP_IOCTL_ALLOC, &alloc) == 0) {
                close(heap_fd);
                return alloc.fd;
            }
            close(heap_fd);
        }
        
        // DMA allocation failed - do not fallback to SHMEM
        // Let the caller handle the failure appropriately
        return -1;
    }

    int allocate_shmem_buffer(size_t size, void **address) {
        int fd = memfd_create("video_buffer", MFD_CLOEXEC | MFD_ALLOW_SEALING);
        if (fd < 0) return -1;
        
        if (ftruncate(fd, size) < 0) {
            close(fd);
            return -1;
        }
        
        // Add sealing to prevent unauthorized modifications
        fcntl(fd, F_ADD_SEALS, F_SEAL_SHRINK | F_SEAL_GROW | F_SEAL_WRITE);
                    // Map memory
        int prot = PROT_READ | PROT_WRITE;
        address = mmap(nullptr, size, prot, MAP_SHARED, fd, 0);
        
        if (address == MAP_FAILED) {
            close(fd);
            return -1;
        } else {
            return fd;
        }
    }

    // Release buffer back to pool (consumer done)
    void release_buffer(BufferSlot* buffer) {
        if (!buffer) return;
        
        // Reset meta
        buffer->ready.store(false);
        buffer->readers.store(0);
        buffer->meta = FrameMetadata{};
        buffer->meta.buffer_id = buffer->buffer_id;
        buffer->meta.type = buffer->type;
        
        // Return to free pool
        std::lock_guard<std::mutex> lock(pool_mutex_);
        free_buffers_.push(buffer);
        pool_cv_.notify_one();
    }

    // Set memory protection for buffer (read-only or read-write) - PUBLIC METHOD
    bool set_memory_protection(BufferSlot* buffer, bool writable, bool readable) {

        if (!buffer) {
            return false;
        }

        if (buffer->type == BufferType::SHMEM) {
            if (!buffer->addr || buffer->addr == MAP_FAILED) {
                return false;
            }
            int prot = 0;
            if (writable) prot |= PROT_WRITE;
            if (readable) prot |= PROT_READ;
            return mprotect(buffer->addr, buffer->size, prot) == 0;
        } else if (buffer->type == BufferType::DMA) {
            struct dma_buf_sync sync = {0};
            sync.flags = DMA_BUF_SYNC_VALIDATE_RANGE;
            if (writable) sync.flags |= DMA_BUF_SYNC_END | DMA_BUF_SYNC_WRITE;
            if (readable) sync.flags |= DMA_BUF_SYNC_START | DMA_BUF_SYNC_READ;
            // 仅同步有效数据区域
            sync.offset = 0;
            sync.len = buffer->meta.data_size;
            
            if (ioctl(slot->fd, DMA_BUF_IOCTL_SYNC, &sync) < 0) {
                handle_sync_error(slot, "Consumer sync failed");
                return false;
            }
            return true;
        }
    }

    void cleanup() {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        
        for (auto& buffer : buffer_pool_) {
            if (buffer->addr && buffer->addr != MAP_FAILED) {
                munmap(buffer->addr, buffer->size);
            }
            if (buffer->fd >= 0) {
                close(buffer->fd);
            }
        }
        
        buffer_pool_.clear();
        // Clear queue
        std::queue<BufferSlot*> empty;
        free_buffers_.swap(empty);
    }

    const BufferType buffer_type_;
    const size_t buffer_size_;
    std::atomic<uint64_t> next_buffer_id_;
    
    std::vector<std::unique_ptr<BufferSlot>> buffer_pool_;
    std::queue<BufferSlot*> free_buffers_;
    mutable std::mutex pool_mutex_;
    std::condition_variable pool_cv_;

    // DMA heap allocation structure
    struct dma_heap_allocation_data {
        uint64_t len;
        uint32_t fd_flags;
        uint32_t heap_flags;
        uint32_t fd;
        uint32_t reserved;
    };

#ifndef DMA_HEAP_IOCTL_ALLOC
#define DMA_HEAP_IOCTL_ALLOC _IOWR('H', 0x0, struct dma_heap_allocation_data)
#endif
};

} // namespace simplified_transport

#endif // SIMPLIFIED_BUFFER_MANAGER_H