#!/bin/bash

# 30-Minute Dual Video Recording Test Script
# Tests the dual video control service with continuous recording for 30 minutes
# Monitors performance, creates segments, and validates results

set -e

echo "=== 30-Minute Dual Video Recording Test ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_DURATION_MIN=30
SEGMENT_DURATION_MIN=5
EXPECTED_SEGMENTS=6
TEST_PORT=14552
TEST_CONFIG="test/test_dual_video_config.json"
TEST_DIR="/tmp/test_dual_video_30min"
LOG_FILE="/tmp/dual_video_test_30min.log"

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}[$timestamp] [OK]${NC} $message" | tee -a "$LOG_FILE"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[$timestamp] [WARN]${NC} $message" | tee -a "$LOG_FILE"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}[$timestamp] [INFO]${NC} $message" | tee -a "$LOG_FILE"
    else
        echo -e "${RED}[$timestamp] [ERROR]${NC} $message" | tee -a "$LOG_FILE"
    fi
}

# Function to check disk space
check_disk_space() {
    local required_mb=$1
    local available_mb=$(df "$TEST_DIR" | awk 'NR==2 {print int($4/1024)}')
    
    if [ "$available_mb" -lt "$required_mb" ]; then
        print_status "ERROR" "Insufficient disk space. Required: ${required_mb}MB, Available: ${available_mb}MB"
        return 1
    else
        print_status "OK" "Sufficient disk space. Available: ${available_mb}MB"
        return 0
    fi
}

# Function to send Mavlink command
send_mavlink_command() {
    local command=$1
    local description=$2
    
    print_status "INFO" "Sending command: $description"
    
    # Use Python to send UDP command
    python3 -c "
import socket
import struct
import time

sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
try:
    packet = struct.pack('BB', $command, int(time.time()) % 256)
    sock.sendto(packet, ('127.0.0.1', $TEST_PORT))
    sock.settimeout(5.0)
    response, addr = sock.recvfrom(1024)
    print('Response received:', len(response), 'bytes')
except Exception as e:
    print('Command failed:', e)
finally:
    sock.close()
" 2>&1 | tee -a "$LOG_FILE"
}

# Function to monitor system resources
monitor_resources() {
    local pid=$1
    
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        local cpu_usage=$(ps -p "$pid" -o %cpu --no-headers | tr -d ' ')
        local mem_usage=$(ps -p "$pid" -o rss --no-headers | awk '{print int($1/1024)}')
        local disk_usage=$(du -sm "$TEST_DIR" 2>/dev/null | awk '{print $1}')
        
        echo "CPU: ${cpu_usage}%, Memory: ${mem_usage}MB, Disk Used: ${disk_usage}MB"
    else
        echo "Process not running"
    fi
}

# Cleanup function
cleanup() {
    print_status "INFO" "Cleaning up test environment..."
    
    # Stop video control service if running
    if [ -n "$SERVICE_PID" ] && kill -0 "$SERVICE_PID" 2>/dev/null; then
        print_status "INFO" "Stopping video control service (PID: $SERVICE_PID)"
        kill -TERM "$SERVICE_PID" 2>/dev/null || true
        sleep 3
        kill -KILL "$SERVICE_PID" 2>/dev/null || true
    fi
    
    # Archive test results
    if [ -d "$TEST_DIR" ]; then
        local archive_name="dual_video_test_$(date +%Y%m%d_%H%M%S).tar.gz"
        print_status "INFO" "Archiving test results to $archive_name"
        tar -czf "$archive_name" -C "$(dirname "$TEST_DIR")" "$(basename "$TEST_DIR")" "$LOG_FILE" 2>/dev/null || true
    fi
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Check if we're in the project root
if [ ! -f "CMakeLists.txt" ]; then
    print_status "ERROR" "Please run this script from the project root directory"
    exit 1
fi

print_status "OK" "Found project root directory"

# Check if executable exists
if [ ! -f "build/video_control" ]; then
    print_status "ERROR" "video_control executable not found. Please build the project first."
    exit 1
fi

print_status "OK" "Found video_control executable"

# Check test configuration
if [ ! -f "$TEST_CONFIG" ]; then
    print_status "ERROR" "Test configuration file not found: $TEST_CONFIG"
    exit 1
fi

print_status "OK" "Found test configuration file"

# Create test directory
mkdir -p "$TEST_DIR/photos" "$TEST_DIR/videos"
print_status "OK" "Created test directories"

# Check disk space (estimate 1GB needed)
if ! check_disk_space 1024; then
    exit 1
fi

# Initialize log file
echo "=== 30-Minute Dual Video Recording Test Log ===" > "$LOG_FILE"
echo "Start Time: $(date)" >> "$LOG_FILE"
echo "Test Configuration:" >> "$LOG_FILE"
echo "  Duration: $TEST_DURATION_MIN minutes" >> "$LOG_FILE"
echo "  Segment Duration: $SEGMENT_DURATION_MIN minutes" >> "$LOG_FILE"
echo "  Expected Segments: $EXPECTED_SEGMENTS per stream" >> "$LOG_FILE"
echo "  Visible Topic: main_video_frames" >> "$LOG_FILE"
echo "  Infrared Topic: thermal_video_frames" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

print_status "INFO" "Starting 30-minute dual video recording test"
print_status "INFO" "Test directory: $TEST_DIR"
print_status "INFO" "Log file: $LOG_FILE"

# Start video control service
print_status "INFO" "Starting video control service..."
./build/video_control --config "$TEST_CONFIG" --debug > "$TEST_DIR/service.log" 2>&1 &
SERVICE_PID=$!

print_status "OK" "Video control service started (PID: $SERVICE_PID)"

# Wait for service to initialize
sleep 5

# Check if service is still running
if ! kill -0 "$SERVICE_PID" 2>/dev/null; then
    print_status "ERROR" "Video control service failed to start"
    cat "$TEST_DIR/service.log"
    exit 1
fi

print_status "OK" "Video control service is running"

# Start recording
send_mavlink_command 2 "Start dual video recording"
sleep 2

# Monitor test progress
start_time=$(date +%s)
end_time=$((start_time + TEST_DURATION_MIN * 60))
next_photo_time=$((start_time + 300))  # First photo at 5 minutes
next_status_time=$((start_time + 600)) # First status at 10 minutes

print_status "INFO" "Test will run for $TEST_DURATION_MIN minutes until $(date -d @$end_time)"

while [ $(date +%s) -lt $end_time ]; do
    current_time=$(date +%s)
    elapsed_min=$(( (current_time - start_time) / 60 ))
    remaining_min=$(( TEST_DURATION_MIN - elapsed_min ))
    
    # Progress report every minute
    if [ $((current_time % 60)) -eq 0 ]; then
        print_status "INFO" "Progress: ${elapsed_min}/${TEST_DURATION_MIN} minutes (${remaining_min} remaining)"
        
        # Monitor resources
        resources=$(monitor_resources "$SERVICE_PID")
        print_status "INFO" "Resources: $resources"
        
        # Check video files
        visible_files=$(find "$TEST_DIR/videos" -name "video_visible_*.mp4" 2>/dev/null | wc -l)
        infrared_files=$(find "$TEST_DIR/videos" -name "video_infrared_*.mp4" 2>/dev/null | wc -l)
        print_status "INFO" "Video files: Visible=$visible_files, Infrared=$infrared_files"
        
        # Check service status
        if ! kill -0 "$SERVICE_PID" 2>/dev/null; then
            print_status "ERROR" "Video control service stopped unexpectedly!"
            exit 1
        fi
    fi
    
    # Take photo every 5 minutes
    if [ $current_time -ge $next_photo_time ]; then
        send_mavlink_command 1 "Take dual photos (minute $elapsed_min)"
        next_photo_time=$((next_photo_time + 300))
    fi
    
    # Get status every 10 minutes
    if [ $current_time -ge $next_status_time ]; then
        send_mavlink_command 4 "Get system status (minute $elapsed_min)"
        next_status_time=$((next_status_time + 600))
    fi
    
    sleep 1
done

# Stop recording
print_status "INFO" "Test duration completed, stopping recording..."
send_mavlink_command 3 "Stop dual video recording"
sleep 5

# Final status
send_mavlink_command 4 "Final system status"
sleep 2

# Stop service
print_status "INFO" "Stopping video control service..."
kill -TERM "$SERVICE_PID" 2>/dev/null || true
sleep 5

# Analyze results
print_status "INFO" "Analyzing test results..."

# Count files
visible_segments=$(find "$TEST_DIR/videos" -name "video_visible_*.mp4" 2>/dev/null | wc -l)
infrared_segments=$(find "$TEST_DIR/videos" -name "video_infrared_*.mp4" 2>/dev/null | wc -l)
visible_photos=$(find "$TEST_DIR/photos" -name "photo_visible_*.jpg" 2>/dev/null | wc -l)
infrared_photos=$(find "$TEST_DIR/photos" -name "photo_infrared_*.jpg" 2>/dev/null | wc -l)

# Calculate total size
total_size_mb=$(du -sm "$TEST_DIR" 2>/dev/null | awk '{print $1}')

# Print results
echo ""
print_status "INFO" "=== TEST RESULTS ==="
print_status "INFO" "Visible video segments: $visible_segments (expected: $EXPECTED_SEGMENTS)"
print_status "INFO" "Infrared video segments: $infrared_segments (expected: $EXPECTED_SEGMENTS)"
print_status "INFO" "Visible photos: $visible_photos"
print_status "INFO" "Infrared photos: $infrared_photos"
print_status "INFO" "Total data size: ${total_size_mb}MB"

# Validate results
test_passed=true

if [ "$visible_segments" -lt "$EXPECTED_SEGMENTS" ]; then
    print_status "ERROR" "Insufficient visible video segments"
    test_passed=false
fi

if [ "$infrared_segments" -lt "$EXPECTED_SEGMENTS" ]; then
    print_status "ERROR" "Insufficient infrared video segments"
    test_passed=false
fi

if [ "$visible_photos" -eq 0 ] || [ "$infrared_photos" -eq 0 ]; then
    print_status "ERROR" "Missing photo captures"
    test_passed=false
fi

if [ "$total_size_mb" -lt 100 ]; then
    print_status "ERROR" "Total data size too small, possible recording failure"
    test_passed=false
fi

# Final result
echo ""
if [ "$test_passed" = true ]; then
    print_status "OK" "🎉 30-MINUTE DUAL VIDEO TEST PASSED!"
    print_status "INFO" "All video segments created successfully"
    print_status "INFO" "Dual photo capture working"
    print_status "INFO" "Service remained stable throughout test"
else
    print_status "ERROR" "❌ 30-MINUTE DUAL VIDEO TEST FAILED!"
    print_status "ERROR" "Check logs and video files for issues"
fi

print_status "INFO" "Test completed at $(date)"
print_status "INFO" "Results archived and logs saved"

exit $([ "$test_passed" = true ] && echo 0 || echo 1)
