#ifndef SHARED_BUFFER_SUBSCRIBER_H
#define SHARED_BUFFER_SUBSCRIBER_H

#include <liburing.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <atomic>
#include <vector>
#include <unordered_map>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <cstring>
#include <chrono>
#include <queue>
#include <functional>
#include <sstream>
#include "shared_buffer_manager.h"
#include "video_transport_interface.h"
#include "../common.h"

namespace video_transport {

// 前向声明
class SharedBufferSubscriber;

// 消费者客户端 - 实现 IVideoSubscriber 接口
class SharedBufferSubscriber : public IVideoSubscriber {
public:

    struct MappedBufferInfo {
        void* addr;
        size_t size;
        int fd;
    };
    // 同步缓冲区管理的结构
    struct SyncBuffer {
        FrameMetadata metadata;
        int fd; // 缓冲区文件描述符 DMA or SHMEM
        SyncBuffer(const FrameMetadata& meta, int fd) : metadata(meta), fd(fd) {}
    };

    // SharedBuffer操作数据结构
    struct OperationData {
        enum Type { RECV, SEND } type;
        MessagePool::Message* msg;

        OperationData(Type t, MessagePool::Message* m) : type(t), msg(m) {}
    };


    SharedBufferSubscriber() : initialized_(false), connected_(false), running_(false), server_fd_(-1) {}
    ~SharedBufferSubscriber() override { cleanup(); }

    // IVideoSubscriber接口实现
    bool initialize(const TransportConfig& config) override;
    void cleanup() override;
    BufferResult receive_frame_buffer(BufferHandle& handle, int timeout_ms = 1000) override;
    BufferResult return_frame_buffer(BufferHandle& handle) override;
    void set_buffer_callback(std::function<void(BufferHandle&)> callback) override;
    bool is_connected() const override;
    TransportStats get_stats() const override;
    void reset_stats() override;
    std::string get_status() const override;

private:
    void setup_io_uring();
    void connect_to_server();
    void submit_recv();
    void submit_release(uint64_t buffer_id);
    void process_events();
    void handle_recv(struct io_uring_cqe* cqe, OperationData* data);
    void handle_send(struct io_uring_cqe* cqe, OperationData* data);
    void update_stats_received(size_t bytes, bool success);

    // 成员变量
    std::string socket_path_;
    int server_fd_;
    io_uring ring_;
    std::thread event_thread_;
    std::atomic<bool> running_;

    // IVideoSubscriber required members
    TransportConfig config_;
    std::atomic<bool> initialized_;
    std::atomic<bool> connected_;
    mutable std::mutex stats_mutex_;
    TransportStats stats_;
    std::function<void(BufferHandle&)> buffer_callback_;

    // 消息池
    MessagePool msg_pool_;

    // 同步缓冲区队列
    std::queue<SyncBuffer> pending_sync_buffers_;
    std::mutex sync_buffer_mutex_;
    std::condition_variable sync_buffer_cv_;

    // 映射的缓冲区记录 - 避免昂贵的mmap/munmap
    std::unordered_map<uint64_t, MappedBufferInfo> mapped_buffers_;
    std::mutex mapped_buffers_mutex_;
};

// ========================================
// SharedBufferSubscriber 实现
// ========================================

inline bool SharedBufferSubscriber::initialize(const TransportConfig& config) {
    if (initialized_.load()) {
        return true;
    }

    if (config.type != TransportType::DMA && config.type != TransportType::SHMEM) {
        return false;
    }

    try {
        config_ = config;
        socket_path_ = config.topic_name;

        connect_to_server();
        setup_io_uring();
        submit_recv();

        running_ = true;
        event_thread_ = std::thread(&SharedBufferSubscriber::process_events, this);

        initialized_.store(true);
        connected_.store(true);
        return true;
    } catch (const std::exception& e) {
        cleanup();
        return false;
    }
}

inline void SharedBufferSubscriber::cleanup() {
    if (!initialized_.load()) {
        return;
    }

    running_ = false;
    connected_.store(false);

    // 唤醒等待的线程
    sync_buffer_cv_.notify_all();

    if (event_thread_.joinable()) {
        event_thread_.join();
    }

    if (server_fd_ >= 0) {
        close(server_fd_);
        server_fd_ = -1;
    }

    // 清理所有持久映射的缓冲区 - 只有断开时才munmap
    {
        std::lock_guard<std::mutex> lock(mapped_buffers_mutex_);
        for (auto& [buffer_id, info] : mapped_buffers_) {
            if (info.addr && info.addr != MAP_FAILED) {
                munmap(info.addr, info.size);
            }
            if (info.fd >= 0) {
                close(info.fd);
            }
        }
        mapped_buffers_.clear();
    }

    io_uring_queue_exit(&ring_);
    initialized_.store(false);
}

inline BufferResult SharedBufferSubscriber::receive_frame_buffer(BufferHandle& handle, int timeout_ms) {
    if (!initialized_.load() || !connected_.load()) {
        return BufferResult::TRANSPORT_ERROR;
    }

    // 等待同步缓冲区
    std::unique_lock<std::mutex> lock(sync_buffer_mutex_);
    bool success = sync_buffer_cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                                           [this]() { return !pending_sync_buffers_.empty() || !connected_.load(); });

    if (!connected_.load()) {
        return BufferResult::TRANSPORT_ERROR;
    }

    if (!success || pending_sync_buffers_.empty()) {
        return BufferResult::TIMEOUT;
    }

    auto sync_buffer = pending_sync_buffers_.front();
    pending_sync_buffers_.pop();
    lock.unlock();

    // 检查是否已经映射此缓冲区（持久映射避免昂贵的mmap/munmap）
    void* mapped_addr = nullptr;
    {
        std::lock_guard<std::mutex> map_lock(mapped_buffers_mutex_);
        auto it = mapped_buffers_.find(sync_buffer.metadata.buffer_id);
        if (it != mapped_buffers_.end()) {
            // 缓冲区已映射，重用现有映射
            mapped_addr = it->second.addr;
            // 更新fd（通常不会发生，但为了安全）
            if (it->second.fd != sync_buffer.fd) {
                close(it->second.fd);
                it->second.fd = sync_buffer.fd;
            }
        } else {
            // 首次看到此缓冲区，创建持久映射
            mapped_addr = mmap(nullptr, sync_buffer.metadata.data_size,
                                PROT_READ, MAP_SHARED, sync_buffer.fd, 0);

            if (mapped_addr == MAP_FAILED) {
                close(sync_buffer.fd);
                return BufferResult::TRANSPORT_ERROR;
            }

            // 存储持久映射
            mapped_buffers_[sync_buffer.metadata.buffer_id] = {
                mapped_addr, sync_buffer.metadata.data_size, sync_buffer.fd
            };
        }
    }

    // 设置BufferHandle字段
    handle.data = mapped_addr;
    handle.size = sync_buffer.metadata.data_size;
    handle.used_size = sync_buffer.metadata.data_size;
    handle.buffer_id = sync_buffer.metadata.buffer_id;
    handle.metadata = sync_buffer.metadata;
    handle.transport_type = (sync_buffer.metadata.type == BufferType::DMA) ?
                           TransportType::DMA : TransportType::SHMEM;
    handle.transport_data.dma.slot = nullptr;
    handle.transport_data.dma.borrowed_from_v4l2 = false;
    handle.is_valid = true;

    return BufferResult::SUCCESS;
}

inline BufferResult SharedBufferSubscriber::return_frame_buffer(BufferHandle& handle) {
    if (!handle.is_valid) {
        return BufferResult::INVALID_DATA;
    }

    // 发送释放消息给生产者
    submit_release(handle.buffer_id);
    handle.is_valid = false;
    return BufferResult::SUCCESS;
}

inline void SharedBufferSubscriber::set_buffer_callback(std::function<void(BufferHandle&)> callback) {
    buffer_callback_ = std::move(callback);
}

inline bool SharedBufferSubscriber::is_connected() const {
    return connected_.load();
}

inline TransportStats SharedBufferSubscriber::get_stats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

inline void SharedBufferSubscriber::reset_stats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = TransportStats{};
}

inline std::string SharedBufferSubscriber::get_status() const {
    std::ostringstream oss;
    oss << "SharedBufferSubscriber: "
        << (initialized_.load() ? "initialized" : "not initialized")
        << ", connected: " << (connected_.load() ? "yes" : "no")
        << ", socket: " << socket_path_
        << ", mapped_buffers: " << mapped_buffers_.size();
    return oss.str();
}

// ========================================
// 私有方法实现
// ========================================

inline void SharedBufferSubscriber::setup_io_uring() {
    struct io_uring_params params = {};
    if (io_uring_queue_init_params(256, &ring_, &params) != 0) {
        throw std::runtime_error("Failed to initialize io_uring");
    }
}

inline void SharedBufferSubscriber::connect_to_server() {
    server_fd_ = socket(AF_UNIX, SOCK_SEQPACKET, 0);
    if (server_fd_ < 0) {
        throw std::runtime_error("Failed to create socket");
    }

    // 设置非阻塞模式
    int flags = fcntl(server_fd_, F_GETFL, 0);
    if (flags >= 0) {
        fcntl(server_fd_, F_SETFL, flags | O_NONBLOCK);
    }

    struct sockaddr_un addr = {};
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);

    if (connect(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        close(server_fd_);
        server_fd_ = -1;
        throw std::runtime_error("Failed to connect to server: " + socket_path_);
    }
}

inline void SharedBufferSubscriber::submit_recv() {
    if (!connected_.load()) return;

    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;

    auto* msg = msg_pool_.acquire();
    auto* data = new OperationData(OperationData::RECV, msg);

    io_uring_prep_recvmsg(sqe, server_fd_, &msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void SharedBufferSubscriber::submit_release(uint64_t buffer_id) {
    if (!connected_.load()) return;

    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;

    // 使用线程本地存储避免内存分配
    static thread_local ReleaseMessage release_msg;
    release_msg.buffer_id = buffer_id;
    release_msg.msg_type = 1;

    auto* data = new OperationData(OperationData::SEND, nullptr);

    io_uring_prep_send(sqe, server_fd_, &release_msg, sizeof(ReleaseMessage), 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void SharedBufferSubscriber::update_stats_received(size_t bytes, bool success) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (success) {
        stats_.frames_received.fetch_add(1);
        stats_.bytes_received.fetch_add(bytes);
    } else {
        stats_.failed_operations.fetch_add(1);
    }
    stats_.last_update_time.store(std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
}

inline void SharedBufferSubscriber::process_events() {
    while (running_.load()) {
        struct io_uring_cqe* cqe;
        int ret = io_uring_wait_cqe(&ring_, &cqe);

        if (ret < 0) {
            if (errno == EINTR) continue;
            if (errno == EAGAIN) continue;
            // 其他错误，退出循环
            break;
        }

        auto* data = static_cast<OperationData*>(io_uring_cqe_get_data(cqe));
        if (!data) {
            io_uring_cqe_seen(&ring_, cqe);
            continue;
        }

        switch (data->type) {
            case OperationData::RECV:
                handle_recv(cqe, data);
                break;
            case OperationData::SEND:
                handle_send(cqe, data);
                break;
        }

        io_uring_cqe_seen(&ring_, cqe);
    }
}

inline void SharedBufferSubscriber::handle_recv(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_received = cqe->res;
    auto* msg = data->msg;

    if (bytes_received <= 0) {
        // 连接丢失或错误
        connected_.store(false);
        msg_pool_.release(msg);
        delete data;
        return;
    }

    // 从控制消息中提取DMA fd
    struct cmsghdr* cmsg = CMSG_FIRSTHDR(&msg->hdr);
    if (cmsg && cmsg->cmsg_type == SCM_RIGHTS) {
        int dma_fd = *((int*)CMSG_DATA(cmsg));
    
        // 创建同步缓冲区并添加到队列
        {
            std::lock_guard<std::mutex> lock(sync_buffer_mutex_);
            pending_sync_buffers_.emplace(msg->meta, dma_fd);
        }
        sync_buffer_cv_.notify_one();

        if (buffer_callback_) {
            BufferHandle handle;
            if (receive_frame_buffer(handle, 0) == BufferResult::SUCCESS) {
                buffer_callback_(handle);
            }
            // 异步处理完，发送release通知给生产者
            return_frame_buffer(handle);
        }
        update_stats_received(msg->meta.data_size, true);
    } else {
        update_stats_received(0, false);
    }

    msg_pool_.release(msg);
    delete data;

    // 继续接收
    submit_recv();
}

inline void SharedBufferSubscriber::handle_send(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_sent = cqe->res;
    delete data;

    if (bytes_sent < 0) {
        // 发送失败，连接可能丢失
        connected_.store(false);
    }
    // 对于释放消息，不需要进一步操作
}



} // namespace video_transport

#endif // SHARED_BUFFER_SUBSCRIBER_H