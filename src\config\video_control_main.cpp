#include "video_control.h"
#include "config_loader.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>
#include <cstring>

// 全局变量
std::unique_ptr<VideoControlService> g_control_service;
std::atomic<bool> g_stop{false};

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            g_stop.store(true);
            break;
        case SIGUSR1:
        case SIGUSR2:
            if (g_control_service) {
                g_control_service->handle_signal(signal);
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -c, --config FILE     Configuration file (default: config/video_control.json)\n"
              << "  -s, --sdcard PATH     SD card mount path (default: /media/sdcard)\n"
              << "  --photo-path PATH     Photo save path (default: /media/sdcard/photos)\n"
              << "  --video-path PATH     Video save path (default: /media/sdcard/videos)\n"
              << "  --visible-topic TOPIC DDS visible stream topic (default: Video_Frames_Visible)\n"
              << "  --infrared-topic TOPIC DDS infrared stream topic (default: Video_Frames_Infrared)\n"
              << "  --segment-duration N  Video segment duration in minutes (default: 5)\n"
              << "  --visible-bitrate N   Visible stream bitrate in bps (default: 4000000)\n"
              << "  --infrared-bitrate N  Infrared stream bitrate in bps (default: 1000000)\n"
              << "  --status-interval N   Status report interval in seconds (default: 2)\n"
              << "  --debug               Enable debug logging\n"
              << "  --help                Show this help message\n"
              << "\n"
              << "Dual Video Control Service Features:\n"
              << "  1. Monitor SD card status (insertion, capacity, usage)\n"
              << "  2. Capture dual photos (visible + infrared) from DDS streams\n"
              << "  3. Record dual video streams with different resolutions/bitrates\n"
              << "  4. Visible: 1920x1080@25fps, 4Mbps H265 encoding\n"
              << "  5. Infrared: 640x512@30fps, 1Mbps H265 encoding\n"
              << "  6. Auto-segment videos (5-minute chunks) for data protection\n"
              << "  7. Report system status every 2 seconds\n"
              << "\n";
}

int main(int argc, char* argv[]) {
    // 默认配置
    VideoControlConfig config;
    
    // 配置文件路径
    std::string config_file = ConfigLoader::get_default_config_path("video_control");
    
    // 命令行参数解析
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"sdcard", required_argument, 0, 's'},
        {"photo-path", required_argument, 0, 1},
        {"video-path", required_argument, 0, 2},
        {"visible-topic", required_argument, 0, 3},
        {"infrared-topic", required_argument, 0, 4},
        {"segment-duration", required_argument, 0, 5},
        {"visible-bitrate", required_argument, 0, 6},
        {"infrared-bitrate", required_argument, 0, 7},
        {"status-interval", required_argument, 0, 8},
        {"debug", no_argument, 0, 'd'},
        {"help", no_argument, 0, 9},
        {0, 0, 0, 0}
    };
    
    // 第一次解析：获取配置文件路径
    int c;
    optind = 1;
    while ((c = getopt_long(argc, argv, "c:s:t:w:h:f:b:d", long_options, nullptr)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }
    
    // 加载配置文件
    if (!ConfigLoader::load_video_control_config(config_file, config)) {
        LOG_W("Failed to load config file: %s, using default settings", config_file.c_str());
    }
    
    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1;
    while ((c = getopt_long(argc, argv, "c:s:t:w:h:f:b:d", long_options, nullptr)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件已处理
                break;
            case 's':
                config.sdcard_mount_path = optarg;
                break;
            case 1:
                config.photo_save_path = optarg;
                break;
            case 2:
                config.video_save_path = optarg;
                break;
            case 3:
                config.visible_stream.dds_topic = optarg;
                break;
            case 4:
                config.infrared_stream.dds_topic = optarg;
                break;
            case 5:
                config.video_segment_duration_min = atoi(optarg);
                break;
            case 6:
                config.visible_stream.bitrate = atoi(optarg);
                break;
            case 7:
                config.infrared_stream.bitrate = atoi(optarg);
                break;
            case 8:
                config.status_report_interval_sec = atoi(optarg);
                break;
            case 'd':
                config.enable_debug = true;
                config.log_level = "DEBUG";
                break;
            case 9:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    if (config.video_segment_duration_min <= 0) {
        std::cerr << "Invalid segment duration: " << config.video_segment_duration_min << std::endl;
        return 1;
    }
    
    // 设置日志级别
    if (config.log_level == "DEBUG") {
        Logger::set_level(LEVEL_DEBUG);
    } else if (config.log_level == "INFO") {
        Logger::set_level(LEVEL_INFO);
    } else if (config.log_level == "WARN") {
        Logger::set_level(LEVEL_WARN);
    } else {
        Logger::set_level(LEVEL_ERROR);
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    
    LOG_I("Starting Dual Video Control Service...");
    LOG_I("SD Card: %s", config.sdcard_mount_path.c_str());
    LOG_I("Photo Path: %s", config.photo_save_path.c_str());
    LOG_I("Video Path: %s", config.video_save_path.c_str());
    LOG_I("Visible Stream: %s (%dx%d@%dfps, %d kbps, %s)",
          config.visible_stream.dds_topic.c_str(),
          config.visible_stream.width, config.visible_stream.height,
          config.visible_stream.fps, config.visible_stream.bitrate / 1000,
          config.visible_stream.codec.c_str());
    LOG_I("Infrared Stream: %s (%dx%d@%dfps, %d kbps, %s)",
          config.infrared_stream.dds_topic.c_str(),
          config.infrared_stream.width, config.infrared_stream.height,
          config.infrared_stream.fps, config.infrared_stream.bitrate / 1000,
          config.infrared_stream.codec.c_str());
    LOG_I("Segment Duration: %d minutes", config.video_segment_duration_min);
    
    int ret = 0;
    try {
        // 创建并初始化服务
        g_control_service = std::make_unique<VideoControlService>();
        if (!g_control_service->init(config)) {
            LOG_E("Failed to initialize video control service");
            ret = 1;
            goto exit_;
        }
        
        // 启动服务
        g_control_service->start();
        
        // 主循环 - 定期输出统计信息
        while (!g_stop.load()) {
            std::this_thread::sleep_for(std::chrono::seconds(config.stats_interval_sec));
            if (g_control_service && !g_stop.load()) {
                VideoControlService::Stats stats;
                g_control_service->get_stats(stats);
                SDCardStatus sd_status = g_control_service->get_sdcard_status();
                
                LOG_I("Stats - Dual Photos: %lu/%lu, Recording: %s, Segments: %lu",
                      stats.photos_taken, stats.photos_failed,
                      sd_status.is_recording ? "YES" : "NO", stats.video_segments_created);
                LOG_I("SD Card - Mounted: %s, Space: %lu/%lu MB (%.1f%% used), Data Written: %lu MB",
                      sd_status.is_mounted ? "YES" : "NO",
                      sd_status.used_capacity_mb, sd_status.total_capacity_mb,
                      sd_status.usage_percentage, stats.total_data_written_mb);
            }
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        ret = 1;
    }
    
exit_:
    LOG_I("Video Control Service stopped");
    if (g_control_service) {
        g_control_service->stop();
        g_control_service.reset();
    }
    return ret;
}
