#include "video_capture.h"
#include "config_loader.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>
#include <cstring>
#include <map>
#include <string>
#include <algorithm>
#include <linux/videodev2.h>

// 全局变量
std::unique_ptr<VideoCaptureService> g_capture_service;
std::atomic<bool> g_stop{false};

// V4L2像素格式转换 - 遵循v4l2-ctl工具的转换规则
// 将四字符格式名称转换为V4L2像素格式整数值
std::map<std::string, uint32_t> pixel_format_map = {
    // 常用YUV格式
    {"YUYV", V4L2_PIX_FMT_YUYV},    // 1448695129
    {"UYVY", V4L2_PIX_FMT_UYVY},    // 1498831189
    {"YUV4", V4L2_PIX_FMT_YUV420},  // 842093913
    {"YU12", V4L2_PIX_FMT_YUV420},  // 842093913 (alias)
    {"I420", V4L2_PIX_FMT_YUV420},  // 842093913 (alias)
    {"NV12", V4L2_PIX_FMT_NV12},    // 842094158
    {"NV21", V4L2_PIX_FMT_NV21},    // 825382478
    {"YV12", V4L2_PIX_FMT_YVU420},  // 842094169

    // RGB格式
    {"RGB3", V4L2_PIX_FMT_RGB24},   // 859981650
    {"BGR3", V4L2_PIX_FMT_BGR24},   // 861030210
    {"RGBP", V4L2_PIX_FMT_RGB565},  // 1346520914
    {"RGB4", V4L2_PIX_FMT_RGB32},   // 876758866
    {"BGR4", V4L2_PIX_FMT_BGR32},   // 877807426

    // 压缩格式
    {"MJPG", V4L2_PIX_FMT_MJPEG},   // 1196444237
    {"JPEG", V4L2_PIX_FMT_JPEG},    // 1195724874
    {"H264", V4L2_PIX_FMT_H264},    // 875967048
    {"H265", V4L2_PIX_FMT_H265},    // 1211250229
    {"HEVC", V4L2_PIX_FMT_HEVC},    // 1129727304 (alias)
    {"VP8 ", V4L2_PIX_FMT_VP8},     // 808996950
    {"VP9 ", V4L2_PIX_FMT_VP9},     // 808997206

    // 其他格式
    {"GREY", V4L2_PIX_FMT_GREY},    // 1497715271 (8位灰度)
    {"GRAY", V4L2_PIX_FMT_GREY},    // 1497715271 (8位灰度，别名)
    {"Y8  ", V4L2_PIX_FMT_GREY},    // 1497715271 (8位灰度，别名)
    {"Y16 ", V4L2_PIX_FMT_Y16},     // 540422489 (16位灰度)
    {"BA81", V4L2_PIX_FMT_SBGGR8},  // 825770306 (Bayer)
    {"GBRG", V4L2_PIX_FMT_SGBRG8},  // 1196573255 (Bayer)
    {"GRBG", V4L2_PIX_FMT_SGRBG8},  // 1111967575 (Bayer)
    {"RGGB", V4L2_PIX_FMT_SRGGB8},  // 1111967570 (Bayer)
};

// 将四字符格式字符串转换为V4L2像素格式整数
uint32_t fourcc_to_pixelformat(const std::string& fourcc) {
    // 转换为大写
    std::string upper_fourcc = fourcc;
    std::transform(upper_fourcc.begin(), upper_fourcc.end(), upper_fourcc.begin(), ::toupper);

    // 查找预定义映射
    auto it = pixel_format_map.find(upper_fourcc);
    if (it != pixel_format_map.end()) {
        return it->second;
    }

    // 如果没有找到预定义映射，按照v4l2-ctl的规则直接转换
    // 四字符字符串转换为32位整数（小端序）
    if (fourcc.length() == 4) {
        return (static_cast<uint32_t>(fourcc[0]) << 0) |
               (static_cast<uint32_t>(fourcc[1]) << 8) |
               (static_cast<uint32_t>(fourcc[2]) << 16) |
               (static_cast<uint32_t>(fourcc[3]) << 24);
    }

    return 0; // 无效格式
}

// 将V4L2像素格式整数转换为四字符字符串（用于显示）
std::string pixelformat_to_fourcc(uint32_t pixelformat) {
    char fourcc[5] = {0};
    fourcc[0] = static_cast<char>((pixelformat >> 0) & 0xFF);
    fourcc[1] = static_cast<char>((pixelformat >> 8) & 0xFF);
    fourcc[2] = static_cast<char>((pixelformat >> 16) & 0xFF);
    fourcc[3] = static_cast<char>((pixelformat >> 24) & 0xFF);
    return std::string(fourcc);
}

// 显示所有支持的格式
void print_supported_formats() {
    std::cout << "Supported pixel formats:\n";
    std::cout << "Format  Integer Value  Description\n";
    std::cout << "------  -------------  -----------\n";

    // 按类别显示格式
    std::cout << "\nYUV Formats:\n";
    std::cout << "YUYV    " << V4L2_PIX_FMT_YUYV << "  YUV 4:2:2 (YUYV)\n";
    std::cout << "UYVY    " << V4L2_PIX_FMT_UYVY << "  YUV 4:2:2 (UYVY)\n";
    std::cout << "YUV4    " << V4L2_PIX_FMT_YUV420 << "   YUV 4:2:0 planar\n";
    std::cout << "NV12    " << V4L2_PIX_FMT_NV12 << "   YUV 4:2:0 (NV12)\n";
    std::cout << "NV21    " << V4L2_PIX_FMT_NV21 << "   YUV 4:2:0 (NV21)\n";

    std::cout << "\nRGB Formats:\n";
    std::cout << "RGB3    " << V4L2_PIX_FMT_RGB24 << "   24-bit RGB\n";
    std::cout << "BGR3    " << V4L2_PIX_FMT_BGR24 << "   24-bit BGR\n";
    std::cout << "RGB4    " << V4L2_PIX_FMT_RGB32 << "   32-bit RGB\n";
    std::cout << "BGR4    " << V4L2_PIX_FMT_BGR32 << "   32-bit BGR\n";

    std::cout << "\nCompressed Formats:\n";
    std::cout << "MJPG    " << V4L2_PIX_FMT_MJPEG << "  Motion JPEG\n";
    std::cout << "H264    " << V4L2_PIX_FMT_H264 << "   H.264\n";
    std::cout << "H265    " << V4L2_PIX_FMT_H265 << "  H.265/HEVC\n";

    std::cout << "\nOther Formats:\n";
    std::cout << "GREY    " << V4L2_PIX_FMT_GREY << "  8-bit Greyscale\n";

    std::cout << "\nUsage examples:\n";
    std::cout << "  --format YUYV     (use fourcc string)\n";
    std::cout << "  --format " << V4L2_PIX_FMT_YUYV << "  (use integer value)\n";
    std::cout << "  --format 0        (auto-select)\n";
}

// 解析格式参数（支持整数和四字符字符串）
uint32_t parse_format_parameter(const char* optarg) {
    if (!optarg) return 0;

    std::string format_str(optarg);

    // 特殊处理：显示支持的格式
    if (format_str == "help" || format_str == "list") {
        print_supported_formats();
        exit(0);
    }

    // 尝试解析为整数
    char* endptr;
    long format_int = strtol(optarg, &endptr, 0);
    if (*endptr == '\0' && format_int >= 0) {
        // 成功解析为整数
        return static_cast<uint32_t>(format_int);
    }

    // 尝试解析为四字符格式
    uint32_t format_fourcc = fourcc_to_pixelformat(format_str);
    if (format_fourcc != 0) {
        return format_fourcc;
    }

    // 解析失败
    std::cerr << "Invalid format: " << optarg << std::endl;
    std::cerr << "Use --format help to see all supported formats" << std::endl;
    return 0;
}

// 信号处理函数 - 只设置标志，避免在信号处理中进行复杂操作
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            // 只设置停止标志，让主线程处理实际的停止逻辑
            g_stop.store(true);
            break;
        case SIGUSR1:
        case SIGUSR2:
            if (g_capture_service) {
                g_capture_service->handle_signal(signal);
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -c, --config FILE     Configuration file (default: config/video_capture.json)\n"
              << "  -s, --source TYPE     Video source type (v4l2|rtsp)\n"
              << "  -d, --device PATH     V4L2 device path (default: /dev/video0)\n"
              << "  -u, --url URL         RTSP URL\n"
              << "  -w, --width WIDTH     Video width (0=auto, default: 1280)\n"
              << "  -h, --height HEIGHT   Video height (0=auto, default: 720)\n"
              << "  -f, --fps FPS         Frame rate (0=auto, default: 30)\n"
              << "  --format FORMAT       Pixel format (0=auto, YUYV, MJPG, H264, help)\n"
              << "  -t, --tcp             Use TCP for RTSP (default: UDP)\n"
              << "  --topic TOPIC         DDS topic name (default: Video_Frames)\n"
              << "  --timeout MS          RTSP timeout in milliseconds (default: 1000)\n"
              << "  --buffer-count COUNT  Buffer count (default: 4)\n"
              << "  --no-dma              Disable DMA buffers\n"
              << "  --no-timestamp        Disable timestamp\n"
              << "  --auto                Auto-select all parameters\n"
              << "  --help                Show this help message\n"
              << "\n"
              << "Parameter Priority (when specified):\n"
              << "  1. Format (highest) - Must be supported, program exits if not\n"
              << "  2. FPS (medium)     - Must be supported, program exits if not\n"
              << "  3. Size (lowest)    - Must be supported, program exits if not\n"
              << "\n"
              << "Auto-selection (set to 0):\n"
              << "  Only parameters set to 0 will be auto-selected\n"
              << "  Specified parameters are strictly enforced\n"
              << "\n"
              << "Format specification:\n"
              << "  Four-character codes: YUYV, UYVY, MJPG, H264, RGB3, BGR3, NV12, etc.\n"
              << "  Integer values: " << V4L2_PIX_FMT_YUYV << " (YUYV), " << V4L2_PIX_FMT_MJPEG << " (MJPG), etc.\n"
              << "  Use 0 for auto-selection\n"
              << "  Use --format help to see all supported formats\n";
}

int main(int argc, char* argv[]) {
    // 默认配置
    CaptureConfig config;
    config.source_type = V4L2_SOURCE;
    config.device = "/dev/video0";
    config.width = 1280;
    config.height = 720;
    config.fps = 30;
    config.use_tcp = false;
    config.use_dma = true;
    config.buffer_count = 2;  // 减少缓冲区数量以降低延时
    config.dds_topic = "Video_Frames";

    // 配置文件路径
    std::string config_file = ConfigLoader::get_default_config_path("video_capture");

    // 第一次解析命令行参数，只获取配置文件路径
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"source", required_argument, 0, 's'},
        {"device", required_argument, 0, 'd'},
        {"url", required_argument, 0, 'u'},
        {"width", required_argument, 0, 'w'},
        {"height", required_argument, 0, 'h'},
        {"fps", required_argument, 0, 'f'},
        {"format", required_argument, 0, 'F'},
        {"tcp", no_argument, 0, 't'},
        {"topic", required_argument, 0, 'T'},
        {"timeout", required_argument, 0, 'o'},
        {"buffer-count", required_argument, 0, 'b'},
        {"no-dma", no_argument, 0, 1},
        {"no-timestamp", no_argument, 0, 2},
        {"auto", no_argument, 0, 3},
        {"help", no_argument, 0, 4},
        {0, 0, 0, 0}
    };
    
    // 第一次解析：只获取配置文件路径
    int c;
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:s:d:u:w:h:f:F:tT:o:b:", long_options, nullptr)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }

    // 加载配置文件（在解析其他命令行参数之前）
    if (!ConfigLoader::load_video_capture_config(config_file, config)) {
        LOG_W("Failed to load config file: %s, using default settings", config_file.c_str());
    }

    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:s:d:u:w:h:f:F:tT:o:b:", long_options, nullptr)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件路径已经处理过了
                break;
            case 's':
                if (strcmp(optarg, "v4l2") == 0) {
                    config.source_type = V4L2_SOURCE;
                } else if (strcmp(optarg, "rtsp") == 0) {
                    config.source_type = RTSP_SOURCE;
                } else {
                    std::cerr << "Invalid source type: " << optarg << std::endl;
                    return 1;
                }
                break;
            case 'd':
                config.device = optarg;
                break;
            case 'u':
                config.url = optarg;
                break;
            case 'w':
                config.width = atoi(optarg);
                break;
            case 'h':
                config.height = atoi(optarg);
                break;
            case 'f':
                config.fps = atoi(optarg);
                break;
            case 'F':
                config.format = parse_format_parameter(optarg);
                if (config.format == 0 && strcmp(optarg, "0") != 0) {
                    // 解析失败且不是显式的"0"
                    return 1;
                }
                break;
            case 't':
                config.use_tcp = true;
                break;
            case 'T':
                config.dds_topic = optarg;
                break;
            case 'o':
                config.timeout_us = atoi(optarg) * 1000; // 转换为微秒
                break;
            case 'b':
                config.buffer_count = atoi(optarg);
                break;
            case 1:
                config.use_dma = false;
                break;
            case 2:
                config.enable_timestamp = false;
                break;
            case 3:
                // --auto: 启用所有参数的自动选择
                config.width = 0;
                config.height = 0;
                config.fps = 0;
                config.format = 0;
                std::cout << "Auto-selection enabled for all parameters\n";
                break;
            case 4:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }

    // 验证配置
    if (config.source_type == RTSP_SOURCE && config.url.empty()) {
        std::cerr << "RTSP URL is required for RTSP source" << std::endl;
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    
    LOG_I("Starting video capture service...");
    LOG_I("Source: %s", (config.source_type == V4L2_SOURCE) ? "V4L2" : "RTSP");
    if (config.source_type == V4L2_SOURCE) {
        LOG_I("Device: %s, Resolution: %dx%d, FPS: %d, DMA: %s",
              config.device.c_str(), config.width, config.height, config.fps,
              config.use_dma ? "enabled" : "disabled");
    } else {
        LOG_I("URL: %s, TCP: %s", config.url.c_str(), config.use_tcp ? "yes" : "no");
    }
    
    int ret = 0;
    try {
        // 创建并初始化服务
        g_capture_service = std::make_unique<VideoCaptureService>();
        if (!g_capture_service->init(config)) {
            LOG_E("Failed to initialize video capture service");
            ret = 1;
            goto exit_;
        }
       
        // 启动服务
        g_capture_service->start();
        
        // 主循环 - 定期输出统计信息
        while (!g_stop.load()) {
            VideoCaptureService::Stats stats;
            std::this_thread::sleep_for(std::chrono::seconds(5));
            if (g_capture_service && !g_stop.load()) {
                g_capture_service->get_stats(stats);
                LOG_I("Stats - Captured: %lu, Dropped: %lu, FPS: %.1f, CPU: %.1f%%",
                      stats.frames_captured, stats.frames_dropped, stats.fps, stats.cpu_usage);
            }
        }

    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        ret = 1;
    } 

exit_:
    LOG_I("Video capture service stopped");
    // 显式停止服务
    if (g_capture_service) {
        g_capture_service->stop();
        // 显式释放资源
        g_capture_service.reset();
    }
    return ret;
}
