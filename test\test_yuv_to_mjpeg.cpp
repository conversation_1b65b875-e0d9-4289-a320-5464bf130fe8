#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <cstring>
#include <chrono>
#include <thread>
#include "common.h"
#include "gstreamer_encoder.h"

// 设置GStreamer日志级别
void set_gstreamer_log_level(const std::string& level) {
    if (level == "ERROR") {
        gst_debug_set_default_threshold(GST_LEVEL_ERROR);
    } else if (level == "WARNING") {
        gst_debug_set_default_threshold(GST_LEVEL_WARNING);
    } else if (level == "INFO") {
        gst_debug_set_default_threshold(GST_LEVEL_INFO);
    } else if (level == "DEBUG") {
        gst_debug_set_default_threshold(GST_LEVEL_DEBUG);
    } else if (level == "LOG") {
        gst_debug_set_default_threshold(GST_LEVEL_LOG);
    } else {
        gst_debug_set_default_threshold(GST_LEVEL_WARNING);
    }
    std::cout << "GStreamer log level set to: " << level << std::endl;
}

// 从文件读取YUV数据到Frame结构
bool load_yuv_from_file(const std::string& filename, Frame& frame, 
                       int width, int height, uint32_t format) {
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to open YUV file: " << filename << std::endl;
        return false;
    }

    // 计算YUV数据大小
    size_t data_size = 0;
    switch (format) {
        case V4L2_PIX_FMT_YUV420:
            data_size = width * height * 3 / 2;  // Y + U/2 + V/2
            break;
        case V4L2_PIX_FMT_NV12:
            data_size = width * height * 3 / 2;  // Y + UV
            break;
        case V4L2_PIX_FMT_YUYV:
            data_size = width * height * 2;      // YUYV packed
            break;
        default:
            std::cerr << "Unsupported YUV format: 0x" << std::hex << format << std::endl;
            return false;
    }

    // 设置Frame属性
    frame.width = width;
    frame.height = height;
    frame.format = format;
    frame.data_length = data_size;
    frame.data.resize(data_size);
    frame.valid = true;
    frame.timestamp = get_current_us();
    frame.frame_id = 1;
    frame.source_type = 0; // V4L2
    frame.is_keyframe = true;
    frame.dma_fd = -1;

    file.read(reinterpret_cast<char*>(frame.data.data()), data_size);
    if (file.gcount() != static_cast<std::streamsize>(data_size)) {
        std::cerr << "Failed to read complete YUV data. Expected: " << data_size 
                  << ", Read: " << file.gcount() << std::endl;
        return false;
    }

    std::cout << "Loaded YUV data: " << width << "x" << height 
              << ", format: 0x" << std::hex << format << std::dec
              << ", size: " << data_size << " bytes" << std::endl;
    return true;
}

// 从DDS读取帧数据
bool read_frame_from_dds(const std::string& topic_name, Frame& frame, int timeout_ms = 5000) {
    try {
        std::cout << "Initializing DDS Video Reader for topic: " << topic_name << std::endl;
        DDSVideoReader reader(topic_name, 5);  // 最多缓存5帧
        
        std::cout << "Waiting for frame from DDS (timeout: " << timeout_ms << "ms)..." << std::endl;
        if (reader.read(frame, timeout_ms)) {
            std::cout << "Received frame from DDS:" << std::endl;
            std::cout << "  Frame ID: " << frame.frame_id << std::endl;
            std::cout << "  Resolution: " << frame.width << "x" << frame.height << std::endl;
            std::cout << "  Format: 0x" << std::hex << frame.format << ", " << v4l2_format_to_gst_string(frame.format) << std::dec << std::endl;
            std::cout << "  Data size: " << frame.data.size() << " bytes" << std::endl;
            std::cout << "  Timestamp: " << frame.timestamp << std::endl;
            std::cout << "  Is keyframe: " << (frame.is_keyframe ? "Yes" : "No") << std::endl;
            return true;
        } else {
            std::cerr << "Timeout waiting for frame from DDS" << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize DDS Video Reader: " << e.what() << std::endl;
        return false;
    }
}

// 保存Frame数据到文件
bool save_frame_to_file(const std::string& filename, const std::vector<uint8_t>& frame_data) {
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to create file: " << filename << std::endl;
        return false;
    }

    file.write(reinterpret_cast<const char*>(frame_data.data()), frame_data.size());
    if (!file.good()) {
        std::cerr << "Failed to write frame data to file" << std::endl;
        file.close();
        return false;
    }

    file.close();
    std::cout << "Saved frame data to: " << filename 
              << ", size: " << frame_data.size() << " bytes" << std::endl;
    return true;
}

// 格式字符串转换为V4L2格式
uint32_t parse_yuv_format(const std::string& format_str) {
    if (format_str == "YUV420" || format_str == "yuv420") {
        return V4L2_PIX_FMT_YUV420;
    } else if (format_str == "NV12" || format_str == "nv12") {
        return V4L2_PIX_FMT_NV12;
    } else if (format_str == "YUYV" || format_str == "yuyv") {
        return V4L2_PIX_FMT_YUYV;
    } else {
        return 0;  // 无效格式
    }
}

// 打印使用说明
void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " <mode> [options...]" << std::endl;
    std::cout << std::endl;
    std::cout << "Modes:" << std::endl;
    std::cout << "  file   - Read YUV from file and convert to MJPEG" << std::endl;
    std::cout << "  dds    - Read frame from DDS and convert to MJPEG" << std::endl;
    std::cout << std::endl;
    std::cout << "File mode:" << std::endl;
    std::cout << "  " << program_name << " file <input_yuv_file> <output_mjpeg_file> "
              << "<width> <height> <yuv_format> [quality] [log_level]" << std::endl;
    std::cout << std::endl;
    std::cout << "DDS mode:" << std::endl;
    std::cout << "  " << program_name << " dds <topic_name> <output_mjpeg_file> "
              << "[quality] [timeout_ms] [log_level]" << std::endl;
    std::cout << std::endl;
    std::cout << "Parameters:" << std::endl;
    std::cout << "  input_yuv_file   - Input YUV file path" << std::endl;
    std::cout << "  output_mjpeg_file - Output MJPEG file path" << std::endl;
    std::cout << "  width            - Image width" << std::endl;
    std::cout << "  height           - Image height" << std::endl;
    std::cout << "  yuv_format       - YUV format (YUV420/NV12/YUYV)" << std::endl;
    std::cout << "  topic_name       - DDS topic name" << std::endl;
    std::cout << "  quality          - JPEG quality (1-100, default: 85)" << std::endl;
    std::cout << "  timeout_ms       - DDS read timeout in ms (default: 5000)" << std::endl;
    std::cout << "  log_level        - GStreamer log level (ERROR/WARNING/INFO/DEBUG/LOG, default: WARNING)" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << program_name << " file input.yuv output.mjpeg 1920 1080 NV12 90 DEBUG" << std::endl;
    std::cout << "  " << program_name << " dds video_topic output.mjpeg 85 10000 INFO" << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }

    std::string mode = argv[1];
    
    if (mode == "file") {
        // 文件模式
        if (argc < 7) {
            print_usage(argv[0]);
            return 1;
        }

        std::string input_file = argv[2];
        std::string output_file = argv[3];
        int width = std::atoi(argv[4]);
        int height = std::atoi(argv[5]);
        std::string format_str = argv[6];
        int quality = (argc > 7) ? std::atoi(argv[7]) : 85;
        std::string log_level = (argc > 8) ? argv[8] : "WARNING";

        // 验证参数
        if (width <= 0 || height <= 0) {
            std::cerr << "Invalid width or height" << std::endl;
            return 1;
        }

        uint32_t yuv_format = parse_yuv_format(format_str);
        if (yuv_format == 0) {
            std::cerr << "Invalid YUV format: " << format_str << std::endl;
            std::cerr << "Supported formats: YUV420, NV12, YUYV" << std::endl;
            return 1;
        }

        if (quality < 1 || quality > 100) {
            std::cerr << "Invalid quality value: " << quality << " (should be 1-100)" << std::endl;
            return 1;
        }

        std::cout << "=== File Mode: YUV to MJPEG Conversion ===" << std::endl;
        std::cout << "Input file: " << input_file << std::endl;
        std::cout << "Output file: " << output_file << std::endl;
        std::cout << "Resolution: " << width << "x" << height << std::endl;
        std::cout << "YUV format: " << format_str << " (0x" << std::hex << yuv_format << std::dec << ")" << std::endl;
        std::cout << "JPEG quality: " << quality << std::endl;
        std::cout << "Log level: " << log_level << std::endl;
        std::cout << std::endl;

        // 设置GStreamer日志级别
        set_gstreamer_log_level(log_level);

        // 加载YUV数据到Frame结构
        Frame input_frame;
        if (!load_yuv_from_file(input_file, input_frame, width, height, yuv_format)) {
            std::cerr << "Failed to load YUV data" << std::endl;
            return 1;
        }

        // 创建MJPEG编码器
        GStreamerEncoder encoder(EncoderType::JPEG, quality, width, height, 30, yuv_format);

        // 初始化编码器
        if (!encoder.init()) {
            std::cerr << "Failed to initialize MJPEG encoder" << std::endl;
            return 1;
        }

        std::cout << "MJPEG encoder initialized successfully" << std::endl;

        // 执行编码
        std::cout << "Starting YUV to MJPEG conversion..." << std::endl;
        Frame output_frame;
        if (!encoder.encode(input_frame, output_frame)) {
            std::cerr << "Failed to encode YUV to MJPEG" << std::endl;
            encoder.cleanup();
            return 1;
        }

        std::cout << "Conversion successful!" << std::endl;
        std::cout << "  Input size: " << input_frame.data.size() << " bytes" << std::endl;
        std::cout << "  Output size: " << output_frame.data.size() << " bytes" << std::endl;
        std::cout << "  Compression ratio: " << 
            (float)input_frame.data.size() / output_frame.data.size() << ":1" << std::endl;

        // 保存MJPEG数据
        if (!save_frame_to_file(output_file, output_frame.data)) {
            std::cerr << "Failed to save MJPEG data" << std::endl;
            encoder.cleanup();
            return 1;
        }

        encoder.cleanup();
        std::cout << "=== File Mode Test Completed Successfully ===" << std::endl;

    } else if (mode == "dds") {
        // DDS模式
        if (argc < 4) {
            print_usage(argv[0]);
            return 1;
        }

        std::string topic_name = argv[2];
        std::string output_file = argv[3];
        int quality = (argc > 4) ? std::atoi(argv[4]) : 85;
        int timeout_ms = (argc > 5) ? std::atoi(argv[5]) : 5000;
        std::string log_level = (argc > 6) ? argv[6] : "WARNING";

        if (quality < 1 || quality > 100) {
            std::cerr << "Invalid quality value: " << quality << " (should be 1-100)" << std::endl;
            return 1;
        }

        std::cout << "=== DDS Mode: Frame to MJPEG Conversion ===" << std::endl;
        std::cout << "DDS topic: " << topic_name << std::endl;
        std::cout << "Output file: " << output_file << std::endl;
        std::cout << "JPEG quality: " << quality << std::endl;
        std::cout << "Timeout: " << timeout_ms << "ms" << std::endl;
        std::cout << "Log level: " << log_level << std::endl;
        std::cout << std::endl;

        // 设置GStreamer日志级别
        set_gstreamer_log_level(log_level);

        // 从DDS读取帧
        Frame input_frame;
        if (!read_frame_from_dds(topic_name, input_frame, timeout_ms)) {
            std::cerr << "Failed to read frame from DDS" << std::endl;
            return 1;
        }

        // 创建MJPEG编码器（使用从DDS获取的帧参数）
        GStreamerEncoder encoder(EncoderType::JPEG, quality, 
                                input_frame.width, input_frame.height, 30, 
                                input_frame.format);

        // 初始化编码器
        if (!encoder.init()) {
            std::cerr << "Failed to initialize MJPEG encoder" << std::endl;
            return 1;
        }

        std::cout << "MJPEG encoder initialized successfully" << std::endl;
        std::cout << "Encoder info:" << std::endl;
        std::cout << "  Type: " << encoder.get_encoder_name() << std::endl;
        std::cout << "  Input format: 0x" << std::hex << encoder.get_input_format() << std::dec << std::endl;
        std::cout << "  Resolution: " << encoder.get_width() << "x" << encoder.get_height() << std::endl;
        std::cout << "  Quality: " << encoder.get_quality() << std::endl;

        // 执行编码
        std::cout << "\nStarting frame to MJPEG conversion..." << std::endl;
        Frame output_frame;
        if (!encoder.encode(input_frame, output_frame)) {
            std::cerr << "Failed to encode frame to MJPEG" << std::endl;
            encoder.cleanup();
            return 1;
        }

        // 同时也保存frame 到本地文件。
        save_frame_to_file("dds_input_frame.yuv", input_frame.data);

        std::cout << "Conversion successful!" << std::endl;
        std::cout << "  Input size: " << input_frame.data.size() << " bytes" << std::endl;
        std::cout << "  Output size: " << output_frame.data.size() << " bytes" << std::endl;
        std::cout << "  Compression ratio: " << 
            (float)input_frame.data.size() / output_frame.data.size() << ":1" << std::endl;

        // 保存MJPEG数据
        if (!save_frame_to_file(output_file, output_frame.data)) {
            std::cerr << "Failed to save MJPEG data" << std::endl;
            encoder.cleanup();
            return 1;
        }

        encoder.cleanup();
        std::cout << "=== DDS Mode Test Completed Successfully ===" << std::endl;

    } else {
        std::cerr << "Invalid mode: " << mode << std::endl;
        std::cerr << "Supported modes: file, dds" << std::endl;
        print_usage(argv[0]);
        return 1;
    }

    return 0;
}


