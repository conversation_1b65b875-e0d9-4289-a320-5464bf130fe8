#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "mpp_decoder.h"
#include "common.h"
#include <chrono>
#include <vector>

// Test fixture for MPP Decoder tests
class MPPDecoderTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup test environment - create MJPEG decoder by default
        decoder_ = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);
    }

    void TearDown() override {
        // Cleanup test environment
        if (decoder_) {
            decoder_->cleanup();
        }
    }
    
    // Helper function to generate valid MJPEG frame
    void generateTestMJPEGFrame(Frame& frame, int width, int height) {
        frame.width = width;
        frame.height = height;
        frame.format = V4L2_PIX_FMT_MJPEG;
        frame.frame_id = 1;
        frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();
        frame.source_type = 0;
        frame.is_keyframe = true;
        frame.valid = true;

        // Generate a minimal valid JPEG image (8x8 grayscale)
        std::vector<uint8_t> jpeg_data = {
            // SOI (Start of Image)
            0xFF, 0xD8,

            // APP0 (JFIF header)
            0xFF, 0xE0, 0x00, 0x10,
            0x4A, 0x46, 0x49, 0x46, 0x00,  // "JFIF\0"
            0x01, 0x01,                    // Version 1.1
            0x01,                          // Units (dots per inch)
            0x00, 0x48, 0x00, 0x48,        // X and Y density (72 DPI)
            0x00, 0x00,                    // Thumbnail width and height

            // DQT (Define Quantization Table) - Luminance
            0xFF, 0xDB, 0x00, 0x43, 0x00,
            0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07,
            0x07, 0x07, 0x09, 0x09, 0x08, 0x0A, 0x0C, 0x14,
            0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12, 0x13,
            0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A,
            0x1C, 0x1C, 0x20, 0x24, 0x2E, 0x27, 0x20, 0x22,
            0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29, 0x2C,
            0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39,
            0x3D, 0x38, 0x32, 0x3C, 0x2E, 0x33, 0x34, 0x32,

            // SOF0 (Start of Frame - Baseline DCT)
            0xFF, 0xC0, 0x00, 0x11, 0x08,
            0x00, 0x08, 0x00, 0x08,        // Height=8, Width=8
            0x01,                          // Number of components = 1 (grayscale)
            0x01, 0x11, 0x00,             // Component 1: ID=1, sampling=1x1, quantization table=0

            // DHT (Define Huffman Table) - DC Luminance
            0xFF, 0xC4, 0x00, 0x1F, 0x00,
            0x00, 0x01, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B,

            // DHT (Define Huffman Table) - AC Luminance (simplified)
            0xFF, 0xC4, 0x00, 0x14, 0x10,
            0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x08,

            // SOS (Start of Scan)
            0xFF, 0xDA, 0x00, 0x08, 0x01, 0x01, 0x00, 0x00, 0x3F, 0x00,

            // Compressed image data (minimal data for 8x8 grayscale image)
            0xD2, 0xCF, 0x20,

            // EOI (End of Image)
            0xFF, 0xD9
        };

        frame.data = jpeg_data;
    }
    
    std::unique_ptr<MPPDecoder> decoder_;
};

// Test MPP Decoder initialization with MJPEG format
TEST_F(MPPDecoderTest, InitializationMJPEGFormat) {
    // Test successful initialization with MJPEG format
    bool result = decoder_->init(1280, 720);

    // Verify decoder type
    EXPECT_EQ(decoder_->get_decoder_type(), MPP_DECODER_TYPE_MJPEG);
    EXPECT_STREQ(decoder_->get_decoder_type_name(), "MJPEG");

    // Note: This may fail on systems without MPP hardware, which is expected
    if (result) {
        EXPECT_TRUE(result);
        EXPECT_TRUE(decoder_->is_initialized());
        GTEST_LOG_(INFO) << "MPP decoder initialized successfully for MJPEG 1280x720";
    } else {
        GTEST_LOG_(WARNING) << "MPP decoder initialization failed (expected on systems without MPP hardware)";
    }
}

// Test MPP Decoder initialization with H264 format
TEST_F(MPPDecoderTest, InitializationH264Format) {
    // Create H264 decoder
    auto h264_decoder = std::make_unique<MPPDecoder>(MPP_DECODER_TYPE_H264);
    bool result = h264_decoder->init(1920, 1080);

    // Verify decoder type
    EXPECT_EQ(h264_decoder->get_decoder_type(), MPP_DECODER_TYPE_H264);
    EXPECT_STREQ(h264_decoder->get_decoder_type_name(), "H264");

    if (result) {
        EXPECT_TRUE(result);
        EXPECT_TRUE(h264_decoder->is_initialized());
        GTEST_LOG_(INFO) << "MPP decoder initialized successfully for H264 1920x1080";
    } else {
        GTEST_LOG_(WARNING) << "MPP decoder initialization failed (expected on systems without MPP hardware)";
    }
}

// Test MPP Decoder initialization with H265 format
TEST_F(MPPDecoderTest, InitializationH265Format) {
    // Create H265 decoder
    auto h265_decoder = std::make_unique<MPPDecoder>(MPP_DECODER_TYPE_H265);
    bool result = h265_decoder->init(3840, 2160);

    // Verify decoder type
    EXPECT_EQ(h265_decoder->get_decoder_type(), MPP_DECODER_TYPE_H265);
    EXPECT_STREQ(h265_decoder->get_decoder_type_name(), "H265");

    if (result) {
        EXPECT_TRUE(result);
        EXPECT_TRUE(h265_decoder->is_initialized());
        GTEST_LOG_(INFO) << "MPP decoder initialized successfully for H265 3840x2160";
    } else {
        GTEST_LOG_(WARNING) << "MPP decoder initialization failed (expected on systems without MPP hardware)";
    }
}

// Test MPP Decoder initialization with unsupported format
TEST_F(MPPDecoderTest, InitializationUnsupportedFormat) {
    // Test creation with unsupported format (should fail at construction)
    auto unsupported_decoder = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_YUYV);

    EXPECT_EQ(unsupported_decoder->get_state(), MPP_DECODER_STATE_ERROR);
    GTEST_LOG_(INFO) << "MPP decoder correctly rejected unsupported format (YUYV) at construction";
}

// Test MPP Decoder initialization with different resolutions
TEST_F(MPPDecoderTest, InitializationDifferentResolutions) {
    std::vector<std::pair<int, int>> resolutions = {
        {320, 240},   // QVGA
        {640, 480},   // VGA
        {1280, 720},  // HD
        {1920, 1080}, // Full HD
    };

    for (const auto& res : resolutions) {
        // Create new decoder for each resolution test
        auto test_decoder = std::make_unique<MPPDecoder>(MPP_DECODER_TYPE_MJPEG);

        bool result = test_decoder->init(res.first, res.second);

        if (result) {
            EXPECT_TRUE(result);
            EXPECT_TRUE(test_decoder->is_initialized());
            GTEST_LOG_(INFO) << "Init successful for " << res.first << "x" << res.second;
        } else {
            GTEST_LOG_(WARNING) << "Init failed for " << res.first << "x" << res.second
                               << " (expected on systems without MPP hardware)";
        }
    }
}

// Test MJPEG decoding functionality
TEST_F(MPPDecoderTest, MJPEGDecoding) {
    // Initialize decoder
    bool init_result = decoder_->init(640, 480);

    if (!init_result) {
        GTEST_SKIP() << "Skipping decode test - decoder initialization failed (no MPP hardware)";
        return;
    }

    // Verify decoder type
    EXPECT_EQ(decoder_->get_decoder_type(), MPP_DECODER_TYPE_MJPEG);

    // Generate test MJPEG frame
    Frame src_frame, dst_frame;
    generateTestMJPEGFrame(src_frame, 640, 480);

    // Verify source frame properties
    EXPECT_EQ(src_frame.width, 640);
    EXPECT_EQ(src_frame.height, 480);
    EXPECT_EQ(src_frame.format, V4L2_PIX_FMT_MJPEG);
    EXPECT_TRUE(src_frame.is_keyframe);
    EXPECT_GT(src_frame.data.size(), 0);

    // Attempt to decode using unified interface
    bool decode_result = decoder_->decode_frame(src_frame, dst_frame);

    if (decode_result) {
        // Verify decoded frame properties
        EXPECT_GT(dst_frame.width, 0);
        EXPECT_GT(dst_frame.height, 0);
        EXPECT_EQ(dst_frame.format, V4L2_PIX_FMT_NV12);  // MPP typically outputs NV12
        EXPECT_GT(dst_frame.data.size(), 0);

        GTEST_LOG_(INFO) << "MJPEG decode successful - Output: "
                        << dst_frame.width << "x" << dst_frame.height
                        << ", format: 0x" << std::hex << dst_frame.format;
    } else {
        GTEST_LOG_(WARNING) << "MJPEG decode failed (expected on systems without MPP hardware)";
    }
}

// Test multiple initialization and cleanup cycles
TEST_F(MPPDecoderTest, MultipleInitCleanupCycles) {
    const int num_cycles = 3;

    for (int i = 0; i < num_cycles; i++) {
        bool init_result = decoder_->init(1920, 1080);

        if (init_result) {
            EXPECT_TRUE(init_result);
            EXPECT_TRUE(decoder_->is_initialized());
            GTEST_LOG_(INFO) << "Cycle " << (i + 1) << ": Init successful";
        } else {
            GTEST_LOG_(WARNING) << "Cycle " << (i + 1) << ": Init failed (expected on systems without MPP hardware)";
        }

        // Cleanup should not throw or crash
        EXPECT_NO_THROW(decoder_->cleanup());
        EXPECT_FALSE(decoder_->is_initialized());
    }
}

// Test duplicate initialization (should fail)
TEST_F(MPPDecoderTest, DuplicateInitialization) {
    bool first_init = decoder_->init(640, 480);

    if (first_init) {
        EXPECT_TRUE(decoder_->is_initialized());

        // Second initialization should fail
        bool second_init = decoder_->init(1280, 720);
        EXPECT_FALSE(second_init);

        GTEST_LOG_(INFO) << "Duplicate initialization correctly rejected";
    } else {
        GTEST_SKIP() << "Skipping duplicate init test - first initialization failed";
    }
}

// Test decoder behavior with invalid input
TEST_F(MPPDecoderTest, InvalidInputHandling) {
    // Try to decode without initialization
    Frame src_frame, dst_frame;
    generateTestMJPEGFrame(src_frame, 640, 480);
    
    bool decode_result = decoder_->decode_frame(src_frame, dst_frame);
    
    // Should handle gracefully (may auto-initialize or return false)
    if (!decode_result) {
        GTEST_LOG_(INFO) << "Decoder correctly handled decode attempt without initialization";
    }
    
    // Test with empty frame data
    Frame empty_frame;
    empty_frame.width = 640;
    empty_frame.height = 480;
    empty_frame.format = V4L2_PIX_FMT_MJPEG;
    empty_frame.data.clear();  // Empty data
    
    bool empty_decode_result = decoder_->decode_frame(empty_frame, dst_frame);
    
    // Should handle empty data gracefully
    EXPECT_FALSE(empty_decode_result);
    GTEST_LOG_(INFO) << "Decoder correctly handled empty frame data";
}

// Test cleanup without initialization
TEST_F(MPPDecoderTest, CleanupWithoutInit) {
    // Create a fresh decoder
    auto test_decoder = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);
    
    // Cleanup should not crash even without initialization
    EXPECT_NO_THROW(test_decoder->cleanup());
    GTEST_LOG_(INFO) << "Cleanup without initialization handled gracefully";
}

// Performance test (basic timing)
TEST_F(MPPDecoderTest, BasicPerformanceTest) {
    bool init_result = decoder_->init(1280, 720);
    
    if (!init_result) {
        GTEST_SKIP() << "Skipping performance test - decoder initialization failed";
        return;
    }
    
    Frame src_frame, dst_frame;
    generateTestMJPEGFrame(src_frame, 1280, 720);
    
    const int num_iterations = 10;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    int successful_decodes = 0;
    for (int i = 0; i < num_iterations; i++) {
        if (decoder_->decode_frame(src_frame, dst_frame)) {
            successful_decodes++;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    if (successful_decodes > 0) {
        double avg_time = static_cast<double>(duration.count()) / successful_decodes;
        GTEST_LOG_(INFO) << "Performance test: " << successful_decodes << "/" << num_iterations 
                        << " successful decodes, average time: " << avg_time << "ms";
        
        // Basic performance expectation (should be reasonably fast)
        EXPECT_LT(avg_time, 1000.0);  // Should be less than 1 second per decode
    } else {
        GTEST_LOG_(WARNING) << "No successful decodes in performance test";
    }
}
