// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file DDSVideoFrameApplication.cxx
 * This file contains the implementation of the application functions.
 *
 * This file was generated by the tool fastddsgen.
 */

#include "DDSVideoFrameApplication.hpp"

#include "DDSVideoFramePublisherApp.hpp"
#include "DDSVideoFrameSubscriberApp.hpp"

//! Factory method to create a publisher or subscriber
std::shared_ptr<DDSVideoFrameApplication> DDSVideoFrameApplication::make_app(
        const int& domain_id,
        const std::string& entity_kind)
{
    std::shared_ptr<DDSVideoFrameApplication> entity;
    if (strcmp(entity_kind.c_str(), "publisher") == 0)
    {
        entity = std::make_shared<DDSVideoFramePublisherApp>(domain_id);
    }
    else if (strcmp(entity_kind.c_str(), "subscriber") == 0)
    {
        entity = std::make_shared<DDSVideoFrameSubscriberApp>(domain_id);
    }
    else
    {
        throw std::runtime_error("Entity initialization failed");
    }
    return entity;
}