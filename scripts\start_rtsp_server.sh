#!/bin/bash

# RTSP Server 启动脚本
# 用于启动和管理RTSP服务器服务

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
CONFIG_DIR="$PROJECT_ROOT/config"
LOG_DIR="$PROJECT_ROOT/logs"

# 默认配置
DEFAULT_CONFIG="$CONFIG_DIR/rtsp_server.json"
DEFAULT_TOPIC="Video_Frames"
DEFAULT_PORT=8554
DEFAULT_MOUNT="/stream"
DEFAULT_ADDRESS="0.0.0.0"
DEFAULT_WIDTH=1280
DEFAULT_HEIGHT=720
DEFAULT_FPS=30
DEFAULT_BITRATE=2000000

# 日志文件
LOG_FILE="$LOG_DIR/rtsp_server.log"
PID_FILE="$LOG_DIR/rtsp_server.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

log_debug() {
    if [[ "$DEBUG" == "1" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1" | tee -a "$LOG_FILE"
    fi
}

# 显示使用说明
show_usage() {
    cat << EOF
RTSP Server 启动脚本

用法: $0 [选项] [命令]

命令:
    start           启动RTSP服务器
    stop            停止RTSP服务器
    restart         重启RTSP服务器
    status          查看服务器状态
    logs            查看日志
    test            测试RTSP流

选项:
    -c, --config FILE       配置文件路径 (默认: $DEFAULT_CONFIG)
    -t, --topic TOPIC       DDS topic名称 (默认: $DEFAULT_TOPIC)
    -a, --address ADDR      服务器地址 (默认: $DEFAULT_ADDRESS)
    -p, --port PORT         服务器端口 (默认: $DEFAULT_PORT)
    -m, --mount PATH        挂载点 (默认: $DEFAULT_MOUNT)
    -w, --width WIDTH       输出宽度 (默认: $DEFAULT_WIDTH)
    -h, --height HEIGHT     输出高度 (默认: $DEFAULT_HEIGHT)
    -f, --fps FPS           帧率 (默认: $DEFAULT_FPS)
    -b, --bitrate RATE      码率 (默认: $DEFAULT_BITRATE)
    --hw-encoder            使用硬件编码器
    --sw-encoder            使用软件编码器
    --stats-interval SEC    统计信息间隔 (默认: 10)
    --debug                 启用调试模式
    --help                  显示此帮助信息

示例:
    $0 start                                    # 使用默认配置启动
    $0 start -t CloudFrames -p 8555            # 指定topic和端口
    $0 start -c custom_config.json             # 使用自定义配置文件
    $0 test rtsp://localhost:8554/stream       # 测试RTSP流

RTSP URL格式: rtsp://server_ip:port/mount_point
EOF
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    # 检查可执行文件
    if [[ ! -f "$BUILD_DIR/rtsp_server_main" ]]; then
        missing_deps+=("rtsp_server_main (请先编译项目)")
    fi
    
    # 检查GStreamer工具
    if ! command -v gst-launch-1.0 &> /dev/null; then
        missing_deps+=("gst-launch-1.0 (GStreamer工具)")
    fi
    
    # 检查FFplay (用于测试)
    if ! command -v ffplay &> /dev/null; then
        log_warn "ffplay未找到，无法使用内置测试功能"
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少依赖项:"
        for dep in "${missing_deps[@]}"; do
            log_error "  - $dep"
        done
        return 1
    fi
    
    return 0
}

# 创建必要目录
create_directories() {
    mkdir -p "$LOG_DIR"
    mkdir -p "$BUILD_DIR"
}

# 检查服务器状态
check_status() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # 运行中
        else
            rm -f "$PID_FILE"
            return 1  # 未运行
        fi
    else
        return 1  # 未运行
    fi
}

# 启动服务器
start_server() {
    log_info "启动RTSP服务器..."
    
    if check_status; then
        log_warn "RTSP服务器已在运行中"
        return 0
    fi
    
    # 构建命令行参数
    local cmd_args=()
    
    if [[ -n "$CONFIG_FILE" ]]; then
        cmd_args+=("--config" "$CONFIG_FILE")
    fi
    
    if [[ -n "$DDS_TOPIC" ]]; then
        cmd_args+=("--topic" "$DDS_TOPIC")
    fi
    
    if [[ -n "$SERVER_ADDRESS" ]]; then
        cmd_args+=("--address" "$SERVER_ADDRESS")
    fi
    
    if [[ -n "$SERVER_PORT" ]]; then
        cmd_args+=("--port" "$SERVER_PORT")
    fi
    
    if [[ -n "$MOUNT_POINT" ]]; then
        cmd_args+=("--mount" "$MOUNT_POINT")
    fi
    
    if [[ -n "$OUTPUT_WIDTH" ]]; then
        cmd_args+=("--width" "$OUTPUT_WIDTH")
    fi
    
    if [[ -n "$OUTPUT_HEIGHT" ]]; then
        cmd_args+=("--height" "$OUTPUT_HEIGHT")
    fi
    
    if [[ -n "$OUTPUT_FPS" ]]; then
        cmd_args+=("--fps" "$OUTPUT_FPS")
    fi
    
    if [[ -n "$OUTPUT_BITRATE" ]]; then
        cmd_args+=("--bitrate" "$OUTPUT_BITRATE")
    fi
    
    if [[ "$USE_HW_ENCODER" == "1" ]]; then
        cmd_args+=("--hw-encoder")
    elif [[ "$USE_SW_ENCODER" == "1" ]]; then
        cmd_args+=("--sw-encoder")
    fi
    
    if [[ -n "$STATS_INTERVAL" ]]; then
        cmd_args+=("--stats-interval" "$STATS_INTERVAL")
    fi
    
    log_debug "执行命令: $BUILD_DIR/rtsp_server_main ${cmd_args[*]}"
    
    # 启动服务器
    nohup "$BUILD_DIR/rtsp_server_main" "${cmd_args[@]}" >> "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo "$pid" > "$PID_FILE"
    
    # 等待启动
    sleep 2
    
    if check_status; then
        local rtsp_url="rtsp://${SERVER_ADDRESS:-$DEFAULT_ADDRESS}:${SERVER_PORT:-$DEFAULT_PORT}${MOUNT_POINT:-$DEFAULT_MOUNT}"
        log_info "RTSP服务器启动成功 (PID: $pid)"
        log_info "RTSP URL: $rtsp_url"
        log_info "日志文件: $LOG_FILE"
    else
        log_error "RTSP服务器启动失败"
        return 1
    fi
}

# 停止服务器
stop_server() {
    log_info "停止RTSP服务器..."
    
    if ! check_status; then
        log_warn "RTSP服务器未运行"
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    
    # 发送SIGTERM信号
    kill "$pid" 2>/dev/null
    
    # 等待进程结束
    local count=0
    while kill -0 "$pid" 2>/dev/null && [[ $count -lt 10 ]]; do
        sleep 1
        ((count++))
    done
    
    # 如果进程仍在运行，强制终止
    if kill -0 "$pid" 2>/dev/null; then
        log_warn "强制终止RTSP服务器进程"
        kill -9 "$pid" 2>/dev/null
    fi
    
    rm -f "$PID_FILE"
    log_info "RTSP服务器已停止"
}

# 重启服务器
restart_server() {
    stop_server
    sleep 1
    start_server
}

# 显示状态
show_status() {
    if check_status; then
        local pid=$(cat "$PID_FILE")
        log_info "RTSP服务器正在运行 (PID: $pid)"
        
        # 显示网络连接信息
        if command -v netstat &> /dev/null; then
            local port=${SERVER_PORT:-$DEFAULT_PORT}
            local connections=$(netstat -an | grep ":$port " | wc -l)
            log_info "端口 $port 连接数: $connections"
        fi
        
        # 显示RTSP URL
        local rtsp_url="rtsp://${SERVER_ADDRESS:-$DEFAULT_ADDRESS}:${SERVER_PORT:-$DEFAULT_PORT}${MOUNT_POINT:-$DEFAULT_MOUNT}"
        log_info "RTSP URL: $rtsp_url"
    else
        log_info "RTSP服务器未运行"
    fi
}

# 查看日志
show_logs() {
    if [[ -f "$LOG_FILE" ]]; then
        tail -f "$LOG_FILE"
    else
        log_error "日志文件不存在: $LOG_FILE"
    fi
}

# 测试RTSP流
test_stream() {
    local rtsp_url="$1"
    
    if [[ -z "$rtsp_url" ]]; then
        rtsp_url="rtsp://${SERVER_ADDRESS:-$DEFAULT_ADDRESS}:${SERVER_PORT:-$DEFAULT_PORT}${MOUNT_POINT:-$DEFAULT_MOUNT}"
    fi
    
    log_info "测试RTSP流: $rtsp_url"
    
    # 使用ffplay测试
    if command -v ffplay &> /dev/null; then
        log_info "使用ffplay播放RTSP流 (按q退出)"
        ffplay -rtsp_transport tcp "$rtsp_url"
    # 使用gst-launch测试
    elif command -v gst-launch-1.0 &> /dev/null; then
        log_info "使用GStreamer播放RTSP流 (按Ctrl+C退出)"
        gst-launch-1.0 rtspsrc location="$rtsp_url" ! decodebin ! videoconvert ! autovideosink
    else
        log_error "未找到播放工具 (ffplay 或 gst-launch-1.0)"
        log_info "请手动测试RTSP流: $rtsp_url"
    fi
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -t|--topic)
                DDS_TOPIC="$2"
                shift 2
                ;;
            -a|--address)
                SERVER_ADDRESS="$2"
                shift 2
                ;;
            -p|--port)
                SERVER_PORT="$2"
                shift 2
                ;;
            -m|--mount)
                MOUNT_POINT="$2"
                shift 2
                ;;
            -w|--width)
                OUTPUT_WIDTH="$2"
                shift 2
                ;;
            -h|--height)
                OUTPUT_HEIGHT="$2"
                shift 2
                ;;
            -f|--fps)
                OUTPUT_FPS="$2"
                shift 2
                ;;
            -b|--bitrate)
                OUTPUT_BITRATE="$2"
                shift 2
                ;;
            --hw-encoder)
                USE_HW_ENCODER=1
                shift
                ;;
            --sw-encoder)
                USE_SW_ENCODER=1
                shift
                ;;
            --stats-interval)
                STATS_INTERVAL="$2"
                shift 2
                ;;
            --debug)
                DEBUG=1
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            start|stop|restart|status|logs|test)
                COMMAND="$1"
                shift
                ;;
            *)
                if [[ "$1" =~ ^rtsp:// ]]; then
                    TEST_URL="$1"
                else
                    log_error "未知参数: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    # 创建目录
    create_directories
    
    # 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    # 执行命令
    case "${COMMAND:-start}" in
        start)
            start_server
            ;;
        stop)
            stop_server
            ;;
        restart)
            restart_server
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        test)
            test_stream "$TEST_URL"
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_usage
            exit 1
            ;;
    esac
}

# 信号处理
trap 'log_info "收到中断信号，正在停止..."; stop_server; exit 0' INT TERM

# 运行主函数
main "$@"
