# 视频服务代码目录重组总结

本次重组将 `include` 和 `src` 目录下的文件按功能模块进行了分类整理，提高了代码的可维护性和可读性。

## 📁 新的目录结构

### `include/` 目录结构

```
include/
├── common.h                     # 通用工具和定义（保留在根目录）
├── capture/                     # 视频捕获相关
│   ├── capture_config.h         # 捕获配置
│   ├── v4l2_capture_base.h      # V4L2捕获基类
│   ├── v4l2_config.h            # V4L2配置
│   ├── v4l2_dds.h               # V4L2 DDS集成
│   ├── v4l2_shr_mem.h           # V4L2共享内存
│   └── video_capture.h          # 视频捕获主接口
├── config/                      # 配置管理相关
│   ├── config_loader.h          # 通用配置加载器
│   ├── thread_pool.h            # 线程池工具
│   ├── video_control.h          # 视频控制
│   └── video_transport_config.h # 传输配置
├── hardware/                    # 硬件加速相关
│   ├── dma_op.h.ion             # DMA操作
│   ├── mpp_decoder.h            # MPP解码器
│   └── rga_accelerator.h        # RGA加速器
├── processing/                  # 视频处理相关
│   ├── ai_processor.h           # AI处理器
│   ├── gstreamer_encoder.h      # GStreamer编码器
│   ├── onnx_engine.h            # ONNX推理引擎
│   ├── tensorrt_engine.h        # TensorRT推理引擎
│   └── video_converter.h        # 视频转换器
├── streaming/                   # 流媒体相关
│   ├── cloud_streamer.h         # 云端流媒体
│   ├── rtsp_client.h            # RTSP客户端
│   └── rtsp_server.h            # RTSP服务器
└── transport/                   # 传输抽象层
    ├── dma_buffer_manager.h     # DMA缓冲区管理
    ├── dma_distribution.h       # DMA分发机制
    ├── dma_video_transport.h    # DMA视频传输适配器
    ├── fastdds_video_transport.h # FastDDS视频传输适配器
    └── video_transport_interface.h # 视频传输抽象接口
```

### `src/` 目录结构

```
src/
├── capture/                     # 视频捕获实现
│   └── video_capture_main.cpp   # 视频捕获主程序
├── config/                      # 配置管理实现
│   ├── video_control.cpp        # 视频控制实现
│   ├── video_control_main.cpp   # 视频控制主程序
│   └── video_transport_config.cpp # 传输配置实现
├── processing/                  # 视频处理实现
│   ├── ai_processor_main.cpp    # AI处理主程序
│   └── video_converter_main.cpp # 视频转换主程序
├── streaming/                   # 流媒体实现
│   ├── cloud_streamer_main.cpp  # 云端流媒体主程序
│   ├── rtsp_server.cpp          # RTSP服务器实现
│   ├── rtsp_server_dds.cpp      # RTSP服务器DDS集成
│   ├── rtsp_server_main.cpp     # RTSP服务器主程序
│   └── rtsp_server_pipe.cpp     # RTSP服务器管道
└── transport/                   # 传输抽象层实现
    ├── dma_video_transport.cpp  # DMA视频传输实现
    ├── fastdds_video_transport.cpp # FastDDS视频传输实现
    └── video_transport_factory.cpp # 传输工厂实现
```

## 🔧 模块分类说明

### 1. Transport（传输抽象层）
**目的**: 统一视频帧传输接口，支持FastDDS和DMA两种传输机制
- `video_transport_interface.h`: 抽象接口定义
- `fastdds_video_transport.*`: FastDDS适配器
- `dma_video_transport.*`: DMA适配器
- `dma_buffer_manager.h`: DMA缓冲区管理
- `dma_distribution.h`: DMA分发机制
- `video_transport_factory.cpp`: 工厂模式实现

### 2. Capture（视频捕获）
**目的**: 处理各种视频输入源的捕获
- V4L2设备捕获
- RTSP流捕获
- 硬件加速捕获
- 捕获配置管理

### 3. Processing（视频处理）
**目的**: 视频内容的处理和转换
- AI推理处理（ONNX/TensorRT）
- 视频格式转换
- GStreamer编码
- 实时视频处理

### 4. Streaming（流媒体）
**目的**: 视频流的分发和传输
- RTSP服务器
- RTSP客户端
- 云端流媒体推送
- 网络流媒体协议

### 5. Hardware（硬件加速）
**目的**: 硬件相关的加速功能
- RGA加速器（Rockchip）
- MPP解码器（Rockchip）
- DMA操作
- 硬件特定优化

### 6. Config（配置管理）
**目的**: 系统配置和工具类
- 配置文件加载
- 传输配置管理
- 视频控制
- 线程池等工具

## 🔄 包含路径更新

为了适应新的目录结构，已更新了以下文件的包含路径：

1. **传输模块文件**:
   - `fastdds_video_transport.h`: 更新了DDS头文件路径
   - `video_transport_interface.h`: 更新了common.h路径
   - `fastdds_video_transport.cpp`: 更新了头文件路径
   - `dma_video_transport.cpp`: 更新了头文件路径
   - `video_transport_factory.cpp`: 更新了所有头文件路径

2. **配置模块文件**:
   - `video_transport_config.h`: 更新了transport接口路径
   - `video_transport_config.cpp`: 更新了头文件路径

## 🛠️ 构建系统更新

需要更新构建系统（CMakeLists.txt或Makefile）以反映新的目录结构：

```cmake
# 添加新的包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include/transport
    ${CMAKE_CURRENT_SOURCE_DIR}/include/capture
    ${CMAKE_CURRENT_SOURCE_DIR}/include/processing
    ${CMAKE_CURRENT_SOURCE_DIR}/include/streaming
    ${CMAKE_CURRENT_SOURCE_DIR}/include/hardware
    ${CMAKE_CURRENT_SOURCE_DIR}/include/config
)

# 更新源文件路径
set(TRANSPORT_SOURCES
    src/transport/fastdds_video_transport.cpp
    src/transport/dma_video_transport.cpp
    src/transport/video_transport_factory.cpp
)

set(CONFIG_SOURCES
    src/config/video_transport_config.cpp
    src/config/video_control.cpp
)

# ... 其他模块类似
```

## 📈 优势

1. **模块化**: 清晰的功能边界，便于维护
2. **可扩展性**: 新功能可以轻松添加到对应模块
3. **团队协作**: 不同团队可以专注于不同模块
4. **依赖管理**: 更清晰的模块间依赖关系
5. **测试友好**: 每个模块可以独立测试

## 🎯 下一步建议

1. **更新构建脚本**: 修改CMakeLists.txt和Makefile
2. **更新文档**: 同步更新API文档和架构图
3. **IDE配置**: 更新IDE项目文件的包含路径
4. **持续集成**: 更新CI/CD脚本的构建路径
5. **团队通知**: 通知开发团队新的目录结构

这次重组为项目带来了更好的代码组织结构，特别是新的传输抽象层使得FastDDS和DMA传输机制能够无缝共存和切换。