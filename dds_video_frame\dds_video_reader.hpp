#pragma once

#include <fastdds/dds/domain/DomainParticipant.hpp>
#include <fastdds/dds/domain/DomainParticipantFactory.hpp>
#include <fastdds/dds/topic/TypeSupport.hpp>
#include <fastdds/dds/topic/Topic.hpp>
#include <fastdds/dds/subscriber/Subscriber.hpp>
#include <fastdds/dds/subscriber/DataReader.hpp>
#include <fastdds/dds/subscriber/SampleInfo.hpp>
#include <fastdds/dds/subscriber/DataReaderListener.hpp>

#include "DDSVideoFramePubSubTypes.hpp"

namespace eprosima {
namespace fastdds {
namespace video {
// DDS Reader封装
class DDSReader : public dds::DataReaderListener{
private:
    dds::DomainParticipant* participant_;
    dds::Subscriber* subscriber_;
    dds::Topic* topic_;
    dds::DataReader* reader_;
    dds::TypeSupport type_;
    int max_samples_;
    std::function<void(const DDSVideoFrame&)> callback_;
    
public:
    DDSReader() :
        participant_(nullptr),
        subscriber_(nullptr),
        topic_(nullptr),
        reader_(nullptr) {}
    
    ~DDSReader() {
        cleanup();
    }
    
    bool init(const std::string& topic_name, int domain_id, int max_samples = 3, std::function<void(const DDSVideoFrame&)> callback = nullptr);
    void on_data_available(dds::DataReader* reader) override;
    void on_subscription_matched(dds::DataReader* reader, const dds::SubscriptionMatchedStatus& info) override;
    bool wait_for_data(DDSVideoFrame& frame,int timeout_ms = 1000);
    
private:
    void cleanup();
};
} // namespace video
} // namespace fastdds
} // namespace eprosima
