/**
 * Video Control Service API Example
 * 
 * This example demonstrates how to use the public methods of VideoControlService
 * for external control of dual video recording and photo capture.
 */

#include "video_control.h"
#include "config_loader.h"
#include <iostream>
#include <chrono>
#include <thread>

class VideoControlAPIExample {
public:
    VideoControlAPIExample() {
        // Initialize service
        service_ = std::make_unique<VideoControlService>();
    }
    
    ~VideoControlAPIExample() {
        if (service_) {
            service_->stop();
        }
    }
    
    bool initialize() {
        std::cout << "=== Video Control Service API Example ===" << std::endl;
        
        // Load configuration
        VideoControlConfig config;
        if (!ConfigLoader::load_video_control_config("config/video_control.json", config)) {
            std::cout << "Warning: Failed to load config, using defaults" << std::endl;
        }
        
        // Override for example
        config.udp_port = 14553; // Use different port to avoid conflicts
        config.sdcard_mount_path = "/mnt/sdcard";
        config.photo_save_path = "/mnt/sdcard/photos";
        config.video_save_path = "/mnt/sdcard/videos";
        
        std::cout << "Configuration:" << std::endl;
        std::cout << "  Visible Stream: " << config.visible_stream.dds_topic 
                  << " (" << config.visible_stream.width << "x" << config.visible_stream.height 
                  << "@" << config.visible_stream.fps << "fps)" << std::endl;
        std::cout << "  Infrared Stream: " << config.infrared_stream.dds_topic 
                  << " (" << config.infrared_stream.width << "x" << config.infrared_stream.height 
                  << "@" << config.infrared_stream.fps << "fps)" << std::endl;
        
        // Initialize and start service
        if (!service_->init(config)) {
            std::cout << "ERROR: Failed to initialize service" << std::endl;
            return false;
        }
        
        if (!service_->start()) {
            std::cout << "ERROR: Failed to start service" << std::endl;
            return false;
        }
        
        std::cout << "✓ Service initialized and started successfully" << std::endl;
        
        // Wait for service to be ready
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        return true;
    }
    
    void demonstrate_photo_capture() {
        std::cout << "\n--- Demonstrating Photo Capture ---" << std::endl;
        
        // Capture dual photos using public method
        std::cout << "Capturing dual photos..." << std::endl;
        PhotoResult result = service_->capture_photo_dual();
        
        if (result.success) {
            std::cout << "✓ Photo capture successful!" << std::endl;
            std::cout << "  Photos: " << result.photo_path << std::endl;
            std::cout << "  Total size: " << result.photo_size_bytes << " bytes" << std::endl;
            std::cout << "  Timestamp: " << result.capture_timestamp << std::endl;
        } else {
            std::cout << "✗ Photo capture failed: " << result.error_message << std::endl;
            std::cout << "  Note: This is expected without actual DDS video streams" << std::endl;
        }
        
        // Demonstrate single photo capture with mock data
        std::cout << "\nCapturing single photo with mock data..." << std::endl;
        Frame mock_frame;
        mock_frame.width = 640;
        mock_frame.height = 480;
        mock_frame.format = 1196444237; // MJPEG
        mock_frame.timestamp = get_current_us();
        mock_frame.data.resize(640 * 480 * 3 / 2); // YUV420 size
        mock_frame.valid = true;
        
        // Fill with test pattern
        for (size_t i = 0; i < mock_frame.data.size(); ++i) {
            mock_frame.data[i] = static_cast<uint8_t>((i * 123) % 256);
        }
        
        PhotoResult single_result = service_->capture_single_photo(mock_frame, "example", nullptr);
        if (single_result.success) {
            std::cout << "✓ Single photo capture successful!" << std::endl;
            std::cout << "  Photo: " << single_result.photo_path << std::endl;
        } else {
            std::cout << "✗ Single photo capture failed: " << single_result.error_message << std::endl;
            std::cout << "  Note: This is expected without JPEG encoder" << std::endl;
        }
    }
    
    void demonstrate_recording_control() {
        std::cout << "\n--- Demonstrating Recording Control ---" << std::endl;
        
        // Check initial recording status
        SDCardStatus status = service_->get_sdcard_status();
        std::cout << "Initial recording status: " << (status.is_recording ? "ACTIVE" : "INACTIVE") << std::endl;
        
        // Start recording using public method
        std::cout << "\nStarting dual video recording..." << std::endl;
        bool start_result = service_->start_recording_session();
        
        if (start_result) {
            std::cout << "✓ Recording started successfully!" << std::endl;
            
            // Check recording status
            std::this_thread::sleep_for(std::chrono::seconds(1));
            status = service_->get_sdcard_status();
            std::cout << "  Recording status: " << (status.is_recording ? "ACTIVE" : "INACTIVE") << std::endl;
            std::cout << "  Current video file: " << status.current_video_file << std::endl;
            
            // Let it record for a few seconds
            std::cout << "  Recording for 5 seconds..." << std::endl;
            for (int i = 1; i <= 5; ++i) {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                std::cout << "    " << i << "/5 seconds..." << std::endl;
            }
            
            // Get statistics during recording
            VideoControlService::Stats stats;
            service_->get_stats(stats);
            std::cout << "  Recording statistics:" << std::endl;
            std::cout << "    Video segments: " << stats.video_segments_created << std::endl;
            std::cout << "    Data written: " << stats.total_data_written_mb << " MB" << std::endl;
            
        } else {
            std::cout << "✗ Failed to start recording" << std::endl;
            std::cout << "  Note: This may be due to SD card or GStreamer issues" << std::endl;
        }
        
        // Stop recording using public method
        std::cout << "\nStopping dual video recording..." << std::endl;
        bool stop_result = service_->stop_recording_session();
        
        if (stop_result) {
            std::cout << "✓ Recording stopped successfully!" << std::endl;
            
            // Check final status
            std::this_thread::sleep_for(std::chrono::seconds(1));
            status = service_->get_sdcard_status();
            std::cout << "  Final recording status: " << (status.is_recording ? "ACTIVE" : "INACTIVE") << std::endl;
            
        } else {
            std::cout << "✗ Failed to stop recording" << std::endl;
        }
    }
    
    void demonstrate_multiple_cycles() {
        std::cout << "\n--- Demonstrating Multiple Recording Cycles ---" << std::endl;
        
        for (int cycle = 1; cycle <= 3; ++cycle) {
            std::cout << "\nCycle " << cycle << "/3:" << std::endl;
            
            // Start recording
            std::cout << "  Starting recording..." << std::endl;
            bool start_result = service_->start_recording_session();
            
            if (start_result) {
                std::cout << "  ✓ Recording started" << std::endl;
                
                // Record for 2 seconds
                std::this_thread::sleep_for(std::chrono::seconds(2));
                
                // Stop recording
                std::cout << "  Stopping recording..." << std::endl;
                bool stop_result = service_->stop_recording_session();
                
                if (stop_result) {
                    std::cout << "  ✓ Recording stopped" << std::endl;
                } else {
                    std::cout << "  ✗ Failed to stop recording" << std::endl;
                }
                
            } else {
                std::cout << "  ✗ Failed to start recording" << std::endl;
            }
            
            // Small delay between cycles
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        
        std::cout << "✓ Multiple recording cycles completed" << std::endl;
    }
    
    void show_final_statistics() {
        std::cout << "\n--- Final Statistics ---" << std::endl;
        
        VideoControlService::Stats stats;
        service_->get_stats(stats);
        
        std::cout << "Service Statistics:" << std::endl;
        std::cout << "  Mavlink messages received: " << stats.mavlink_messages_received << std::endl;
        std::cout << "  Mavlink messages processed: " << stats.mavlink_messages_processed << std::endl;
        std::cout << "  Photos taken: " << stats.photos_taken << std::endl;
        std::cout << "  Photos failed: " << stats.photos_failed << std::endl;
        std::cout << "  Recording sessions: " << stats.recording_sessions << std::endl;
        std::cout << "  Video segments created: " << stats.video_segments_created << std::endl;
        std::cout << "  Total recording duration: " << stats.total_recording_duration_sec << " seconds" << std::endl;
        std::cout << "  Total data written: " << stats.total_data_written_mb << " MB" << std::endl;
        std::cout << "  CPU usage: " << stats.cpu_usage << "%" << std::endl;
        std::cout << "  Memory usage: " << stats.memory_usage_mb << " MB" << std::endl;
        
        SDCardStatus sd_status = service_->get_sdcard_status();
        std::cout << "\nSD Card Status:" << std::endl;
        std::cout << "  Mounted: " << (sd_status.is_mounted ? "YES" : "NO") << std::endl;
        std::cout << "  Total capacity: " << sd_status.total_capacity_mb << " MB" << std::endl;
        std::cout << "  Available: " << sd_status.available_capacity_mb << " MB" << std::endl;
        std::cout << "  Usage: " << sd_status.usage_percentage << "%" << std::endl;
    }
    
private:
    std::unique_ptr<VideoControlService> service_;
};

int main() {
    // Initialize GStreamer
    gst_init(nullptr, nullptr);
    
    VideoControlAPIExample example;
    
    try {
        // Initialize the service
        if (!example.initialize()) {
            std::cerr << "Failed to initialize video control service" << std::endl;
            return 1;
        }
        
        // Demonstrate photo capture
        example.demonstrate_photo_capture();
        
        // Demonstrate recording control
        example.demonstrate_recording_control();
        
        // Demonstrate multiple cycles
        example.demonstrate_multiple_cycles();
        
        // Show final statistics
        example.show_final_statistics();
        
        std::cout << "\n=== API Example Completed Successfully ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
