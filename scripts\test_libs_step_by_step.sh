#!/bin/bash

# 逐步测试库依赖的脚本
# Step-by-step library dependency testing script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/build"

echo -e "${BLUE}=== 逐步测试库依赖 ===${NC}"
echo "构建目录: $BUILD_DIR"
echo ""

# 检查构建目录
if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}错误: 构建目录不存在: $BUILD_DIR${NC}"
    echo "请先运行: cd build && make"
    exit 1
fi

# 测试程序列表（按依赖复杂度排序）
TEST_PROGRAMS=(
    "test_minimal:最小测试(只有系统库)"
    "test_with_ffmpeg:FFmpeg库测试"
    "test_with_gstreamer:GStreamer库测试"
    "test_with_jsoncpp:JsonCpp库测试"
    "test_with_fastdds:FastDDS库测试"
    "test_with_opencv:OpenCV库测试"
    "test_safe_libs:安全库测试(不含硬件库)"
    "test_with_rga:RGA硬件库测试"
    "test_with_rknn:RKNN硬件库测试"
    "test_with_mpp:MPP硬件库测试"
    "test_rga_rknn:RGA+RKNN组合测试"
    "test_rga_mpp:RGA+MPP组合测试"
    "test_rknn_mpp:RKNN+MPP组合测试"
    "test_basic_libs:完整库测试(含所有硬件库)"
)

# 运行单个测试
run_test() {
    local test_name="$1"
    local description="$2"
    local test_path="$BUILD_DIR/$test_name"
    
    echo -e "${BLUE}测试: $description${NC}"
    
    if [ ! -f "$test_path" ]; then
        echo -e "${RED}✗ 程序不存在: $test_path${NC}"
        return 1
    fi
    
    if [ ! -x "$test_path" ]; then
        echo -e "${RED}✗ 程序没有执行权限${NC}"
        return 1
    fi
    
    # 运行测试，设置超时
    if timeout 10s "$test_path" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ $test_name 运行成功${NC}"
        return 0
    else
        local exit_code=$?
        echo -e "${RED}✗ $test_name 运行失败 (退出码: $exit_code)${NC}"
        
        # 尝试获取更多信息
        echo "  尝试获取错误信息..."
        timeout 5s "$test_path" 2>&1 | head -5 | sed 's/^/    /'
        
        return 1
    fi
}

# 主测试循环
echo -e "${YELLOW}开始逐步测试...${NC}"
echo ""

failed_tests=()
successful_tests=()

for test_info in "${TEST_PROGRAMS[@]}"; do
    IFS=':' read -r test_name description <<< "$test_info"
    
    if run_test "$test_name" "$description"; then
        successful_tests+=("$test_name")
    else
        failed_tests+=("$test_name")
        echo -e "${YELLOW}在 $test_name 处停止，这可能是问题所在${NC}"
    fi
    echo ""
done

# 总结结果
echo -e "${YELLOW}=== 测试结果总结 ===${NC}"

if [ ${#successful_tests[@]} -gt 0 ]; then
    echo -e "${GREEN}成功的测试:${NC}"
    for test in "${successful_tests[@]}"; do
        echo "  ✓ $test"
    done
fi

if [ ${#failed_tests[@]} -gt 0 ]; then
    echo -e "${RED}失败的测试:${NC}"
    for test in "${failed_tests[@]}"; do
        echo "  ✗ $test"
    done
    echo ""
    echo -e "${YELLOW}调试建议:${NC}"
    echo "1. 第一个失败的测试指向了问题库"
    echo "2. 检查该库的安装和版本"
    echo "3. 检查交叉编译工具链配置"
    echo "4. 运行失败的测试程序查看详细错误:"
    echo "   $BUILD_DIR/${failed_tests[0]}"
else
    echo -e "${GREEN}所有测试都通过了！${NC}"
fi

echo ""
echo -e "${BLUE}=== 额外调试信息 ===${NC}"

# 检查第一个失败的测试程序的依赖
if [ ${#failed_tests[@]} -gt 0 ]; then
    first_failed="${failed_tests[0]}"
    echo "检查 $first_failed 的库依赖:"
    
    if command -v ldd >/dev/null 2>&1; then
        ldd_output=$(ldd "$BUILD_DIR/$first_failed" 2>&1)
        if echo "$ldd_output" | grep -q "not found"; then
            echo -e "${RED}发现缺失的库:${NC}"
            echo "$ldd_output" | grep "not found" | sed 's/^/  /'
        else
            echo -e "${GREEN}所有库依赖都找到了${NC}"
        fi
    else
        echo "ldd 命令不可用"
    fi
fi

echo ""
echo -e "${BLUE}完成逐步测试${NC}"
