#include "../include/transport/video_transport_interface.h"
#include "../include/transport/fastdds_video_transport.h"
#include "../include/transport/buffer_share_video_transport.h"
#include "../include/transport/simplified_video_transport.h"
#include <stdexcept>

namespace video_transport {

// IoUringProducer/Consumer now in video_transport namespace

// VideoTransportFactory实现
std::unique_ptr<IVideoPublisher> VideoTransportFactory::create_publisher(TransportType type) {
    switch (type) {
        case TransportType::FASTDDS:
            return std::make_unique<FastDDSVideoPublisher>();
        
        case TransportType::DMA:
        case TransportType::SHMEM:
            // Use simplified transport for better performance and reduced complexity
            return std::make_unique<simplified_transport::SimplifiedProducer>();
        
        default:
            throw std::invalid_argument("Unsupported transport type for publisher");
    }
}

std::unique_ptr<IVideoSubscriber> VideoTransportFactory::create_subscriber(TransportType type) {
    switch (type) {
        case TransportType::FASTDDS:
            return std::make_unique<FastDDSVideoSubscriber>();
        
        case TransportType::DMA:
        case TransportType::SHMEM:
            // Use simplified transport for better performance and reduced complexity
            return std::make_unique<simplified_transport::SimplifiedConsumer>();
        
        default:
            throw std::invalid_argument("Unsupported transport type for subscriber");
    }
}

std::unique_ptr<IVideoPublisher> VideoTransportFactory::create_publisher(const TransportConfig& config) {
    auto publisher = create_publisher(config.type);
    if (!publisher->initialize(config)) {
        throw std::runtime_error("Failed to initialize publisher with provided config");
    }
    
    return publisher;
}

std::unique_ptr<IVideoSubscriber> VideoTransportFactory::create_subscriber(const TransportConfig& config) {
    auto subscriber = create_subscriber(config.type);
    if (!subscriber->initialize(config)) {
        throw std::runtime_error("Failed to initialize subscriber with provided config");
    }
    
    return subscriber;
}

std::vector<TransportType> VideoTransportFactory::get_supported_types() {
    return {
        TransportType::FASTDDS,
        TransportType::DMA,
        TransportType::SHMEM
    };
}

} // namespace video_transport