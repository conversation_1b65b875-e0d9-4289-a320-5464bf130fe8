#include <vector>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <type_traits>

class ThreadPool {
public:
    explicit ThreadPool(size_t threads) : stop(false) {
        for (size_t i = 0; i < threads; ++i) {
            workers.emplace_back([this] {
                for (;;) {
                    std::function<void()> task;
                    {
                        std::unique_lock<std::mutex> lock(queue_mutex);
                        condition.wait(lock, [this] { 
                            return stop || !tasks.empty(); 
                        });
                        
                        if (stop && tasks.empty()) return;
                        
                        task = std::move(tasks.front());
                        tasks.pop();
                    }
                    task();
                }
            });
        }
    }

    // 提交任务并返回 future
    template <class F, class... Args>
    auto submit(F&& f, Args&&... args) 
        -> std::future<std::invoke_result_t<F, Args...>> {
        
        using return_type = std::invoke_result_t<F, Args...>;
        
        // 创建 packaged_task 并获取 future
        auto task_ptr = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        std::future<return_type> res = task_ptr->get_future();
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            if (stop) throw std::runtime_error("submit on stopped ThreadPool");
            
            // 包装任务为 void() 类型
            tasks.emplace([task_ptr]() { (*task_ptr)(); });
        }
        condition.notify_one();
        return res;
    }

    // 批量提交任务并返回 future 集合
    template <class F, class... Args>
    auto submit_batch(size_t count, F&& f, Args&&... args) 
        -> std::vector<std::future<std::invoke_result_t<F, Args...>>> {
        
        std::vector<std::future<std::invoke_result_t<F, Args...>>> futures;
        futures.reserve(count);
        
        for (size_t i = 0; i < count; ++i) {
            futures.push_back(submit(f, args...));
        }
        return futures;
    }

    // 等待所有任务完成（通过 future 集合）
    template <typename T>
    static void wait_all(std::vector<std::future<T>>& futures) {
        for (auto& fut : futures) {
            fut.wait();
        }
    }

    // 检查所有任务是否完成（通过 future 集合）
    template <typename T>
    static bool all_done(const std::vector<std::future<T>>& futures) {
        for (const auto& fut : futures) {
            if (fut.wait_for(std::chrono::seconds(0)) != std::future_status::ready) {
                return false;
            }
        }
        return true;
    }

    // 停止线程池
    void shutdown() {
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            stop = true;
        }
        condition.notify_all();
        for (std::thread& worker : workers) {
            worker.join();
        }
        workers.clear();
    }

    ~ThreadPool() {
        if (!stop) {
            shutdown();
        }
    }

private:
    std::vector<std::thread> workers;
    std::queue<std::function<void()>> tasks;
    std::mutex queue_mutex;
    std::condition_variable condition;
    bool stop;
};