#include "mpp_decoder.h"
#include "common.h"
#include <iostream>
#include <chrono>
#include <vector>
#include <fstream>
#include <string>
#include <map>

// 从文件加载MJPEG帧数据
bool load_mjpeg_from_file(Frame& frame, const std::string& filename, int width, int height) {
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot open MJPEG file: " << filename << std::endl;
        return false;
    }

    // 获取文件大小
    file.seekg(0, std::ios::end);
    size_t file_size = file.tellg();
    file.seekg(0, std::ios::beg);

    if (file_size == 0) {
        std::cerr << "Error: MJPEG file is empty: " << filename << std::endl;
        return false;
    }

    // 读取文件内容
    std::vector<uint8_t> jpeg_data(file_size);
    file.read(reinterpret_cast<char*>(jpeg_data.data()), file_size);
    file.close();

    // 验证JPEG文件头
    if (jpeg_data.size() < 2 || jpeg_data[0] != 0xFF || jpeg_data[1] != 0xD8) {
        std::cerr << "Error: Invalid JPEG file format (missing SOI marker): " << filename << std::endl;
        return false;
    }

    // 验证JPEG文件尾
    if (jpeg_data.size() < 2 ||
        jpeg_data[jpeg_data.size()-2] != 0xFF ||
        jpeg_data[jpeg_data.size()-1] != 0xD9) {
        std::cerr << "Error: Invalid JPEG file format (missing EOI marker): " << filename << std::endl;
        return false;
    }

    // 设置Frame信息
    frame.width = width;
    frame.height = height;
    frame.format = V4L2_PIX_FMT_MJPEG;
    frame.frame_id = 1;
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    frame.source_type = 0;
    frame.is_keyframe = true;
    frame.valid = true;
    frame.data = jpeg_data;

    std::cout << "Successfully loaded MJPEG file: " << filename
              << " (size: " << file_size << " bytes)" << std::endl;
    return true;
}

// 生成简单的测试MJPEG帧数据（用于标准测试）
void generate_simple_test_mjpeg_frame(Frame& frame, int width, int height) {
    frame.width = width;
    frame.height = height;
    frame.format = V4L2_PIX_FMT_MJPEG;
    frame.frame_id = 1;
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    frame.source_type = 0;
    frame.is_keyframe = true;
    frame.valid = true;

    // 生成一个最小的合法JPEG图像（8x8灰度图像）
    std::vector<uint8_t> jpeg_data = {
        // SOI (Start of Image)
        0xFF, 0xD8,
        // APP0 (JFIF header)
        0xFF, 0xE0, 0x00, 0x10,
        0x4A, 0x46, 0x49, 0x46, 0x00,  // "JFIF\0"
        0x01, 0x01, 0x01,              // Version 1.1, units
        0x00, 0x48, 0x00, 0x48,        // X and Y density (72 DPI)
        0x00, 0x00,                    // Thumbnail width and height
        // DQT (Define Quantization Table)
        0xFF, 0xDB, 0x00, 0x43, 0x00,
        0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07,
        0x07, 0x07, 0x09, 0x09, 0x08, 0x0A, 0x0C, 0x14,
        0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12, 0x13,
        0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A,
        0x1C, 0x1C, 0x20, 0x24, 0x2E, 0x27, 0x20, 0x22,
        0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29, 0x2C,
        0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39,
        0x3D, 0x38, 0x32, 0x3C, 0x2E, 0x33, 0x34, 0x32,
        // SOF0 (Start of Frame)
        0xFF, 0xC0, 0x00, 0x11, 0x08,
        0x00, 0x08, 0x00, 0x08,        // Height=8, Width=8
        0x01, 0x01, 0x11, 0x00,        // 1 component, grayscale
        // DHT (Define Huffman Table) - DC
        0xFF, 0xC4, 0x00, 0x1F, 0x00,
        0x00, 0x01, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B,
        // DHT (Define Huffman Table) - AC
        0xFF, 0xC4, 0x00, 0xB5, 0x10,
        0x00, 0x02, 0x01, 0x03, 0x03, 0x02, 0x04, 0x03, 0x05, 0x05, 0x04, 0x04, 0x00, 0x00, 0x01, 0x7D,
        0x01, 0x02, 0x03, 0x00, 0x04, 0x11, 0x05, 0x12, 0x21, 0x31, 0x41, 0x06, 0x13, 0x51, 0x61,
        0x07, 0x22, 0x71, 0x14, 0x32, 0x81, 0x91, 0xA1, 0x08, 0x23, 0x42, 0xB1, 0xC1, 0x15, 0x52, 0xD1, 0xF0,
        0x24, 0x33, 0x62, 0x72, 0x82, 0x09, 0x0A, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x25, 0x26, 0x27, 0x28,
        0x29, 0x2A, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49,
        0x4A, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5A, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
        0x6A, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7A, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89,
        0x8A, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99, 0x9A, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7,
        0xA8, 0xA9, 0xAA, 0xB2, 0xB3, 0xB4, 0xB5, 0xB6, 0xB7, 0xB8, 0xB9, 0xBA, 0xC2, 0xC3, 0xC4, 0xC5,
        0xC6, 0xC7, 0xC8, 0xC9, 0xCA, 0xD2, 0xD3, 0xD4, 0xD5, 0xD6, 0xD7, 0xD8, 0xD9, 0xDA, 0xE1, 0xE2,
        0xE3, 0xE4, 0xE5, 0xE6, 0xE7, 0xE8, 0xE9, 0xEA, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7, 0xF8,
        0xF9, 0xFA,
        // SOS (Start of Scan)
        0xFF, 0xDA, 0x00, 0x08, 0x01, 0x01, 0x00, 0x00, 0x3F, 0x00,
        // Compressed image data
        0xD2, 0xCF, 0x20,
        // EOI (End of Image)
        0xFF, 0xD9
    };

    frame.data = jpeg_data;
}

// 生成合法的H264帧数据（最小的IDR帧）
void generate_test_h264_frame(Frame& frame, int width, int height) {
    frame.width = width;
    frame.height = height;
    frame.format = V4L2_PIX_FMT_H264;
    frame.frame_id = 1;
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    frame.source_type = 0;
    frame.is_keyframe = true;
    frame.valid = true;

    // 生成合法的H264 NAL单元序列
    std::vector<uint8_t> h264_data;

    // SPS (Sequence Parameter Set) - NALU type 7
    std::vector<uint8_t> sps = {
        0x00, 0x00, 0x00, 0x01,  // Start code
        0x67,                    // NALU header: forbidden_zero_bit(0) + nal_ref_idc(3) + nal_unit_type(7)
        0x42, 0x00, 0x1E,        // profile_idc=66 (Baseline), constraint_set_flags, level_idc=30
        0xFF, 0xE1, 0x00, 0x18,  // SPS data start
        0x67, 0x42, 0x00, 0x1E, 0xFF, 0xE1, 0x00, 0x18,
        0x67, 0x42, 0x00, 0x1E, 0x8D, 0x68, 0x05, 0x8B,
        0x7C, 0x04, 0x04, 0x04, 0x08, 0x08, 0x10, 0x00,
        0x00, 0x03, 0x00, 0x10, 0x00, 0x00, 0x03, 0x03,
        0x20, 0xF1, 0x42, 0x99, 0x60
    };

    // PPS (Picture Parameter Set) - NALU type 8
    std::vector<uint8_t> pps = {
        0x00, 0x00, 0x00, 0x01,  // Start code
        0x68,                    // NALU header: forbidden_zero_bit(0) + nal_ref_idc(3) + nal_unit_type(8)
        0xCE, 0x3C, 0x80         // PPS data (minimal)
    };

    // IDR slice - NALU type 5 (I-frame)
    std::vector<uint8_t> idr_slice = {
        0x00, 0x00, 0x00, 0x01,  // Start code
        0x65,                    // NALU header: forbidden_zero_bit(0) + nal_ref_idc(3) + nal_unit_type(5)
        0x88, 0x84, 0x21, 0xA0,  // Slice header and data (minimal)
        0x02, 0x1F, 0xFF, 0xC4,
        0x00, 0x04, 0x06, 0x0C,
        0x08, 0x10, 0x18, 0x20,
        0x24, 0x28, 0x2C, 0x30,
        0x34, 0x38, 0x3C, 0x40
    };

    // 组合所有NALU
    h264_data.insert(h264_data.end(), sps.begin(), sps.end());
    h264_data.insert(h264_data.end(), pps.begin(), pps.end());
    h264_data.insert(h264_data.end(), idr_slice.begin(), idr_slice.end());

    frame.data = h264_data;
}

// 生成合法的H265帧数据（最小的IDR帧）
void generate_test_h265_frame(Frame& frame, int width, int height) {
    frame.width = width;
    frame.height = height;
    frame.format = V4L2_PIX_FMT_H265;
    frame.frame_id = 1;
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    frame.source_type = 0;
    frame.is_keyframe = true;
    frame.valid = true;

    // 生成合法的H265 NAL单元序列
    std::vector<uint8_t> h265_data;

    // VPS (Video Parameter Set) - NALU type 32
    std::vector<uint8_t> vps = {
        0x00, 0x00, 0x00, 0x01,  // Start code
        0x40, 0x01,              // NALU header: forbidden_zero_bit(0) + nal_unit_type(32) + nuh_layer_id(0) + nuh_temporal_id_plus1(1)
        0x0C, 0x01, 0xFF, 0xFF,  // VPS data
        0x01, 0x60, 0x00, 0x00,
        0x03, 0x00, 0xB0, 0x00,
        0x00, 0x03, 0x00, 0x00,
        0x03, 0x00, 0x5D, 0xAC,
        0x59
    };

    // SPS (Sequence Parameter Set) - NALU type 33
    std::vector<uint8_t> sps = {
        0x00, 0x00, 0x00, 0x01,  // Start code
        0x42, 0x01,              // NALU header: forbidden_zero_bit(0) + nal_unit_type(33) + nuh_layer_id(0) + nuh_temporal_id_plus1(1)
        0x01, 0x01, 0x60, 0x00,  // SPS data
        0x00, 0x03, 0x00, 0xB0,
        0x00, 0x00, 0x03, 0x00,
        0x00, 0x03, 0x00, 0x5D,
        0xA0, 0x02, 0x80, 0x80,
        0x2D, 0x16, 0x59, 0x59,
        0xA4, 0x93, 0x2B, 0xC0,
        0x5A, 0x70, 0x80, 0x80,
        0x80, 0x82
    };

    // PPS (Picture Parameter Set) - NALU type 34
    std::vector<uint8_t> pps = {
        0x00, 0x00, 0x00, 0x01,  // Start code
        0x44, 0x01,              // NALU header: forbidden_zero_bit(0) + nal_unit_type(34) + nuh_layer_id(0) + nuh_temporal_id_plus1(1)
        0xC1, 0x73, 0xD1, 0x89   // PPS data (minimal)
    };

    // IDR slice - NALU type 19 (IDR_W_RADL)
    std::vector<uint8_t> idr_slice = {
        0x00, 0x00, 0x00, 0x01,  // Start code
        0x26, 0x01,              // NALU header: forbidden_zero_bit(0) + nal_unit_type(19) + nuh_layer_id(0) + nuh_temporal_id_plus1(1)
        0xAF, 0x06, 0xB8, 0x18,  // Slice header and data (minimal)
        0x31, 0x8F, 0x22, 0x76,
        0x08, 0x20, 0x00, 0x48,
        0x00, 0x00, 0x1E, 0x84,
        0x80, 0x00, 0x00, 0x07,
        0xA1, 0x20, 0x00, 0x00,
        0x01, 0xE8, 0x48, 0x00,
        0x00, 0x00, 0x7A, 0x12
    };

    // 组合所有NALU
    h265_data.insert(h265_data.end(), vps.begin(), vps.end());
    h265_data.insert(h265_data.end(), sps.begin(), sps.end());
    h265_data.insert(h265_data.end(), pps.begin(), pps.end());
    h265_data.insert(h265_data.end(), idr_slice.begin(), idr_slice.end());

    frame.data = h265_data;
}

// 打印帧信息
void print_frame_info(const Frame& frame, const std::string& name) {
    std::cout << name << " Frame Info:" << std::endl;
    std::cout << "  Size: " << frame.width << "x" << frame.height << std::endl;
    std::cout << "  Format: 0x" << std::hex << frame.format << std::dec << std::endl;
    std::cout << "  Data size: " << frame.data.size() << " bytes" << std::endl;
    std::cout << "  Valid: " << (frame.valid ? "true" : "false") << std::endl;
    std::cout << "  Keyframe: " << (frame.is_keyframe ? "true" : "false") << std::endl;
    std::cout << std::endl;
}

// 测试基本初始化 - 每个实例支持一种编码格式
bool test_mpp_decoder_init() {
    std::cout << "=== Testing MPP Decoder Initialization ===" << std::endl;

    bool all_passed = true;

    // 测试MJPEG格式初始化
    {
        MPPDecoder mjpeg_decoder(MPP_DECODER_TYPE_MJPEG);
        bool mjpeg_result = mjpeg_decoder.init(1280, 720);
        if (mjpeg_result) {
            std::cout << "✓ MPP MJPEG decoder initialization successful for 1280x720" << std::endl;
            std::cout << "  Decoder type: " << mjpeg_decoder.get_decoder_type_name() << std::endl;
        } else {
            std::cout << "✗ MPP MJPEG decoder initialization failed for 1280x720" << std::endl;
            all_passed = false;
        }
    }

    // 测试H264格式初始化
    {
        MPPDecoder h264_decoder(MPP_DECODER_TYPE_H264);
        bool h264_result = h264_decoder.init(1920, 1080);
        if (h264_result) {
            std::cout << "✓ MPP H264 decoder initialization successful for 1920x1080" << std::endl;
            std::cout << "  Decoder type: " << h264_decoder.get_decoder_type_name() << std::endl;
        } else {
            std::cout << "✗ MPP H264 decoder initialization failed for 1920x1080" << std::endl;
            all_passed = false;
        }
    }

    // 测试H265格式初始化
    {
        MPPDecoder h265_decoder(MPP_DECODER_TYPE_H265);
        bool h265_result = h265_decoder.init(3840, 2160);
        if (h265_result) {
            std::cout << "✓ MPP H265 decoder initialization successful for 3840x2160" << std::endl;
            std::cout << "  Decoder type: " << h265_decoder.get_decoder_type_name() << std::endl;
        } else {
            std::cout << "✗ MPP H265 decoder initialization failed for 3840x2160" << std::endl;
            all_passed = false;
        }
    }

    // 测试兼容的V4L2接口
    {
        MPPDecoder v4l2_decoder(V4L2_PIX_FMT_MJPEG);
        bool v4l2_result = v4l2_decoder.init(1280, 720);
        if (v4l2_result) {
            std::cout << "✓ MPP decoder V4L2 compatibility interface works" << std::endl;
            std::cout << "  Decoder type: " << v4l2_decoder.get_decoder_type_name() << std::endl;
        } else {
            std::cout << "✗ MPP decoder V4L2 compatibility interface failed" << std::endl;
            all_passed = false;
        }
    }

    // 测试不支持的格式（应该在构造时就失败）
    try {
        MPPDecoder unsupported_decoder(V4L2_PIX_FMT_YUYV);
        if (unsupported_decoder.get_state() == MPP_DECODER_STATE_ERROR) {
            std::cout << "✓ MPP decoder correctly rejected unsupported format (YUYV) at construction" << std::endl;
        } else {
            std::cout << "✗ MPP decoder should have rejected unsupported format at construction" << std::endl;
            all_passed = false;
        }
    } catch (...) {
        std::cout << "✓ MPP decoder correctly rejected unsupported format (YUYV) with exception" << std::endl;
    }

    std::cout << std::endl;
    return all_passed;
}

// 测试多格式解码 - 每个解码器实例专用于一种格式
bool test_multi_format_decode() {
    std::cout << "=== Testing Multi-Format Decode ===" << std::endl;

    bool all_passed = true;

    // 测试MJPEG解码
    {
        std::cout << "--- Testing MJPEG Decode ---" << std::endl;
        MPPDecoder mjpeg_decoder(MPP_DECODER_TYPE_MJPEG);

        if (!mjpeg_decoder.init(640, 480)) {
            std::cout << "✗ Failed to initialize MJPEG decoder" << std::endl;
            all_passed = false;
        } else {
            Frame src_frame, dst_frame;
            generate_simple_test_mjpeg_frame(src_frame, 640, 480);

            bool decode_result = mjpeg_decoder.decode_frame(src_frame, dst_frame);
            if (decode_result) {
                std::cout << "✓ MJPEG decode completed successfully" << std::endl;
                print_frame_info(dst_frame, "MJPEG Decoded");

                if (dst_frame.format == V4L2_PIX_FMT_NV12) {
                    std::cout << "✓ Output format is NV12 as expected" << std::endl;
                } else {
                    std::cout << "✗ Unexpected output format: 0x" << std::hex << dst_frame.format << std::dec << std::endl;
                }
            } else {
                std::cout << "✗ MJPEG decode failed (expected on systems without MPP hardware)" << std::endl;
                all_passed = false;
            }
        }
    }

    // 测试H264解码
    // {
    //     std::cout << "--- Testing H264 Decode ---" << std::endl;
    //     MPPDecoder h264_decoder(MPP_DECODER_TYPE_H264);

    //     if (!h264_decoder.init(1280, 720)) {
    //         std::cout << "✗ Failed to initialize H264 decoder" << std::endl;
    //         all_passed = false;
    //     } else {
    //         Frame src_frame, dst_frame;
    //         generate_test_h264_frame(src_frame, 1280, 720);

    //         bool decode_result = h264_decoder.decode_frame(src_frame, dst_frame);
    //         if (decode_result) {
    //             std::cout << "✓ H264 decode completed successfully" << std::endl;
    //             print_frame_info(dst_frame, "H264 Decoded");
    //         } else {
    //             std::cout << "✗ H264 decode failed (expected on systems without MPP hardware)" << std::endl;
    //             all_passed = false;
    //         }
    //     }
    // }

    // 测试H265解码
    // {
    //     std::cout << "--- Testing H265 Decode ---" << std::endl;
    //     MPPDecoder h265_decoder(MPP_DECODER_TYPE_H265);

    //     if (!h265_decoder.init(1920, 1080)) {
    //         std::cout << "✗ Failed to initialize H265 decoder" << std::endl;
    //         all_passed = false;
    //     } else {
    //         Frame src_frame, dst_frame;
    //         generate_test_h265_frame(src_frame, 1920, 1080);

    //         bool decode_result = h265_decoder.decode_frame(src_frame, dst_frame);
    //         if (decode_result) {
    //             std::cout << "✓ H265 decode completed successfully" << std::endl;
    //             print_frame_info(dst_frame, "H265 Decoded");
    //         } else {
    //             std::cout << "✗ H265 decode failed (expected on systems without MPP hardware)" << std::endl;
    //             all_passed = false;
    //         }
    //     }
    // }

    // 测试格式不匹配的情况
    {
        std::cout << "--- Testing Format Mismatch ---" << std::endl;
        MPPDecoder mjpeg_decoder(MPP_DECODER_TYPE_MJPEG);

        if (mjpeg_decoder.init(640, 480)) {
            Frame h264_frame, dst_frame;
            generate_test_h264_frame(h264_frame, 640, 480);

            // 尝试用MJPEG解码器解码H264数据（应该有警告但可能仍会尝试）
            bool decode_result = mjpeg_decoder.decode_frame(h264_frame, dst_frame);
            std::cout << "  Format mismatch test: " << (decode_result ? "decoded" : "failed")
                      << " (warning expected)" << std::endl;
        }
    }

    std::cout << std::endl;
    return all_passed;
}

// 测试多次初始化和清理 - 每个解码器实例固定格式
bool test_multiple_init_cleanup() {
    std::cout << "=== Testing Multiple Init/Cleanup Cycles ===" << std::endl;

    bool all_passed = true;

    // 测试MJPEG解码器的多次初始化
    {
        std::cout << "--- Testing MJPEG Decoder Cycles ---" << std::endl;
        MPPDecoder mjpeg_decoder(MPP_DECODER_TYPE_MJPEG);

        for (int i = 0; i < 3; i++) {
            std::cout << "MJPEG Cycle " << (i + 1) << ":" << std::endl;

            bool init_result = mjpeg_decoder.init(1920, 1080);
            if (init_result) {
                std::cout << "  ✓ Init successful" << std::endl;

                // 检查状态
                if (mjpeg_decoder.is_initialized()) {
                    std::cout << "  ✓ Decoder state is correct" << std::endl;
                    std::cout << "  ✓ Decoder type: " << mjpeg_decoder.get_decoder_type_name() << std::endl;
                } else {
                    std::cout << "  ✗ Decoder state is incorrect" << std::endl;
                    all_passed = false;
                }
            } else {
                std::cout << "  ✗ Init failed" << std::endl;
                all_passed = false;
            }

            mjpeg_decoder.cleanup();
            std::cout << "  ✓ Cleanup completed" << std::endl;

            // 检查清理后状态
            if (!mjpeg_decoder.is_initialized()) {
                std::cout << "  ✓ Decoder state cleared correctly" << std::endl;
            } else {
                std::cout << "  ✗ Decoder state not cleared" << std::endl;
                all_passed = false;
            }
        }
    }

    // 测试H264解码器的多次初始化
    {
        std::cout << "--- Testing H264 Decoder Cycles ---" << std::endl;
        MPPDecoder h264_decoder(MPP_DECODER_TYPE_H264);

        for (int i = 0; i < 2; i++) {
            std::cout << "H264 Cycle " << (i + 1) << ":" << std::endl;

            bool init_result = h264_decoder.init(1280, 720);
            if (init_result) {
                std::cout << "  ✓ Init successful" << std::endl;
                std::cout << "  ✓ Decoder type: " << h264_decoder.get_decoder_type_name() << std::endl;
            } else {
                std::cout << "  ✗ Init failed" << std::endl;
                all_passed = false;
            }

            h264_decoder.cleanup();
            std::cout << "  ✓ Cleanup completed" << std::endl;
        }
    }

    // 测试重复初始化（应该失败）
    {
        std::cout << "--- Testing Duplicate Initialization ---" << std::endl;
        MPPDecoder test_decoder(MPP_DECODER_TYPE_MJPEG);

        bool first_init = test_decoder.init(640, 480);
        bool second_init = test_decoder.init(1280, 720);  // 应该失败

        if (first_init && !second_init) {
            std::cout << "  ✓ Duplicate initialization correctly rejected" << std::endl;
        } else {
            std::cout << "  ✗ Duplicate initialization handling failed" << std::endl;
            all_passed = false;
        }
    }

    if (all_passed) {
        std::cout << "✓ All init/cleanup cycles completed successfully" << std::endl;
    } else {
        std::cout << "✗ Some init/cleanup cycles failed" << std::endl;
    }

    std::cout << std::endl;
    return all_passed;
}

// 测试不同分辨率 - 使用固定格式的解码器
bool test_different_resolutions() {
    std::cout << "=== Testing Different Resolutions ===" << std::endl;

    bool all_passed = true;

    std::vector<std::pair<int, int>> resolutions = {
        {320, 240},   // QVGA
        {640, 480},   // VGA
        {1280, 720},  // HD
        {1920, 1080}, // Full HD
        {3840, 2160}  // 4K
    };

    // 测试MJPEG解码器的不同分辨率
    std::cout << "--- Testing MJPEG Resolutions ---" << std::endl;
    for (const auto& res : resolutions) {
        std::cout << "Testing MJPEG resolution: " << res.first << "x" << res.second << std::endl;

        MPPDecoder mjpeg_decoder(MPP_DECODER_TYPE_MJPEG);
        bool result = mjpeg_decoder.init(res.first, res.second);
        if (result) {
            std::cout << "  ✓ Init successful for " << res.first << "x" << res.second << std::endl;
        } else {
            std::cout << "  ✗ Init failed for " << res.first << "x" << res.second << std::endl;
            all_passed = false;
        }
    }

    // 测试H264解码器的不同分辨率
    std::cout << "--- Testing H264 Resolutions ---" << std::endl;
    std::vector<std::pair<int, int>> h264_resolutions = {
        {1280, 720},  // HD
        {1920, 1080}, // Full HD
        {3840, 2160}  // 4K
    };

    for (const auto& res : h264_resolutions) {
        std::cout << "Testing H264 resolution: " << res.first << "x" << res.second << std::endl;

        MPPDecoder h264_decoder(MPP_DECODER_TYPE_H264);
        bool result = h264_decoder.init(res.first, res.second);
        if (result) {
            std::cout << "  ✓ Init successful for " << res.first << "x" << res.second << std::endl;
        } else {
            std::cout << "  ✗ Init failed for " << res.first << "x" << res.second << std::endl;
            all_passed = false;
        }
    }

    // 测试无效分辨率
    std::cout << "--- Testing Invalid Resolutions ---" << std::endl;
    {
        MPPDecoder test_decoder(MPP_DECODER_TYPE_MJPEG);

        bool invalid_result1 = test_decoder.init(0, 480);      // 无效宽度
        bool invalid_result2 = test_decoder.init(640, 0);      // 无效高度
        bool invalid_result3 = test_decoder.init(-1, -1);      // 负数分辨率

        if (!invalid_result1 && !invalid_result2 && !invalid_result3) {
            std::cout << "  ✓ Invalid resolutions correctly rejected" << std::endl;
        } else {
            std::cout << "  ✗ Invalid resolutions should be rejected" << std::endl;
            all_passed = false;
        }
    }

    std::cout << std::endl;
    return all_passed;
}

// 保存帧数据到文件
void save_frame_to_file(const std::vector<uint8_t>& data, const std::string& filename) {
    std::ofstream file(filename, std::ios::binary);
    if (file.is_open()) {
        file.write(reinterpret_cast<const char*>(data.data()), data.size());
        file.close();
        std::cout << "💾 Saved frame to: " << filename << " (" << data.size() << " bytes)" << std::endl;
    } else {
        std::cout << "❌ Failed to save frame to: " << filename << std::endl;
    }
}

// 支持的分辨率配置
struct ResolutionConfig {
    int width;
    int height;
    std::string name;
};

// 支持的分辨率列表
std::map<std::string, ResolutionConfig> supported_resolutions = {
    {"1920x1080", {1920, 1080, "1920x1080"}},
    {"1280x720",  {1280, 720,  "1280x720"}},
    {"640x480",   {640,  480,  "640x480"}}
};

// 显示使用帮助
void show_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [options]" << std::endl;
    std::cout << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  --mjpeg-file <file>     Path to MJPEG file for testing" << std::endl;
    std::cout << "  --resolution <res>      Resolution of the MJPEG file" << std::endl;
    std::cout << "  --help                  Show this help message" << std::endl;
    std::cout << std::endl;
    std::cout << "Supported resolutions:" << std::endl;
    for (const auto& res : supported_resolutions) {
        std::cout << "  " << res.first << std::endl;
    }
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << program_name << " --mjpeg-file test.jpg --resolution 1920x1080" << std::endl;
    std::cout << "  " << program_name << " --mjpeg-file sample.jpg --resolution 1280x720" << std::endl;
    std::cout << std::endl;
    std::cout << "If no MJPEG file is specified, all standard tests will run." << std::endl;
}

// 主测试函数
int main(int argc, char* argv[]) {
    std::cout << "MPP Decoder Test Suite" << std::endl;
    std::cout << "======================" << std::endl;
    std::cout << std::endl;

    std::string mjpeg_file;
    std::string resolution_str;
    bool run_standard_tests = true;

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--help" || arg == "-h") {
            show_usage(argv[0]);
            return 0;
        } else if (arg == "--mjpeg-file" && i + 1 < argc) {
            mjpeg_file = argv[++i];
            run_standard_tests = false;
        } else if (arg == "--resolution" && i + 1 < argc) {
            resolution_str = argv[++i];
        } else {
            std::cerr << "Unknown argument: " << arg << std::endl;
            show_usage(argv[0]);
            return 1;
        }
    }

    // 如果指定了MJPEG文件，运行文件测试
    if (!mjpeg_file.empty()) {
        if (resolution_str.empty()) {
            std::cerr << "Error: --resolution must be specified when using --mjpeg-file" << std::endl;
            show_usage(argv[0]);
            return 1;
        }

        auto res_it = supported_resolutions.find(resolution_str);
        if (res_it == supported_resolutions.end()) {
            std::cerr << "Error: Unsupported resolution: " << resolution_str << std::endl;
            show_usage(argv[0]);
            return 1;
        }

        const ResolutionConfig& res_config = res_it->second;

        std::cout << "=== Testing MJPEG File Decode ===" << std::endl;
        std::cout << "File: " << mjpeg_file << std::endl;
        std::cout << "Resolution: " << res_config.name << std::endl;
        std::cout << std::endl;

        // 测试MJPEG文件解码
        MPPDecoder mjpeg_decoder(MPP_DECODER_TYPE_MJPEG);

        if (!mjpeg_decoder.init(res_config.width, res_config.height)) {
            std::cout << "✗ Failed to initialize MJPEG decoder for " << res_config.name << std::endl;
            return 1;
        }

        Frame src_frame, dst_frame;
        if (!load_mjpeg_from_file(src_frame, mjpeg_file, res_config.width, res_config.height)) {
            std::cout << "✗ Failed to load MJPEG file: " << mjpeg_file << std::endl;
            return 1;
        }

        bool decode_result = mjpeg_decoder.decode_frame(src_frame, dst_frame);
        if (decode_result) {
            std::cout << "✓ MJPEG file decode completed successfully" << std::endl;
            print_frame_info(dst_frame, "MJPEG File Decoded");

            if (dst_frame.format == V4L2_PIX_FMT_NV12) {
                std::cout << "✓ Output format is NV12 as expected" << std::endl;
            } else {
                std::cout << "✗ Unexpected output format: 0x" << std::hex << dst_frame.format << std::dec << std::endl;
            }
            // save frame to file
            save_frame_to_file(dst_frame.data, "mjpeg_decoded.yuv");

            std::cout << "✓ MJPEG file test passed!" << std::endl;
            return 0;
        } else {
            std::cout << "✗ MJPEG file decode failed" << std::endl;
            return 1;
        }
    }

    // 运行标准测试
    if (run_standard_tests) {
        bool all_tests_passed = true;

        // 运行所有测试
        all_tests_passed &= test_mpp_decoder_init();
        all_tests_passed &= test_multi_format_decode();
        all_tests_passed &= test_multiple_init_cleanup();
        all_tests_passed &= test_different_resolutions();

        // 输出总结
        std::cout << "=== Test Summary ===" << std::endl;
        if (all_tests_passed) {
            std::cout << "✓ All tests passed!" << std::endl;
            return 0;
        } else {
            std::cout << "✗ Some tests failed. Note: Failures are expected on systems without MPP hardware." << std::endl;
            return 1;
        }
    }

    return 0;
}
