# RGA Stride 对齐实现说明

## 概述

RGA (Raster Graphic Acceleration) 硬件要求内存访问按照特定的对齐方式进行，以获得最佳性能。本文档说明了在RGA Accelerator中实现的16字节stride对齐机制。

## 为什么需要Stride对齐

### 1. 硬件要求
- RGA硬件对内存访问有对齐要求
- 16字节对齐可以提高DMA传输效率
- 避免硬件访问非对齐内存导致的性能下降

### 2. 性能优化
- 对齐的内存访问速度更快
- 减少内存带宽浪费
- 提高整体图像处理性能

### 3. 兼容性
- 确保在不同的Rockchip芯片上都能正常工作
- 避免因内存对齐问题导致的硬件错误

## 实现方式

### 1. 宽度对齐函数
```cpp
inline int RGAAccelerator::align_width_to_16(int width) {
    // 将宽度对齐到16字节边界
    return (width + 15) & ~15;
}
```

**说明**:
- `(width + 15) & ~15` 是标准的向上对齐算法
- 确保结果是16的倍数
- 例如：width=100 -> aligned_width=112

### 2. Stride计算函数
```cpp
inline int RGAAccelerator::calculate_stride(int width, int32_t format) {
    int bytes_per_pixel = 0;
    
    switch (format) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
            bytes_per_pixel = 2;  // 16 bits per pixel
            break;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
            bytes_per_pixel = 3;  // 24 bits per pixel
            break;
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            bytes_per_pixel = 1;  // 8 bits per pixel for Y plane
            break;
        case V4L2_PIX_FMT_RGBA32:
        case V4L2_PIX_FMT_BGRA32:
            bytes_per_pixel = 4;  // 32 bits per pixel
            break;
    }
    
    // 计算字节宽度并对齐到16字节
    int byte_width = width * bytes_per_pixel;
    int aligned_byte_width = (byte_width + 15) & ~15;
    
    // 返回对齐后的像素宽度
    return aligned_byte_width / bytes_per_pixel;
}
```

**说明**:
- 根据像素格式计算每像素字节数
- 计算总字节宽度
- 对齐到16字节边界
- 转换回像素宽度

### 3. RGA缓冲区设置
```cpp
inline bool RGAAccelerator::setup_rga_buffer(rga_buffer_t& buf, const Frame& frame) {
    memset(&buf, 0, sizeof(buf));
    
    buf.width = frame.width;
    buf.height = frame.height;
    buf.format = v4l2_to_rga_format(frame.format);
    buf.size = frame.data.size();
    buf.vir_addr = (void*)frame.data.data();
    
    // 设置stride，确保16字节对齐
    buf.wstride = calculate_stride(frame.width, frame.format);
    buf.hstride = frame.height;
    
    return true;
}
```

**说明**:
- `wstride`: 宽度stride，按16字节对齐
- `hstride`: 高度stride，通常等于实际高度
- 确保RGA硬件能够正确访问内存

## 对齐示例

### 示例1: RGB24格式
```
原始宽度: 640像素
每像素字节数: 3字节
字节宽度: 640 × 3 = 1920字节
对齐后字节宽度: (1920 + 15) & ~15 = 1920字节 (已对齐)
对齐后像素宽度: 1920 ÷ 3 = 640像素
```

### 示例2: RGB24格式 (非对齐)
```
原始宽度: 100像素
每像素字节数: 3字节
字节宽度: 100 × 3 = 300字节
对齐后字节宽度: (300 + 15) & ~15 = 304字节
对齐后像素宽度: 304 ÷ 3 = 101像素 (向上取整)
```

### 示例3: YUYV格式
```
原始宽度: 1280像素
每像素字节数: 2字节
字节宽度: 1280 × 2 = 2560字节
对齐后字节宽度: (2560 + 15) & ~15 = 2560字节 (已对齐)
对齐后像素宽度: 2560 ÷ 2 = 1280像素
```

## 内存分配调整

### 缓冲区分配
```cpp
inline bool RGAAccelerator::allocate_frame_buffer(Frame& frame, int width, int height, int32_t format) {
    frame.width = width;
    frame.height = height;
    frame.format = format;
    
    // 计算stride对齐后的宽度
    int aligned_width = align_width_to_16(width);
    
    // 使用对齐后的宽度计算缓冲区大小
    size_t buffer_size = 0;
    switch (format) {
        case V4L2_PIX_FMT_RGB24:
            buffer_size = aligned_width * height * 3;
            break;
        // ... 其他格式
    }
    
    frame.data.resize(buffer_size);
    return true;
}
```

**说明**:
- 使用对齐后的宽度分配内存
- 确保有足够的空间存储对齐后的数据
- 避免内存越界访问

## 性能影响

### 1. 内存使用
- **增加**: 对齐可能增加少量内存使用
- **示例**: 100像素RGB24 -> 101像素RGB24 (增加3字节/行)
- **影响**: 通常增加不到5%的内存使用

### 2. 处理性能
- **提升**: 硬件访问对齐内存更高效
- **DMA**: 16字节对齐的DMA传输性能最佳
- **缓存**: 对齐访问对CPU缓存更友好

### 3. 兼容性
- **硬件**: 确保在所有Rockchip芯片上正常工作
- **驱动**: 符合RGA驱动的要求
- **稳定性**: 避免因对齐问题导致的错误

## 调试信息

### 日志输出
```cpp
LOG_D("RGA operation: src %dx%d (stride %d) -> dst %dx%d (stride %d)", 
      src_buf_.width, src_buf_.height, src_buf_.wstride,
      dst_buf_.width, dst_buf_.height, dst_buf_.wstride);
```

**输出示例**:
```
RGA operation: src 1280x720 (stride 1280) -> dst 640x640 (stride 640)
RGA operation: src 100x100 (stride 101) -> dst 640x640 (stride 640)
```

### 对齐检查
```cpp
// 检查是否正确对齐
bool is_aligned = (buf.wstride * bytes_per_pixel) % 16 == 0;
if (!is_aligned) {
    LOG_W("Buffer stride not properly aligned: %d", buf.wstride);
}
```

## 最佳实践

### 1. 预分配对齐缓冲区
```cpp
// 为常用尺寸预分配对齐的缓冲区
Frame aligned_buffer;
allocate_frame_buffer(aligned_buffer, 640, 640, V4L2_PIX_FMT_RGB24);
```

### 2. 检查输入数据对齐
```cpp
// 检查输入数据是否已经对齐
int required_stride = calculate_stride(frame.width, frame.format);
if (frame.width != required_stride) {
    LOG_I("Input frame will be realigned: %d -> %d", frame.width, required_stride);
}
```

### 3. 性能监控
```cpp
// 监控对齐对性能的影响
auto start = std::chrono::high_resolution_clock::now();
rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24);
auto end = std::chrono::high_resolution_clock::now();
auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
LOG_D("RGA operation took %ld microseconds", duration.count());
```

## 总结

通过实现16字节stride对齐，我们确保了：

1. **硬件兼容性**: 符合RGA硬件要求
2. **性能优化**: 提高内存访问效率
3. **稳定性**: 避免对齐相关的错误
4. **可维护性**: 清晰的对齐逻辑和调试信息

这种实现方式在保证功能正确性的同时，最大化了RGA硬件的性能优势。
