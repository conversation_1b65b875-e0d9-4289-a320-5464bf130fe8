#!/bin/bash

# GStreamer RTSP测试构建脚本

set -e

echo "=== GStreamer RTSP Client Build Test ==="

# 检查依赖
echo "Checking GStreamer dependencies..."
pkg-config --exists gstreamer-1.0 || { echo "Error: GStreamer not found"; exit 1; }
pkg-config --exists gstreamer-app-1.0 || { echo "Error: GStreamer app not found"; exit 1; }
pkg-config --exists gstreamer-video-1.0 || { echo "Error: GStreamer video not found"; exit 1; }
pkg-config --exists gstreamer-rtsp-1.0 || { echo "Error: GStreamer RTSP not found"; exit 1; }

echo "All GStreamer dependencies found"

# 获取编译标志
GSTREAMER_CFLAGS=$(pkg-config --cflags gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-rtsp-1.0 gstreamer-plugins-base-1.0)
GSTREAMER_LIBS=$(pkg-config --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-rtsp-1.0 gstreamer-plugins-base-1.0)

echo "GStreamer CFLAGS: $GSTREAMER_CFLAGS"
echo "GStreamer LIBS: $GSTREAMER_LIBS"

# 创建构建目录
BUILD_DIR="build_test"
mkdir -p $BUILD_DIR

# 编译测试程序
echo "Compiling RTSP GStreamer test..."
g++ -std=c++17 -Wall -Wextra -O2 \
    -I./include \
    $GSTREAMER_CFLAGS \
    -o $BUILD_DIR/test_rtsp_gstreamer \
    test/test_rtsp_gstreamer.cpp \
    $GSTREAMER_LIBS \
    -lpthread

if [ $? -eq 0 ]; then
    echo "✓ Compilation successful!"
    echo "Test executable: $BUILD_DIR/test_rtsp_gstreamer"
    echo ""
    echo "Usage:"
    echo "  $BUILD_DIR/test_rtsp_gstreamer <rtsp_url> [tcp] [sw_decode]"
    echo ""
    echo "Examples:"
    echo "  $BUILD_DIR/test_rtsp_gstreamer rtsp://192.168.1.100:554/stream"
    echo "  $BUILD_DIR/test_rtsp_gstreamer rtsp://192.168.1.100:554/stream tcp"
    echo "  $BUILD_DIR/test_rtsp_gstreamer rtsp://192.168.1.100:554/stream tcp sw_decode"
else
    echo "✗ Compilation failed!"
    exit 1
fi

echo "=== Build Test Complete ==="
