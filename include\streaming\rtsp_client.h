#ifndef RTSP_CLIENT_H
#define RTSP_CLIENT_H

#include "common.h"
#include "dma_buffer_manager.h"
#include <string>
#include <memory>

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
}

// RTSP客户端封装 (支持DMA模式)
class RTSPClient {
private:
    AVFormatContext* format_ctx_;
    AVPacket* packet_;
    int video_stream_index_;
    std::string url_;
    bool use_tcp_;
    bool connected_;

    // 流信息
    AVCodecParameters* codec_params_;
    int width_;
    int height_;
    AVRational time_base_;

public:
    RTSPClient() : format_ctx_(nullptr), packet_(nullptr), video_stream_index_(-1),
                   use_tcp_(false), connected_(false), codec_params_(nullptr),
                   width_(0), height_(0) {
        time_base_ = {1, 90000}; // 默认RTP时间基
    }

    ~RTSPClient() {
        cleanup();
    }

    // 初始化RTSP客户端
    bool init(const std::string& url, bool use_tcp = false, int timeout_us = 1000000);

    // 传统模式：获取帧到Frame结构
    void get_frame(Frame& frame);

    // DMA模式：获取帧到DMA缓冲区
    bool get_frame_to_dma(buffer_manager::BufferManager::BufferSlot* dma_buffer);

    // 清理资源
    void cleanup();

    // 状态查询
    bool is_connected() const { return connected_; }
    int get_width() const { return width_; }
    int get_height() const { return height_; }
    AVCodecParameters* get_codec_params() const { return codec_params_; }

private:
    // 内部辅助函数
    bool open_input();
    bool find_video_stream();
    void setup_network_options();
};

// RTSPClient 实现
inline bool RTSPClient::init(const std::string& url, bool use_tcp, int timeout_us) {
    url_ = url;
    use_tcp_ = use_tcp;
    connected_ = false;

    // 分配AVPacket
    packet_ = av_packet_alloc();
    if (!packet_) {
        LOG_E("Failed to allocate AVPacket");
        return false;
    }

    // 设置网络选项
    setup_network_options();

    // 打开输入
    if (!open_input()) {
        cleanup();
        return false;
    }

    // 查找视频流
    if (!find_video_stream()) {
        cleanup();
        return false;
    }

    connected_ = true;
    LOG_I("RTSP client initialized successfully: %s", url_.c_str());
    return true;
}

inline void RTSPClient::get_frame(Frame& frame) {
    if (!connected_ || !format_ctx_) {
        frame.data.clear();
        return;
    }

    int ret = av_read_frame(format_ctx_, packet_);
    if (ret < 0) {
        if (ret == AVERROR_EOF) {
            LOG_W("RTSP stream ended");
        } else {
            LOG_E("Failed to read RTSP frame: %s", av_err2str(ret));
        }
        frame.data.clear();
        return;
    }

    if (packet_->stream_index != video_stream_index_) {
        av_packet_unref(packet_);
        frame.data.clear();
        return;
    }

    // 复制数据到Frame
    frame.data.resize(packet_->size);
    memcpy(frame.data.data(), packet_->data, packet_->size);

    // 设置时间戳
    if (packet_->pts != AV_NOPTS_VALUE) {
        frame.timestamp_us = av_rescale_q(packet_->pts, time_base_, {1, 1000000});
    } else {
        auto now = std::chrono::steady_clock::now();
        frame.timestamp_us = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch()).count();
    }

    // 设置格式信息
    frame.width = width_;
    frame.height = height_;
    frame.format = codec_params_->codec_id == AV_CODEC_ID_H264 ? V4L2_PIX_FMT_H264 :
                  (codec_params_->codec_id == AV_CODEC_ID_HEVC ? V4L2_PIX_FMT_HEVC : V4L2_PIX_FMT_MJPEG);

    // 检查是否为关键帧
    frame.is_keyframe = (packet_->flags & AV_PKT_FLAG_KEY) != 0;

    av_packet_unref(packet_);
}

inline bool RTSPClient::get_frame_to_dma(buffer_manager::BufferManager::BufferSlot* dma_buffer) {
    if (!connected_ || !format_ctx_ || !dma_buffer) {
        return false;
    }

    int ret = av_read_frame(format_ctx_, packet_);
    if (ret < 0) {
        if (ret == AVERROR_EOF) {
            LOG_W("RTSP stream ended");
        } else {
            LOG_E("Failed to read RTSP frame: %s", av_err2str(ret));
        }
        return false;
    }

    if (packet_->stream_index != video_stream_index_) {
        av_packet_unref(packet_);
        return false;
    }

    // 检查缓冲区大小
    if (packet_->size > dma_buffer->size) {
        LOG_E("Packet size (%d) exceeds DMA buffer size (%zu)", packet_->size, dma_buffer->size);
        av_packet_unref(packet_);
        return false;
    }

    // 复制数据到DMA缓冲区
    memcpy(dma_buffer->addr, packet_->data, packet_->size);
    dma_buffer->meta.data_size = packet_->size;

    // 设置时间戳
    if (packet_->pts != AV_NOPTS_VALUE) {
        dma_buffer->meta.timestamp_us = av_rescale_q(packet_->pts, time_base_, {1, 1000000});
    } else {
        auto now = std::chrono::steady_clock::now();
        dma_buffer->meta.timestamp_us = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch()).count();
    }

    // 设置格式信息
    dma_buffer->meta.width = width_;
    dma_buffer->meta.height = height_;
    dma_buffer->meta.format = codec_params_->codec_id == AV_CODEC_ID_H264 ? V4L2_PIX_FMT_H264 :
                             (codec_params_->codec_id == AV_CODEC_ID_HEVC ? V4L2_PIX_FMT_HEVC : V4L2_PIX_FMT_MJPEG);

    // 检查是否为关键帧
    dma_buffer->meta.is_keyframe = (packet_->flags & AV_PKT_FLAG_KEY) != 0;

    av_packet_unref(packet_);
    return true;
}

inline void RTSPClient::cleanup() {
    connected_ = false;

    if (packet_) {
        av_packet_free(&packet_);
        packet_ = nullptr;
    }

    if (format_ctx_) {
        avformat_close_input(&format_ctx_);
        format_ctx_ = nullptr;
    }

    codec_params_ = nullptr;
    video_stream_index_ = -1;
    width_ = 0;
    height_ = 0;
}

inline bool RTSPClient::open_input() {
    format_ctx_ = avformat_alloc_context();
    if (!format_ctx_) {
        LOG_E("Failed to allocate AVFormatContext");
        return false;
    }

    // 设置选项
    AVDictionary* options = nullptr;

    // 网络选项
    av_dict_set(&options, "rtsp_transport", use_tcp_ ? "tcp" : "udp", 0);
    av_dict_set(&options, "stimeout", "5000000", 0);  // 5秒超时

    // 低延时选项 - 根据原始参数设置
    av_dict_set(&options, "fflags", "nobuffer", 0);
    av_dict_set(&options, "flags", "low_delay", 0);
    av_dict_set(&options, "analyzeduration", "100000", 0);  // 100ms
    av_dict_set(&options, "probesize", "8192", 0);          // 8KB
    av_dict_set(&options, "max_delay", "0", 0);
    av_dict_set(&options, "reorder_queue_size", "0", 0);    // 禁用重排序
    av_dict_set(&options, "fifo_size", "1048576", 0);       // 1MB UDP FIFO缓冲区

    int ret = avformat_open_input(&format_ctx_, url_.c_str(), nullptr, &options);
    av_dict_free(&options);

    if (ret < 0) {
        LOG_E("Failed to open RTSP stream %s: %s", url_.c_str(), av_err2str(ret));
        return false;
    }

    // 查找流信息
    ret = avformat_find_stream_info(format_ctx_, nullptr);
    if (ret < 0) {
        LOG_E("Failed to find stream info: %s", av_err2str(ret));
        return false;
    }

    return true;
}

inline bool RTSPClient::find_video_stream() {
    video_stream_index_ = -1;

    for (unsigned int i = 0; i < format_ctx_->nb_streams; i++) {
        if (format_ctx_->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            video_stream_index_ = i;
            codec_params_ = format_ctx_->streams[i]->codecpar;
            time_base_ = format_ctx_->streams[i]->time_base;
            width_ = codec_params_->width;
            height_ = codec_params_->height;
            break;
        }
    }

    if (video_stream_index_ == -1) {
        LOG_E("No video stream found in RTSP stream");
        return false;
    }

    LOG_I("Found video stream: %dx%d, codec=%s", width_, height_,
          avcodec_get_name(codec_params_->codec_id));

    return true;
}

inline void RTSPClient::setup_network_options() {
    // FFmpeg网络初始化
    avformat_network_init();
}

// RTSP客户端工厂类
class RTSPClientFactory {
public:
    static std::unique_ptr<RTSPClient> create_client() {
        return std::make_unique<RTSPClient>();
    }

    static std::unique_ptr<RTSPClient> create_client(const std::string& url, bool use_tcp = false) {
        auto client = std::make_unique<RTSPClient>();
        if (client->init(url, use_tcp)) {
            return client;
        }
        return nullptr;
    }
};

#endif // RTSP_CLIENT_H