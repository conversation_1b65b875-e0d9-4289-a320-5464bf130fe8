#!/bin/bash

# Build script for memory protection tests

echo "Building memory protection tests..."

# Create build directory if it doesn't exist
mkdir -p build

# Compile the memory protection test
g++ -std=c++17 -O2 -Wall -Wextra \
    -I. \
    examples/memory_protection_test.cpp \
    -o build/memory_protection_test \
    -pthread

if [ $? -eq 0 ]; then
    echo "Memory protection test build successful!"
else
    echo "Memory protection test build failed!"
    exit 1
fi

# Compile the cross-process protection test
g++ -std=c++17 -O2 -Wall -Wextra \
    -I. \
    examples/cross_process_protection_test.cpp \
    -o build/cross_process_protection_test \
    -pthread

if [ $? -eq 0 ]; then
    echo "Cross-process protection test build successful!"
else
    echo "Cross-process protection test build failed!"
    exit 1
fi

echo "All builds successful!"
echo "To run tests:"
echo "  ./build/memory_protection_test"
echo "  ./build/cross_process_protection_test (requires Unix environment)"