# GStreamer Encoder 支持的输入格式

## 概述

新的 `GStreamerEncoder` 支持多种输入格式，通过构造函数参数指定输入格式，自动生成相应的pipeline和caps。

## 支持的输入格式

### RGB 格式
| V4L2 格式 | GStreamer 格式 | 描述 |
|-----------|----------------|------|
| `V4L2_PIX_FMT_RGB24` | `RGB` | 24位RGB |
| `V4L2_PIX_FMT_BGR24` | `BGR` | 24位BGR |
| `V4L2_PIX_FMT_RGB32` | `RGBx` | 32位RGB (带填充) |
| `V4L2_PIX_FMT_BGR32` | `BGRx` | 32位BGR (带填充) |
| `V4L2_PIX_FMT_ARGB32` | `ARGB` | 32位ARGB |
| `V4L2_PIX_FMT_ABGR32` | `ABGR` | 32位ABGR |

### YUV 格式
| V4L2 格式 | GStreamer 格式 | 描述 |
|-----------|----------------|------|
| `V4L2_PIX_FMT_YUYV` | `YUY2` | 打包YUV 4:2:2 |
| `V4L2_PIX_FMT_UYVY` | `UYVY` | 打包YUV 4:2:2 |
| `V4L2_PIX_FMT_YUV420` | `I420` | 平面YUV 4:2:0 |
| `V4L2_PIX_FMT_YVU420` | `YV12` | 平面YVU 4:2:0 |
| `V4L2_PIX_FMT_NV12` | `NV12` | 半平面YUV 4:2:0 |
| `V4L2_PIX_FMT_NV21` | `NV21` | 半平面YVU 4:2:0 |
| `V4L2_PIX_FMT_YUV422P` | `Y42B` | 平面YUV 4:2:2 |
| `V4L2_PIX_FMT_YUV411P` | `Y41B` | 平面YUV 4:1:1 |
| `V4L2_PIX_FMT_YUV444` | `Y444` | 平面YUV 4:4:4 |

### 其他格式
| V4L2 格式 | GStreamer 格式 | 描述 |
|-----------|----------------|------|
| `V4L2_PIX_FMT_GREY` | `GRAY8` | 8位灰度 |
| `V4L2_PIX_FMT_MJPEG` | `MJPG` | Motion JPEG (特殊处理) |

## 使用方式

### 基本语法
```cpp
GStreamerEncoder encoder(EncoderType type, int param, 
                        int width, int height, int framerate,
                        uint32_t input_format);
```

### 参数说明
- **type**: 编码器类型 (`H264`, `H265`, `JPEG`)
- **param**: 编码参数 (H264/H265为比特率，JPEG为质量)
- **width/height**: 视频尺寸
- **framerate**: 帧率
- **input_format**: V4L2输入格式

## 使用示例

### RGB24 输入 → H264 编码
```cpp
GStreamerEncoder encoder(EncoderType::H264, 2000000, 
                        1920, 1080, 30, V4L2_PIX_FMT_RGB24);
```

### YUV420 输入 → H265 编码
```cpp
GStreamerEncoder encoder(EncoderType::H265, 4000000, 
                        1280, 720, 30, V4L2_PIX_FMT_YUV420);
```

### NV12 输入 → JPEG 编码
```cpp
GStreamerEncoder encoder(EncoderType::JPEG, 85, 
                        640, 480, 30, V4L2_PIX_FMT_NV12);
```

### MJPEG 输入 → H264 编码
```cpp
GStreamerEncoder encoder(EncoderType::H264, 3000000, 
                        1280, 720, 30, V4L2_PIX_FMT_MJPEG);
```

## Pipeline 结构

### 标准格式 Pipeline
```
appsrc caps="video/x-raw,format=<FORMAT>,width=W,height=H,framerate=F/1"
! queue max-size-buffers=2
! videoconvert
! video/x-raw,format=<TARGET_FORMAT>,width=W,height=H,framerate=F/1
! <encoder>
! <output_caps>
! appsink
```

### MJPEG 特殊 Pipeline
```
appsrc caps="image/jpeg,width=W,height=H,framerate=F/1"
! queue max-size-buffers=2
! mppjpegdec
! videoconvert
! video/x-raw,format=<TARGET_FORMAT>,width=W,height=H,framerate=F/1
! <encoder>
! <output_caps>
! appsink
```

## 格式转换策略

### H264/H265 编码器
- **目标格式**: NV12 (硬件编码器优化)
- **转换**: 所有输入格式 → NV12

### JPEG 编码器
- **目标格式**: I420 (JPEG编码器兼容)
- **转换**: 所有输入格式 → I420

### 自动转换
- 使用 `videoconvert` 元素自动处理格式转换
- 支持硬件加速的格式转换
- 保持原始尺寸和帧率

## 性能考虑

### 最优输入格式
- **H264/H265**: 推荐使用 `V4L2_PIX_FMT_NV12` (避免格式转换)
- **JPEG**: 推荐使用 `V4L2_PIX_FMT_YUV420` 或 `V4L2_PIX_FMT_NV12`

### 格式转换开销
- **无转换**: NV12 → H264/H265
- **轻量转换**: YUV格式之间的转换
- **重量转换**: RGB ↔ YUV 转换

### MJPEG 特殊处理
- MJPEG输入需要先解码再编码
- 增加了解码开销，但支持MJPEG摄像头输入
- 使用硬件MJPEG解码器 (`mppjpegdec`)

## 错误处理

### 不支持的格式
- 返回空的pipeline描述
- 记录错误日志
- 默认回退到RGB格式

### 格式验证
```cpp
std::string format_str = encoder.v4l2_format_to_gst_string(input_format);
if (format_str.empty()) {
    // 处理不支持的格式
}
```

## 扩展新格式

### 添加新的V4L2格式支持
1. 在 `v4l2_format_to_gst_string()` 中添加映射
2. 确保GStreamer支持对应格式
3. 测试格式转换兼容性

### 示例：添加新格式
```cpp
case V4L2_PIX_FMT_NEW_FORMAT:
    return "GST_NEW_FORMAT";
```

这种设计提供了灵活的输入格式支持，同时保持了高性能和易用性。
