# Video Converter Test Suite

## 概述

这是一个全面的Video Converter测试套件，包含了对所有核心组件的详细测试，包括功能测试、性能测试、压力测试和边界条件测试。

## 测试结构

### 🔧 核心组件测试

#### 1. RGA Accelerator 测试
- **基本格式转换**: YUYV -> RGB24 转换测试
- **多种格式支持**: YUYV, UYVY, RGB24, NV12 格式转换
- **不同尺寸缩放**: 多种分辨率缩放测试
- **裁剪和缩放**: 复合操作测试
- **边界条件**: 最小尺寸、奇数尺寸、大尺寸测试
- **性能基准**: 50次迭代的性能测试

#### 2. MPP Decoder 测试
- **基本初始化**: MPP解码器初始化测试
- **不同分辨率**: 多种分辨率初始化测试
- **MJPEG解码**: 模拟数据解码测试
- **错误处理**: 异常情况处理测试
- **多次初始化**: 内存泄漏检测

#### 3. GStreamer Codec 测试
- **基本初始化**: GStreamer初始化测试
- **H264编码**: 基本H264编码功能
- **不同分辨率**: 多种分辨率编码测试
- **不同比特率**: 多种比特率编码测试
- **多帧编码**: 连续帧编码测试
- **错误处理**: 异常情况处理测试
- **性能测试**: 编码性能基准测试

### 🎯 Video Converter 测试

#### 1. 基本功能测试
- **初始化和配置**: 基本初始化流程测试
- **不同配置组合**: AI/云端流媒体开关组合测试
- **启动和停止**: 服务启停操作测试
- **统计信息**: 统计数据收集测试
- **信号处理**: 信号处理机制测试

#### 2. 处理开关测试
- **AI处理开关**: 独立AI处理测试
- **云端流媒体开关**: 独立云端处理测试
- **全部禁用**: 所有处理禁用测试
- **配置验证**: 开关配置正确性验证

### ⚙️ 配置测试

#### 1. 配置文件加载
- **默认配置**: 标准配置文件加载
- **不存在文件**: 错误处理测试
- **自定义配置**: 临时配置文件测试

### 🚀 压力和性能测试

#### 1. 内存泄漏测试
- **多次初始化**: 10次初始化/清理循环
- **配置变化**: 不同配置组合测试

#### 2. 并发测试
- **多实例**: 3个并发VideoConverter实例
- **线程安全**: 多线程环境测试

#### 3. 长时间运行测试
- **稳定性**: 5秒连续运行测试
- **统计监控**: 运行时统计数据验证

## 运行测试

### 基本用法

```bash
# 运行基本测试套件
./test_video_converter

# 运行包含压力测试的完整套件
./test_video_converter --stress

# 启用详细日志
./test_video_converter --verbose

# 显示帮助信息
./test_video_converter --help
```

### 命令行选项

| 选项 | 描述 |
|------|------|
| `--stress` | 运行压力和性能测试 |
| `--verbose`, `-v` | 启用详细日志输出 |
| `--help`, `-h` | 显示帮助信息 |

### 测试输出示例

```
=== Comprehensive Video Converter Test Suite ===
Version: 2.0 - Enhanced with comprehensive testing

Starting test execution...

🔧 CORE COMPONENT TESTS

=== Comprehensive RGA Accelerator Tests ===

Test 1: Basic format conversion (YUYV -> RGB24)
✓ RGA Basic Format Conversion PASSED

Test 2: Multiple format conversions
    ✓ YUYV -> RGB24 conversion successful
    ✓ UYVY -> RGB24 conversion successful
    ✓ RGB24 -> RGB24 conversion successful
    ✓ NV12 -> RGB24 conversion successful
✓ RGA Multiple Format Conversions PASSED

...

=== Test Summary ===
Total tests: 25
Passed: 23
Failed: 2
Success rate: 92.0%

🎉 ALL TESTS PASSED! 🎉
```

## 测试结果解释

### 成功指标
- ✓ **PASSED**: 测试通过
- 所有核心功能正常工作
- 性能指标达到预期

### 失败处理
- ✗ **FAILED**: 测试失败
- 详细错误信息会显示在输出中
- 检查依赖库是否正确安装

### 性能基准

#### RGA Accelerator
- **目标性能**: > 30 FPS (1280x720 -> 640x640)
- **内存使用**: 合理的内存分配
- **错误率**: < 1%

#### GStreamer Codec
- **编码性能**: > 10 FPS (1280x720 H264编码)
- **输出质量**: 有效的H264数据
- **稳定性**: 连续编码无崩溃

#### VideoConverter
- **启动时间**: < 1秒
- **内存泄漏**: 无内存泄漏
- **并发支持**: 支持多实例运行

## 故障排除

### 常见问题

1. **RGA初始化失败**
   ```
   ✗ RGA Initialization FAILED
   ```
   - 检查RGA驱动是否安装
   - 确认设备权限: `sudo chmod 666 /dev/rga`
   - 验证librga库是否正确安装

2. **MPP初始化失败**
   ```
   ✗ MPP Initialization FAILED
   ```
   - 检查MPP库是否安装
   - 确认硬件支持MPP解码
   - 验证设备节点权限

3. **GStreamer初始化失败**
   ```
   ✗ GStreamer Initialization FAILED
   ```
   - 检查GStreamer开发包是否安装
   - 确认插件是否完整: `gst-inspect-1.0`
   - 验证编码器插件可用性

4. **DDS相关错误**
   ```
   Failed to initialize VideoConverter
   ```
   - 检查FastDDS是否正确安装
   - 确认网络配置正确
   - 验证topic名称唯一性

### 调试技巧

1. **启用详细日志**
   ```bash
   ./test_video_converter --verbose
   ```

2. **单独测试组件**
   - 修改main函数只运行特定测试
   - 使用调试器逐步执行

3. **检查系统资源**
   ```bash
   # 检查内存使用
   free -h
   
   # 检查CPU使用
   top
   
   # 检查设备节点
   ls -la /dev/rga /dev/mpp*
   ```

## 扩展测试

### 添加新测试

1. **创建测试函数**
   ```cpp
   void test_new_feature() {
       std::cout << "\n=== New Feature Tests ===" << std::endl;
       
       // 测试逻辑
       bool success = test_implementation();
       
       g_test_results.add_result("New Feature Test", success);
   }
   ```

2. **在main函数中调用**
   ```cpp
   test_new_feature();
   ```

### 自定义测试配置

可以通过修改测试参数来适应不同的测试需求：

```cpp
// 修改性能测试迭代次数
const int iterations = 100;  // 默认50

// 修改压力测试持续时间
const int duration_seconds = 10;  // 默认5

// 修改并发实例数量
const int instance_count = 5;  // 默认3
```

## 持续集成

这个测试套件可以集成到CI/CD流水线中：

```yaml
# .github/workflows/test.yml
- name: Run Video Converter Tests
  run: |
    cd build
    ./test_video_converter --stress
```

测试结果的退出码：
- `0`: 所有测试通过
- `1`: 有测试失败

## 贡献指南

添加新测试时请遵循以下原则：

1. **测试命名**: 使用描述性的测试名称
2. **错误处理**: 确保测试不会因异常而崩溃
3. **资源清理**: 及时清理测试资源
4. **结果记录**: 使用`g_test_results.add_result()`记录结果
5. **文档更新**: 更新此README文档
