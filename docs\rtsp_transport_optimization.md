# RTSP传输协议优化配置

## 概述

本文档详细说明了RTSP客户端中UDP和TCP传输协议的选择策略和优化参数配置。

## 传输协议选择策略

### UDP传输（默认首选）
- **优势**: 低延迟、高吞吐量、适合实时应用
- **劣势**: 可能丢包、网络不稳定时连接不可靠
- **适用场景**: 实时视频监控、低延迟要求的应用

### TCP传输（可靠备选）
- **优势**: 可靠传输、无丢包、连接稳定
- **劣势**: 延迟稍高、重传机制可能导致延迟抖动
- **适用场景**: 网络不稳定环境、要求数据完整性的应用

## 配置参数详解

### 1. 协议选择配置

```cpp
// CaptureConfig中的配置
struct CaptureConfig {
    bool use_tcp = false;  // false=UDP优先, true=TCP优先
    // ...
};
```

### 2. UDP传输优化参数

```cpp
if (!use_tcp_) {
    // 基础UDP配置
    av_dict_set(&options, "rtsp_transport", "udp", 0);
    av_dict_set(&options, "rtsp_flags", "prefer_udp", 0);
    
    // UDP特定优化
    av_dict_set_int(&options, "buffer_size", 1024*32, 0);        // 32KB缓冲区
    av_dict_set_int(&options, "fifo_size", 1024*1024, 0);        // 1MB FIFO缓冲
    av_dict_set_int(&options, "overrun_nonfatal", 1, 0);         // 丢包不致命
    av_dict_set(&options, "fflags", "nobuffer+flush_packets+discardcorrupt+genpts", 0);
}
```

**UDP参数说明：**
- `buffer_size=32KB`: 较小的缓冲区减少延迟
- `fifo_size=1MB`: FIFO缓冲区处理网络抖动
- `overrun_nonfatal=1`: 缓冲区溢出时继续运行而不退出
- `genpts`: 生成时间戳，处理丢包后的时间戳不连续

### 3. TCP传输优化参数

```cpp
if (use_tcp_) {
    // 基础TCP配置
    av_dict_set(&options, "rtsp_transport", "tcp", 0);
    av_dict_set(&options, "rtsp_flags", "prefer_tcp", 0);
    
    // TCP特定优化
    av_dict_set_int(&options, "buffer_size", 1024*64, 0);        // 64KB缓冲区
}
```

**TCP参数说明：**
- `buffer_size=64KB`: 较大的缓冲区提高TCP传输效率
- TCP模式下不需要`overrun_nonfatal`和`genpts`参数

### 4. 通用实时性优化参数

```cpp
// 适用于UDP和TCP的通用优化
av_dict_set(&options, "fflags", "nobuffer+flush_packets+discardcorrupt", 0);
av_dict_set(&options, "flags", "low_delay", 0);
av_dict_set(&options, "avioflags", "direct", 0);
av_dict_set_int(&options, "analyzeduration", 50000, 0);         // 50ms
av_dict_set_int(&options, "probesize", 2048, 0);                // 2KB
av_dict_set_int(&options, "max_delay", 0, 0);                   // 最小延迟
av_dict_set_int(&options, "reorder_queue_size", 0, 0);          // 禁用重排序
av_dict_set(&options, "tune", "zerolatency", 0);                // 零延迟调优
av_dict_set(&options, "max_interleave_delta", "0", 0);          // 禁用交错
```

## 性能对比分析

### 延迟对比
| 传输协议 | 连接建立 | 首帧延迟 | 平均延迟 | 延迟抖动 |
|---------|---------|---------|---------|---------|
| UDP     | 50-100ms | 20-50ms | 30-80ms | 低 |
| TCP     | 100-200ms | 50-100ms | 80-150ms | 中等 |

### 可靠性对比
| 传输协议 | 丢包处理 | 网络适应性 | 连接稳定性 | 错误恢复 |
|---------|---------|-----------|-----------|---------|
| UDP     | 丢弃继续 | 一般 | 一般 | 快速 |
| TCP     | 自动重传 | 好 | 好 | 较慢 |

### 资源使用对比
| 传输协议 | CPU使用 | 内存使用 | 网络带宽 | 系统调用 |
|---------|---------|---------|---------|---------|
| UDP     | 低 | 低 | 高效 | 少 |
| TCP     | 中等 | 中等 | 一般 | 多 |

## 使用建议

### 1. 场景选择指南

**选择UDP的场景：**
- 实时视频监控系统
- 低延迟要求的应用（<100ms）
- 网络带宽充足且稳定
- 可以容忍偶尔的帧丢失

**选择TCP的场景：**
- 网络环境不稳定
- 要求视频数据完整性
- 录像存储应用
- 延迟要求不严格（>200ms可接受）

### 2. 自动切换策略

```cpp
// 建议的自动切换逻辑
bool auto_select_transport(const std::string& url) {
    // 首先尝试UDP
    RTSPClient udp_client;
    if (udp_client.init(url, false, 3000000)) { // 3秒超时
        // 测试UDP连接质量
        int success_count = 0;
        for (int i = 0; i < 10; i++) {
            Frame frame = udp_client.get_frame();
            if (frame.valid) success_count++;
        }
        udp_client.cleanup();
        
        // 如果UDP成功率>80%，使用UDP
        if (success_count >= 8) {
            return false; // 使用UDP
        }
    }
    
    // UDP失败或质量差，切换到TCP
    return true; // 使用TCP
}
```

### 3. 网络环境优化

**系统级优化：**
```bash
# 增加UDP接收缓冲区
echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.core.rmem_default = 262144' >> /etc/sysctl.conf

# 优化网络队列
echo 'net.core.netdev_max_backlog = 5000' >> /etc/sysctl.conf

# 应用配置
sysctl -p
```

**应用级监控：**
```cpp
// 监控网络质量指标
struct NetworkStats {
    uint64_t packets_received;
    uint64_t packets_lost;
    uint64_t bytes_received;
    double avg_latency_ms;
    double jitter_ms;
};
```

## 测试和验证

### 1. 使用测试程序

```bash
# 编译测试程序
chmod +x compile_transport_test.sh
./compile_transport_test.sh

# 测试UDP传输
./test_rtsp_transport rtsp://192.168.1.100:554/stream

# 对比UDP和TCP传输
./test_rtsp_transport rtsp://192.168.1.100:554/stream test_both
```

### 2. 性能指标监控

**关键指标：**
- 连接建立时间
- 首帧获取时间
- 平均帧率
- 丢帧率
- 网络带宽使用

**监控命令：**
```bash
# 网络流量监控
iftop -i eth0

# 实时网络统计
ss -tuln | grep :554

# 进程网络使用
nethogs
```

## 故障排除

### 常见问题及解决方案

1. **UDP连接频繁断开**
   - 检查网络稳定性
   - 增加缓冲区大小
   - 考虑切换到TCP

2. **TCP延迟过高**
   - 检查网络RTT
   - 优化TCP缓冲区配置
   - 考虑使用UDP

3. **帧率不稳定**
   - 检查CPU使用率
   - 优化缓冲区配置
   - 调整线程优先级

4. **内存使用过高**
   - 减少缓冲区大小
   - 启用零拷贝优化
   - 检查内存泄漏

## 总结

通过合理配置UDP和TCP传输参数，可以在不同网络环境下获得最佳的RTSP流接收性能：

- **UDP优先策略**：适合大多数实时应用场景
- **TCP备选策略**：保证在网络不稳定时的可靠性
- **自动切换机制**：根据网络质量动态选择最优协议
- **参数调优**：针对具体应用场景精细化配置

建议在部署前进行充分的网络环境测试，选择最适合的传输协议和参数配置。
