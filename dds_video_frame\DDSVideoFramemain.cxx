// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file DDSVideoFramemain.cxx
 * This file acts as a main entry point to the application.
 *
 * This file was generated by the tool fastddsgen.
 */

#include <csignal>
#include <cstring>
#include <functional>
#include <iostream>
#include <stdexcept>
#include <thread>

#include <fastdds/dds/log/Log.hpp>

#include "DDSVideoFrameApplication.hpp"

using eprosima::fastdds::dds::Log;

std::function<void(int)> stop_handler;
void signal_handler(
        int signum)
{
    stop_handler(signum);
}

std::string parse_signal(
        const int& signum)
{
    switch (signum)
    {
        case SIGINT:
            return "SIGINT";
        case SIGTERM:
            return "SIGTERM";
#ifndef _WIN32
        case SIGQUIT:
            return "SIGQUIT";
        case SIGHUP:
            return "SIGHUP";
#endif // _WIN32
        default:
            return "UNKNOWN SIGNAL";
    }
}

int main(
        int argc,
        char** argv)
{
    auto ret = EXIT_SUCCESS;
    int domain_id = 0;
    std::shared_ptr<DDSVideoFrameApplication> app;

    if (argc != 2 || (strcmp(argv[1], "publisher") != 0 && strcmp(argv[1], "subscriber") != 0))
    {
        std::cout << "Error: Incorrect arguments." << std::endl;
        std::cout << "Usage: " << std::endl << std::endl;
        std::cout << argv[0] << " publisher|subscriber" << std::endl << std::endl;
        ret = EXIT_FAILURE;
    }
    else
    {
        try
        {
            app = DDSVideoFrameApplication::make_app(domain_id, argv[1]);
        }
        catch (const std::runtime_error& e)
        {
            EPROSIMA_LOG_ERROR(app_name, e.what());
            ret = EXIT_FAILURE;
        }

        std::thread thread(&DDSVideoFrameApplication::run, app);

        std::cout << argv[1] << " running. Please press Ctrl+C to stop the " << argv[1] << " at any time." << std::endl;

        stop_handler = [&](int signum)
                {
                    std::cout << "\n" << parse_signal(signum) << " received, stopping " << argv[1]
                              << " execution." << std::endl;
                    app->stop();
                };

        signal(SIGINT, signal_handler);
        signal(SIGTERM, signal_handler);
#ifndef _WIN32
        signal(SIGQUIT, signal_handler);
        signal(SIGHUP, signal_handler);
#endif // _WIN32

        thread.join();
    }

    Log::Reset();
    return ret;
}
