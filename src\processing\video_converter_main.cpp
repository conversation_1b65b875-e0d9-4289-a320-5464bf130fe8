#include "video_converter.h"
#include "config_loader.h"
#include "common.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>

// 全局变量
std::unique_ptr<VideoConverter> g_converter;

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_I("Received termination signal, stopping...");
            if (g_converter) {
                g_converter->stop();
            }
            break;
        case SIGUSR1:
            if (g_converter) {
                g_converter->handle_signal(signal);
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -c, --config FILE     Configuration file (default: config/video_converter.json)\n"
              << "  --input-topic TOPIC   Input DDS topic (default: Video_Frames)\n"
              << "  --ai-topic TOPIC      AI output DDS topic (default: AI_Frames)\n"
              << "  --cloud-topic TOPIC   Cloud output DDS topic (default: Cloud_Frames)\n"
              << "  --enable-ai           Enable AI processing (default: true)\n"
              << "  --disable-ai          Disable AI processing\n"
              << "  --enable-cloud-streaming   Enable cloud streaming (default: true)\n"
              << "  --disable-cloud-streaming  Disable cloud streaming\n"
              << "  -v, --verbose         Enable verbose logging\n"
              << "  --help                Show this help message\n";
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    Logger::set_level(LEVEL_INFO);

    // 默认配置
    VideoConverterConfig config;

    // DDS topic 配置
    std::string input_topic = "Video_Frames";
    std::string ai_topic = "AI_Frames";
    std::string cloud_topic = "Cloud_Frames";

    // 处理开关配置
    bool enable_ai = true;
    bool enable_cloud_streaming = true;

    // 配置文件路径
    std::string config_file = ConfigLoader::get_default_config_path("video_converter");

    // 第一次解析命令行参数，只获取配置文件路径
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"input-topic", required_argument, 0, 'i'},
        {"ai-topic", required_argument, 0, 'a'},
        {"cloud-topic", required_argument, 0, 'C'},
        {"enable-ai", no_argument, 0, 2},
        {"disable-ai", no_argument, 0, 3},
        {"enable-cloud-streaming", no_argument, 0, 4},
        {"disable-cloud-streaming", no_argument, 0, 5},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 1},
        {0, 0, 0, 0}
    };
    
    // 第一次解析：只获取配置文件路径
    int c;
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:i:a:C:v", long_options, nullptr)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }

    // 加载配置文件（在解析其他命令行参数之前）
    if (!ConfigLoader::load_video_converter_config(config_file, config, input_topic, ai_topic, cloud_topic)) {
        LOG_W("Failed to load config file: %s, using default settings", config_file.c_str());
    }

    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:i:a:C:v", long_options, nullptr)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件路径已经处理过了
                break;
            case 'i':
                input_topic = optarg;
                break;
            case 'a':
                ai_topic = optarg;
                break;
            case 'C':
                cloud_topic = optarg;
                break;
            case 2:  // --enable-ai
                enable_ai = true;
                break;
            case 3:  // --disable-ai
                enable_ai = false;
                break;
            case 4:  // --enable-cloud-streaming
                enable_cloud_streaming = true;
                break;
            case 5:  // --disable-cloud-streaming
                enable_cloud_streaming = false;
                break;
            case 'v':
                Logger::set_level(LEVEL_DEBUG);
                break;
            case 1:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }



    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    
    LOG_I("Starting video converter service...");
    
    try {
        // 创建并初始化服务
        g_converter = std::make_unique<VideoConverter>();

        // 应用命令行参数覆盖配置文件设置
        config.enable_ai = enable_ai;
        config.enable_cloud_streaming = enable_cloud_streaming;

        // 显示当前配置状态
        LOG_I("Video Converter Configuration:");
        LOG_I("  AI Processing: %s", config.enable_ai ? "ENABLED" : "DISABLED");
        LOG_I("  Cloud Streaming: %s", config.enable_cloud_streaming ? "ENABLED" : "DISABLED");
        LOG_I("  Hardware Acceleration: %s", config.enable_hardware_acceleration ? "ENABLED" : "DISABLED");
        LOG_I("  Input Topic: %s", input_topic.c_str());
        if (config.enable_ai) {
            LOG_I("  AI Output Topic: %s", ai_topic.c_str());
        }
        if (config.enable_cloud_streaming) {
            LOG_I("  Cloud Output Topic: %s", cloud_topic.c_str());
        }

        // 设置配置
        g_converter->set_config(config);

        if (!g_converter->init(input_topic, ai_topic, cloud_topic)) {
            LOG_E("Failed to initialize video converter");
            return 1;
        }
        
        // 启动服务
        g_converter->start();
        
        // 主循环 - 定期输出统计信息
        while (true) {
            VideoConverter::Stats stats;
            std::this_thread::sleep_for(std::chrono::seconds(15));
            
            g_converter->get_stats(stats);
            LOG_I("Stats - Processed: %lu, Dropped: %lu, CPU: %.1f%%",
                  stats.frames_processed, stats.frames_dropped, stats.cpu_usage);
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        return 1;
    }
    
    LOG_I("Video converter service stopped");
    return 0;
}
