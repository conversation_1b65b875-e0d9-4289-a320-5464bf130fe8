#!/bin/bash

# 配置验证脚本
# 验证视频服务系统配置文件的正确性

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/config"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
配置验证脚本 - 验证视频服务系统配置文件

用法: $0 [选项] [配置文件]

选项:
  -h, --help          显示此帮助信息
  -v, --verbose       详细输出
  -a, --all           验证所有配置文件
  -t, --topics        验证DDS主题命名规范
  -s, --syntax        仅验证JSON语法
  
参数:
  配置文件            要验证的配置文件路径（默认：config/config.json）

示例:
  $0                              # 验证默认配置文件
  $0 config/rtsp_server.json      # 验证RTSP服务器配置
  $0 --all                        # 验证所有配置文件
  $0 --topics                     # 检查DDS主题命名规范
  $0 --verbose config/config.json # 详细验证指定文件

EOF
}

# 验证JSON语法
validate_json_syntax() {
    local file="$1"
    
    if ! command -v jq &> /dev/null; then
        log_warn "jq未安装，跳过JSON语法验证"
        return 0
    fi
    
    if jq empty "$file" 2>/dev/null; then
        log_success "JSON语法正确: $file"
        return 0
    else
        log_error "JSON语法错误: $file"
        return 1
    fi
}

# 验证DDS主题命名规范
validate_topic_naming() {
    local file="$1"
    local errors=0
    
    # 检查是否使用了旧的主题命名
    local old_topics=("VideoFrames" "AIFrames" "CloudFrames" "AIResults" "TestVideoFrames")
    
    for old_topic in "${old_topics[@]}"; do
        if grep -q "\"$old_topic\"" "$file"; then
            log_error "发现旧的主题命名: $old_topic (应该使用下划线格式)"
            ((errors++))
        fi
    done
    
    # 检查推荐的主题命名
    local new_topics=("Video_Frames" "AI_Frames" "Cloud_Frames" "AI_Results")
    local found_topics=0
    
    for new_topic in "${new_topics[@]}"; do
        if grep -q "\"$new_topic\"" "$file"; then
            ((found_topics++))
            if [[ "$VERBOSE" == true ]]; then
                log_info "找到正确的主题命名: $new_topic"
            fi
        fi
    done
    
    if [[ $found_topics -gt 0 ]]; then
        log_success "DDS主题命名规范正确"
    fi
    
    return $errors
}

# 验证配置完整性
validate_config_completeness() {
    local file="$1"
    local errors=0
    
    if ! command -v jq &> /dev/null; then
        log_warn "jq未安装，跳过配置完整性验证"
        return 0
    fi
    
    # 检查必需的配置节
    local required_sections=("dds" "video_capture" "system")
    
    for section in "${required_sections[@]}"; do
        if ! jq -e ".$section" "$file" >/dev/null 2>&1; then
            log_error "缺少必需的配置节: $section"
            ((errors++))
        else
            if [[ "$VERBOSE" == true ]]; then
                log_info "找到配置节: $section"
            fi
        fi
    done
    
    # 检查DDS配置
    if jq -e ".dds" "$file" >/dev/null 2>&1; then
        # 检查domain_id
        if ! jq -e ".dds.domain_id" "$file" >/dev/null 2>&1; then
            log_error "DDS配置缺少domain_id"
            ((errors++))
        fi
        
        # 检查topics配置
        if ! jq -e ".dds.topics" "$file" >/dev/null 2>&1; then
            log_error "DDS配置缺少topics"
            ((errors++))
        fi
    fi
    
    # 检查视频捕获配置
    if jq -e ".video_capture" "$file" >/dev/null 2>&1; then
        # 检查source_type
        local source_type=$(jq -r ".video_capture.source_type" "$file" 2>/dev/null)
        if [[ "$source_type" != "v4l2" && "$source_type" != "rtsp" ]]; then
            log_error "video_capture.source_type必须是'v4l2'或'rtsp'"
            ((errors++))
        fi
        
        # 检查分辨率配置
        if ! jq -e ".video_capture.width" "$file" >/dev/null 2>&1; then
            log_error "video_capture配置缺少width"
            ((errors++))
        fi
        
        if ! jq -e ".video_capture.height" "$file" >/dev/null 2>&1; then
            log_error "video_capture配置缺少height"
            ((errors++))
        fi
    fi
    
    return $errors
}

# 验证端口配置
validate_ports() {
    local file="$1"
    local errors=0
    
    if ! command -v jq &> /dev/null; then
        return 0
    fi
    
    # 检查RTSP服务器端口
    if jq -e ".rtsp_server.server_port" "$file" >/dev/null 2>&1; then
        local port=$(jq -r ".rtsp_server.server_port" "$file")
        if [[ $port -lt 1024 || $port -gt 65535 ]]; then
            log_error "RTSP服务器端口超出有效范围 (1024-65535): $port"
            ((errors++))
        elif [[ $port -lt 1024 ]]; then
            log_warn "RTSP服务器端口 $port 需要root权限"
        fi
    fi
    
    return $errors
}

# 验证路径配置
validate_paths() {
    local file="$1"
    local errors=0
    
    if ! command -v jq &> /dev/null; then
        return 0
    fi
    
    # 检查设备路径
    if jq -e ".video_capture.device" "$file" >/dev/null 2>&1; then
        local device=$(jq -r ".video_capture.device" "$file")
        if [[ -n "$device" && ! -e "$device" ]]; then
            log_warn "视频设备不存在: $device"
        fi
    fi
    
    # 检查AI模型路径
    if jq -e ".ai_processor.model_path" "$file" >/dev/null 2>&1; then
        local model_path=$(jq -r ".ai_processor.model_path" "$file")
        if [[ -n "$model_path" && ! -f "$model_path" ]]; then
            log_warn "AI模型文件不存在: $model_path"
        fi
    fi
    
    # 检查日志目录
    if jq -e ".system.log_file" "$file" >/dev/null 2>&1; then
        local log_file=$(jq -r ".system.log_file" "$file")
        local log_dir=$(dirname "$log_file")
        if [[ ! -d "$log_dir" ]]; then
            log_warn "日志目录不存在: $log_dir"
        fi
    fi
    
    return $errors
}

# 验证单个配置文件
validate_config_file() {
    local file="$1"
    local total_errors=0
    
    log_info "验证配置文件: $file"
    
    # 检查文件是否存在
    if [[ ! -f "$file" ]]; then
        log_error "配置文件不存在: $file"
        return 1
    fi
    
    # JSON语法验证
    if ! validate_json_syntax "$file"; then
        ((total_errors++))
    fi
    
    # DDS主题命名验证
    local topic_errors
    topic_errors=$(validate_topic_naming "$file")
    ((total_errors += topic_errors))
    
    # 配置完整性验证
    local completeness_errors
    completeness_errors=$(validate_config_completeness "$file")
    ((total_errors += completeness_errors))
    
    # 端口配置验证
    local port_errors
    port_errors=$(validate_ports "$file")
    ((total_errors += port_errors))
    
    # 路径配置验证
    local path_errors
    path_errors=$(validate_paths "$file")
    ((total_errors += path_errors))
    
    if [[ $total_errors -eq 0 ]]; then
        log_success "配置文件验证通过: $file"
    else
        log_error "配置文件验证失败: $file (发现 $total_errors 个问题)"
    fi
    
    return $total_errors
}

# 验证所有配置文件
validate_all_configs() {
    local total_errors=0
    
    log_info "验证所有配置文件..."
    
    # 查找所有JSON配置文件
    local config_files=()
    while IFS= read -r -d '' file; do
        config_files+=("$file")
    done < <(find "$CONFIG_DIR" -name "*.json" -type f -print0 2>/dev/null)
    
    if [[ ${#config_files[@]} -eq 0 ]]; then
        log_warn "未找到配置文件"
        return 0
    fi
    
    for config_file in "${config_files[@]}"; do
        local errors
        errors=$(validate_config_file "$config_file")
        ((total_errors += errors))
        echo
    done
    
    log_info "总计验证了 ${#config_files[@]} 个配置文件"
    
    if [[ $total_errors -eq 0 ]]; then
        log_success "所有配置文件验证通过！"
    else
        log_error "发现 $total_errors 个配置问题"
    fi
    
    return $total_errors
}

# 默认参数
VERBOSE=false
VALIDATE_ALL=false
TOPICS_ONLY=false
SYNTAX_ONLY=false
CONFIG_FILE="$CONFIG_DIR/config.json"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -a|--all)
            VALIDATE_ALL=true
            shift
            ;;
        -t|--topics)
            TOPICS_ONLY=true
            shift
            ;;
        -s|--syntax)
            SYNTAX_ONLY=true
            shift
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            CONFIG_FILE="$1"
            shift
            ;;
    esac
done

# 主逻辑
main() {
    log_info "开始配置验证..."
    
    if [[ "$VALIDATE_ALL" == true ]]; then
        validate_all_configs
        exit $?
    fi
    
    if [[ "$TOPICS_ONLY" == true ]]; then
        validate_topic_naming "$CONFIG_FILE"
        exit $?
    fi
    
    if [[ "$SYNTAX_ONLY" == true ]]; then
        validate_json_syntax "$CONFIG_FILE"
        exit $?
    fi
    
    # 验证指定的配置文件
    validate_config_file "$CONFIG_FILE"
    exit $?
}

# 运行主函数
main "$@"
