#!/bin/bash

# 配置迁移脚本
# 将旧的DDS主题名称迁移到新的命名规范

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
配置迁移脚本 - 将DDS主题名称从旧格式迁移到新格式

用法: $0 [选项] [文件/目录]

选项:
  -h, --help          显示此帮助信息
  -d, --dry-run       预览模式，不实际修改文件
  -b, --backup        创建备份文件
  -v, --verbose       详细输出
  -r, --recursive     递归处理目录
  
参数:
  文件/目录           要处理的文件或目录路径（默认：整个项目）

主题名称映射:
  VideoFrames    -> Video_Frames
  AIFrames       -> AI_Frames  
  CloudFrames    -> Cloud_Frames
  AIResults      -> AI_Results
  TestVideoFrames -> Test_Video_Frames

示例:
  $0 --dry-run                    # 预览所有需要修改的文件
  $0 --backup config/config.json  # 迁移单个配置文件并备份
  $0 -r src/                      # 递归处理src目录
  $0 -v --backup                  # 详细输出并备份所有修改的文件

EOF
}

# 主题名称映射
declare -A TOPIC_MAPPING=(
    ["VideoFrames"]="Video_Frames"
    ["AIFrames"]="AI_Frames"
    ["CloudFrames"]="Cloud_Frames"
    ["AIResults"]="AI_Results"
    ["TestVideoFrames"]="Test_Video_Frames"
    ["MainFrames"]="Main_Frames"
)

# 默认参数
DRY_RUN=false
CREATE_BACKUP=false
VERBOSE=false
RECURSIVE=false
TARGET_PATH=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -b|--backup)
            CREATE_BACKUP=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -r|--recursive)
            RECURSIVE=true
            shift
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            TARGET_PATH="$1"
            shift
            ;;
    esac
done

# 如果没有指定目标路径，使用项目根目录
if [[ -z "$TARGET_PATH" ]]; then
    TARGET_PATH="$PROJECT_ROOT"
fi

# 检查目标路径是否存在
if [[ ! -e "$TARGET_PATH" ]]; then
    log_error "目标路径不存在: $TARGET_PATH"
    exit 1
fi

# 创建备份函数
create_backup() {
    local file="$1"
    local backup_file="${file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [[ "$CREATE_BACKUP" == true ]]; then
        cp "$file" "$backup_file"
        log_info "已创建备份: $backup_file"
    fi
}

# 处理单个文件
process_file() {
    local file="$1"
    local changes_made=false
    local temp_file=$(mktemp)
    
    # 检查文件是否包含需要替换的内容
    local has_old_topics=false
    for old_topic in "${!TOPIC_MAPPING[@]}"; do
        if grep -q "$old_topic" "$file"; then
            has_old_topics=true
            break
        fi
    done
    
    if [[ "$has_old_topics" == false ]]; then
        rm "$temp_file"
        return 0
    fi
    
    log_info "处理文件: $file"
    
    # 复制原文件到临时文件
    cp "$file" "$temp_file"
    
    # 执行替换
    for old_topic in "${!TOPIC_MAPPING[@]}"; do
        local new_topic="${TOPIC_MAPPING[$old_topic]}"
        
        if grep -q "$old_topic" "$temp_file"; then
            if [[ "$VERBOSE" == true ]]; then
                log_debug "  替换: $old_topic -> $new_topic"
            fi
            
            # 使用sed进行替换，注意单词边界
            sed -i "s/\b$old_topic\b/$new_topic/g" "$temp_file"
            changes_made=true
        fi
    done
    
    # 如果有变更
    if [[ "$changes_made" == true ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log_warn "  [预览] 将会修改此文件"
            # 显示差异
            if [[ "$VERBOSE" == true ]]; then
                echo "--- 原文件"
                echo "+++ 修改后"
                diff -u "$file" "$temp_file" || true
                echo
            fi
        else
            # 创建备份
            create_backup "$file"
            
            # 应用更改
            mv "$temp_file" "$file"
            log_info "  ✓ 已更新"
        fi
    else
        rm "$temp_file"
    fi
}

# 获取要处理的文件列表
get_files_to_process() {
    local path="$1"
    local files=()
    
    if [[ -f "$path" ]]; then
        # 单个文件
        files=("$path")
    elif [[ -d "$path" ]]; then
        # 目录
        if [[ "$RECURSIVE" == true ]]; then
            # 递归查找相关文件
            while IFS= read -r -d '' file; do
                files+=("$file")
            done < <(find "$path" -type f \( -name "*.json" -o -name "*.cpp" -o -name "*.h" -o -name "*.md" -o -name "*.sh" \) -print0)
        else
            # 只处理当前目录
            for ext in json cpp h md sh; do
                for file in "$path"/*."$ext"; do
                    if [[ -f "$file" ]]; then
                        files+=("$file")
                    fi
                done
            done
        fi
    fi
    
    printf '%s\n' "${files[@]}"
}

# 主处理逻辑
main() {
    log_info "开始配置迁移..."
    log_info "目标路径: $TARGET_PATH"
    
    if [[ "$DRY_RUN" == true ]]; then
        log_warn "预览模式 - 不会实际修改文件"
    fi
    
    # 显示主题映射
    if [[ "$VERBOSE" == true ]]; then
        log_info "主题名称映射:"
        for old_topic in "${!TOPIC_MAPPING[@]}"; do
            echo "  $old_topic -> ${TOPIC_MAPPING[$old_topic]}"
        done
        echo
    fi
    
    # 获取文件列表
    local files_to_process
    mapfile -t files_to_process < <(get_files_to_process "$TARGET_PATH")
    
    if [[ ${#files_to_process[@]} -eq 0 ]]; then
        log_warn "没有找到需要处理的文件"
        exit 0
    fi
    
    log_info "找到 ${#files_to_process[@]} 个文件需要检查"
    
    # 处理每个文件
    local processed_count=0
    for file in "${files_to_process[@]}"; do
        if [[ -f "$file" ]]; then
            process_file "$file"
            ((processed_count++))
        fi
    done
    
    log_info "处理完成！检查了 $processed_count 个文件"
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "要应用更改，请重新运行此脚本而不使用 --dry-run 选项"
    fi
}

# 运行主函数
main "$@"
