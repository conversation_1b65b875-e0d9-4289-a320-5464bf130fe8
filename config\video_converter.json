{"_description": "Video Converter Service Default Configuration", "_version": "1.0", "video_converter": {"enable_hardware_acceleration": true, "processing_control": {"_description": "Control which processing paths are enabled", "enable_ai": true, "enable_cloud_streaming": true}, "dds": {"input_topic": "Video_Frames", "ai_output_topic": "AI_Frames", "cloud_output_topic": "Cloud_Frames", "domain_id": 0, "input_max_samples": 5, "output_max_samples": 5}, "ai_output": {"format": "RGB24", "width": 640, "height": 640, "enable_resize": true, "resize_algorithm": "bilinear", "enable_crop": false, "crop_x": 0, "crop_y": 0, "crop_width": 0, "crop_height": 0}, "cloud_output": {"format": "H264", "width": 1280, "height": 720, "bitrate": 2000000, "fps": 30, "gop_size": 15, "profile": "baseline", "preset": "ultrafast", "tune": "zerolatency"}, "hardware_acceleration": {"enable_gpu": true, "enable_vaapi": true, "enable_nvenc": true, "enable_qsv": false, "fallback_to_software": true}, "performance": {"thread_priority": 80, "cpu_affinity": [2, 3], "thread_pool_size": 2, "stats_interval_sec": 15, "enable_zero_copy": true, "buffer_pool_size": 10}, "quality_control": {"enable_adaptive_quality": true, "min_quality": 20, "max_quality": 95, "target_fps": 30, "drop_frame_threshold": 0.1}, "logging": {"level": "INFO", "enable_conversion_stats": true, "enable_performance_stats": true, "log_frame_details": false}}, "format_mappings": {"_description": "Input to output format conversion mappings", "YUYV_to_RGB24": {"input_format": 1448695129, "output_format": "RGB24", "conversion_method": "rga_hardware", "color_space": "BT709"}, "MJPG_to_H264": {"input_format": 1196444237, "output_format": "H264", "conversion_method": "mpp_hardware", "decode_method": "mpp_mjpeg_decoder", "encode_method": "gstreamer_h264"}, "H264_passthrough": {"input_format": 875967048, "output_format": "H264", "conversion_method": "passthrough", "enable_transcoding": false}, "RGB24_to_RGB24": {"input_format": 859981650, "output_format": "RGB24", "conversion_method": "rga_hardware", "resize_method": "adaptive_letterbox"}}, "conversion_profiles": {"_description": "Predefined conversion configurations", "high_quality": {"ai_output": {"width": 1024, "height": 1024, "format": "RGB24"}, "cloud_output": {"width": 1920, "height": 1080, "bitrate": 5000000, "gop_size": 30}}, "low_latency": {"ai_output": {"width": 416, "height": 416, "format": "RGB24"}, "cloud_output": {"width": 1280, "height": 720, "bitrate": 1000000, "gop_size": 1, "preset": "ultrafast"}}, "balanced": {"ai_output": {"width": 640, "height": 640, "format": "RGB24"}, "cloud_output": {"width": 1280, "height": 720, "bitrate": 2000000, "gop_size": 15}}}}