# GStreamer Migration Guide

## Overview

The RTSP client implementation has been migrated from FFmpeg to GStreamer to provide better streaming performance, lower latency, and more robust RTSP handling.

## Key Changes

### 1. Dependencies
- **Removed**: FFmpeg libraries (libavcodec, libavformat, libavutil, libswscale, libswresample)
- **Added**: GStreamer libraries (gstreamer-1.0, gstreamer-app-1.0, gstreamer-video-1.0, gstreamer-rtsp-1.0)

### 2. RTSP Client Implementation
- Replaced FFmpeg-based RTSPClient with GStreamer pipeline-based implementation
- Improved error handling and connection management
- Added support for hardware-accelerated decoding (NVDEC, VAAPI)
- Better handling of network issues and stream interruptions

### 3. Configuration Changes
Added new GStreamer-specific configuration options in `config.json`:
```json
"gstreamer": {
  "pipeline_latency_ms": 200,
  "buffer_mode": "low-latency",
  "drop_on_latency": true,
  "hw_decode_priority": ["nvh264dec", "vaapih264dec", "avdec_h264"],
  "rtsp_timeout_ms": 5000,
  "rtsp_retry_count": 3,
  "debug_level": 2
}
```

## Installation Requirements

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    libgstreamer-plugins-good1.0-dev \
    libgstreamer-plugins-bad1.0-dev \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-plugins-ugly \
    gstreamer1.0-rtsp
```

### CentOS/RHEL/Fedora
```bash
sudo dnf install \
    gstreamer1-devel \
    gstreamer1-plugins-base-devel \
    gstreamer1-plugins-good \
    gstreamer1-plugins-bad-free \
    gstreamer1-plugins-ugly-free \
    gstreamer1-rtsp
```

## Building

### Using CMake
```bash
mkdir build && cd build
cmake ..
make -j$(nproc)
```

### Using Makefile
```bash
make check-deps  # Check dependencies
make all         # Build all components
```

## Testing

A new test program is available to verify GStreamer RTSP functionality:

```bash
# Build test program
cd build
make test_rtsp_gstreamer

# Run test
./test/test_rtsp_gstreamer rtsp://*************:554/stream

# Test with TCP transport
./test/test_rtsp_gstreamer rtsp://*************:554/stream tcp

# Test with software decoding
./test/test_rtsp_gstreamer rtsp://*************:554/stream tcp sw_decode
```

## Performance Improvements

### Latency Reduction
- GStreamer's optimized RTSP handling reduces end-to-end latency
- Configurable buffer management for low-latency scenarios
- Hardware-accelerated decoding when available

### Resource Usage
- More efficient memory management with GStreamer's buffer pools
- Better CPU utilization through optimized pipelines
- Reduced memory copies with zero-copy operations where possible

### Network Handling
- Improved RTSP protocol compliance
- Better handling of network interruptions and reconnections
- Configurable timeout and retry mechanisms

## Troubleshooting

### Common Issues

1. **Missing GStreamer plugins**
   ```bash
   # Check available plugins
   gst-inspect-1.0 | grep rtsp
   gst-inspect-1.0 | grep h264
   ```

2. **Hardware decoding not working**
   - Check if NVIDIA drivers are installed for NVDEC
   - Check if VA-API is available for Intel/AMD hardware acceleration
   - Fallback to software decoding is automatic

3. **RTSP connection issues**
   - Try TCP transport mode: set `"use_tcp": true` in config
   - Adjust timeout values in configuration
   - Check network connectivity and firewall settings

### Debug Information

Enable GStreamer debug output:
```bash
export GST_DEBUG=3
export GST_DEBUG_FILE=/tmp/gstreamer.log
./video_capture_main
```

Debug levels:
- 0: No debug output
- 1: Error messages only
- 2: Error and warning messages
- 3: Error, warning, and info messages
- 4: All debug messages

## API Compatibility

The public interface of the RTSPClient class remains unchanged:
- `bool init(const std::string& url, bool use_tcp, bool hw_decode)`
- `Frame get_frame()`
- `void cleanup()`
- `bool is_connected()`

This ensures that existing code using the RTSPClient class will continue to work without modifications.

## Migration Benefits

1. **Better RTSP Support**: More robust RTSP protocol handling
2. **Lower Latency**: Optimized streaming pipeline
3. **Hardware Acceleration**: Better support for GPU-accelerated decoding
4. **Stability**: Improved error handling and recovery
5. **Flexibility**: Easier to extend with additional GStreamer plugins
6. **Community Support**: Active GStreamer community and regular updates

## Future Enhancements

With GStreamer as the foundation, future enhancements can include:
- Support for additional streaming protocols (WebRTC, SRT)
- Advanced video processing filters
- Multi-stream handling
- Dynamic pipeline reconfiguration
- Better integration with cloud streaming services
