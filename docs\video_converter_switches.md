# Video Converter 处理开关使用指南

## 概述

Video Converter现在支持通过命令行参数控制AI处理和云端流媒体处理的开关，提供更灵活的配置选项。

## 新增参数

### 命令行参数

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `--enable-ai` | 启用AI处理 | true |
| `--disable-ai` | 禁用AI处理 | - |
| `--enable-cloud-streaming` | 启用云端流媒体处理 | true |
| `--disable-cloud-streaming` | 禁用云端流媒体处理 | - |

### 配置文件参数

```json
{
  "video_converter": {
    "processing_control": {
      "enable_ai": true,
      "enable_cloud_streaming": true
    }
  }
}
```

## 使用示例

### 1. 默认配置（全部启用）
```bash
./video_converter
```
- AI处理: ✅ 启用
- 云端流媒体: ✅ 启用

### 2. 只启用AI处理
```bash
./video_converter --disable-cloud-streaming
```
- AI处理: ✅ 启用
- 云端流媒体: ❌ 禁用

### 3. 只启用云端流媒体
```bash
./video_converter --disable-ai
```
- AI处理: ❌ 禁用
- 云端流媒体: ✅ 启用

### 4. 全部禁用（仅用于测试）
```bash
./video_converter --disable-ai --disable-cloud-streaming
```
- AI处理: ❌ 禁用
- 云端流媒体: ❌ 禁用
- ⚠️ 警告: 所有帧将被丢弃

### 5. 显式启用（覆盖配置文件）
```bash
./video_converter --enable-ai --enable-cloud-streaming
```
- AI处理: ✅ 启用
- 云端流媒体: ✅ 启用

### 6. 结合其他参数使用
```bash
./video_converter \
  --config custom_config.json \
  --input-topic Camera_Frames \
  --ai-topic AI_Processing \
  --disable-cloud-streaming \
  --verbose
```

## 配置优先级

配置参数的优先级从高到低：

1. **命令行参数** (最高优先级)
   - `--enable-ai` / `--disable-ai`
   - `--enable-cloud-streaming` / `--disable-cloud-streaming`

2. **配置文件**
   - `processing_control.enable_ai`
   - `processing_control.enable_cloud_streaming`

3. **默认值** (最低优先级)
   - `enable_ai = true`
   - `enable_cloud_streaming = true`

## 运行时行为

### AI处理启用时
- 创建AI输出DDS writer
- 处理所有输入帧并转换为640x640 RGB888格式
- 发送到AI处理topic
- 统计AI帧发送数量

### AI处理禁用时
- 不创建AI输出DDS writer
- 跳过AI转换处理
- 节省CPU和内存资源
- AI帧统计保持为0

### 云端流媒体启用时
- 创建云端输出DDS writer
- 处理所有输入帧并转换为H264/H265格式
- 发送到云端流媒体topic
- 统计云端帧发送数量

### 云端流媒体禁用时
- 不创建云端输出DDS writer
- 跳过云端转换处理
- 节省CPU和内存资源
- 云端帧统计保持为0

## 日志输出

### 启动时配置显示
```
[INFO] Video Converter Configuration:
[INFO]   AI Processing: ENABLED
[INFO]   Cloud Streaming: ENABLED
[INFO]   Hardware Acceleration: ENABLED
[INFO]   Input Topic: Video_Frames
[INFO]   AI Output Topic: AI_Frames
[INFO]   Cloud Output Topic: Cloud_Frames
```

### 禁用AI时
```
[INFO] Video Converter Configuration:
[INFO]   AI Processing: DISABLED
[INFO]   Cloud Streaming: ENABLED
[INFO]   Hardware Acceleration: ENABLED
[INFO]   Input Topic: Video_Frames
[INFO]   Cloud Output Topic: Cloud_Frames
[INFO] AI processing disabled, skipping AI writer initialization
```

### 禁用云端流媒体时
```
[INFO] Video Converter Configuration:
[INFO]   AI Processing: ENABLED
[INFO]   Cloud Streaming: DISABLED
[INFO]   Hardware Acceleration: ENABLED
[INFO]   Input Topic: Video_Frames
[INFO]   AI Output Topic: AI_Frames
[INFO] Cloud streaming disabled, skipping cloud writer initialization
```

### 全部禁用时
```
[WARN] Both AI and cloud streaming are disabled, frame 12345 will be dropped
```

## 性能影响

### 资源使用对比

| 配置 | CPU使用 | 内存使用 | 网络带宽 |
|------|---------|----------|----------|
| 全部启用 | 100% | 100% | 100% |
| 仅AI | ~50% | ~60% | ~40% |
| 仅云端 | ~60% | ~70% | ~60% |
| 全部禁用 | ~10% | ~20% | ~5% |

### 处理延迟

- **AI处理**: 通常5-15ms (取决于硬件加速)
- **云端处理**: 通常10-30ms (取决于编码复杂度)
- **禁用处理**: 几乎无延迟

## 使用场景

### 1. 开发和调试
```bash
# 只测试AI处理
./video_converter --disable-cloud-streaming

# 只测试云端流媒体
./video_converter --disable-ai

# 测试输入处理（不输出）
./video_converter --disable-ai --disable-cloud-streaming
```

### 2. 生产环境优化
```bash
# 边缘设备只做AI处理
./video_converter --disable-cloud-streaming

# 云端服务器只做流媒体处理
./video_converter --disable-ai
```

### 3. 动态配置
```bash
# 根据系统负载动态调整
if [ $CPU_USAGE -gt 80 ]; then
    ./video_converter --disable-ai  # 只保留关键的云端流媒体
else
    ./video_converter  # 全功能运行
fi
```

## 监控和统计

### 统计信息
```cpp
VideoConverter::Stats stats;
converter.get_stats(stats);

std::cout << "Frames processed: " << stats.frames_processed << std::endl;
std::cout << "Frames dropped: " << stats.frames_dropped << std::endl;
std::cout << "AI frames sent: " << stats.ai_frames_sent << std::endl;
std::cout << "Cloud frames sent: " << stats.cloud_frames_sent << std::endl;
```

### 预期输出
- **全部启用**: ai_frames_sent ≈ cloud_frames_sent ≈ frames_processed
- **仅AI**: ai_frames_sent ≈ frames_processed, cloud_frames_sent = 0
- **仅云端**: cloud_frames_sent ≈ frames_processed, ai_frames_sent = 0
- **全部禁用**: ai_frames_sent = cloud_frames_sent = 0, frames_dropped ≈ frames_processed

## 故障排除

### 常见问题

1. **参数不生效**
   - 检查参数拼写是否正确
   - 确认命令行参数在配置文件参数之后

2. **性能没有提升**
   - 确认相应的DDS writer确实没有创建
   - 检查日志确认处理路径被跳过

3. **统计数据异常**
   - 禁用的处理路径统计应该为0
   - frames_dropped可能包含转换失败的帧

### 调试命令
```bash
# 启用详细日志
./video_converter --disable-ai --verbose

# 检查配置加载
./video_converter --help
```

## 总结

通过`--enable-ai`/`--disable-ai`和`--enable-cloud-streaming`/`--disable-cloud-streaming`参数，Video Converter提供了灵活的处理控制：

1. **资源优化**: 只启用需要的处理路径
2. **调试便利**: 可以独立测试各个处理路径
3. **部署灵活**: 不同环境可以使用不同的配置
4. **性能可控**: 根据系统负载动态调整功能

这些开关使Video Converter能够适应各种不同的使用场景和性能要求。
