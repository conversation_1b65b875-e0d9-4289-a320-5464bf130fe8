# RGA Accelerator 实现说明

## 基于官方librga库的实现

本实现参考了Rockchip官方的librga库示例：
- **官方仓库**: https://github.com/airockchip/librga
- **示例目录**: https://github.com/airockchip/librga/tree/main/samples

## 核心API使用

### 1. 头文件包含
```cpp
#include <im2d.hpp>      // 主要的im2d API
#include <RgaUtils.h>    // RGA工具函数
#include <rga.h>         // RGA基础定义
```

### 2. 初始化和清理
```cpp
// 初始化RGA
int ret = c_RkRgaInit();
if (ret) {
    // 初始化失败
}

// 清理RGA
c_RkRgaDeInit();
```

### 3. 缓冲区设置
```cpp
rga_buffer_t src_buf, dst_buf;

// 设置源缓冲区
src_buf.width = src_width;
src_buf.height = src_height;
src_buf.format = rga_format;
src_buf.size = data_size;
src_buf.vir_addr = data_ptr;

// 设置目标缓冲区
dst_buf.width = dst_width;
dst_buf.height = dst_height;
dst_buf.format = rga_format;
dst_buf.size = data_size;
dst_buf.vir_addr = data_ptr;
```

### 4. 主要操作API

#### 简单缩放
```cpp
int ret = imresize(src_buf, dst_buf);
```

#### 复杂操作（裁剪+缩放+格式转换）
```cpp
im_rect src_rect = {crop_x, crop_y, crop_w, crop_h};
im_rect dst_rect = {0, 0, dst_w, dst_h};

int ret = improcess(src_buf, dst_buf, {}, src_rect, dst_rect, {}, 0);
```

## 格式映射

### V4L2到RGA格式转换
```cpp
int v4l2_to_rga_format(int32_t v4l2_format) {
    switch (v4l2_format) {
        case V4L2_PIX_FMT_YUYV:  return RK_FORMAT_YUYV_422;
        case V4L2_PIX_FMT_UYVY:  return RK_FORMAT_UYVY_422;
        case V4L2_PIX_FMT_RGB24: return RK_FORMAT_RGB_888;
        case V4L2_PIX_FMT_BGR24: return RK_FORMAT_BGR_888;
        case V4L2_PIX_FMT_NV12:  return RK_FORMAT_YCbCr_420_SP;
        case V4L2_PIX_FMT_NV21:  return RK_FORMAT_YCrCb_420_SP;
        case V4L2_PIX_FMT_RGBA32: return RK_FORMAT_RGBA_8888;
        case V4L2_PIX_FMT_BGRA32: return RK_FORMAT_BGRA_8888;
        default: return RK_FORMAT_RGB_888;
    }
}
```

## 实现特点

### 1. 面向对象封装
- 将C风格的RGA API封装为C++类
- 提供简洁的接口函数
- 自动管理资源和错误处理

### 2. 内存管理
```cpp
bool allocate_frame_buffer(Frame& frame, int width, int height, int32_t format) {
    // 根据格式计算缓冲区大小
    size_t buffer_size = 0;
    switch (format) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
            buffer_size = width * height * 2;  // 16 bpp
            break;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
            buffer_size = width * height * 3;  // 24 bpp
            break;
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            buffer_size = width * height * 3 / 2;  // 12 bpp
            break;
        case V4L2_PIX_FMT_RGBA32:
        case V4L2_PIX_FMT_BGRA32:
            buffer_size = width * height * 4;  // 32 bpp
            break;
    }
    
    frame.data.resize(buffer_size);
    return true;
}
```

### 3. 错误处理
```cpp
// 检查RGA操作结果
int ret = imresize(src_buf, dst_buf);
if (ret != IM_STATUS_SUCCESS) {
    LOG_E("RGA resize operation failed: %d", ret);
    return false;
}
```

## 性能优化

### 1. 缓冲区复用
```cpp
class RGAAccelerator {
private:
    rga_buffer_t src_buf_;  // 复用的源缓冲区描述符
    rga_buffer_t dst_buf_;  // 复用的目标缓冲区描述符
};
```

### 2. 批量处理
```cpp
// 初始化一次，处理多帧
RGAAccelerator rga;
rga.init();

for (auto& frame : frames) {
    rga.convert_and_scale(frame, dst, 640, 640, V4L2_PIX_FMT_RGB24);
}

rga.cleanup();
```

### 3. 内存对齐
- RGA硬件对内存对齐有要求
- 使用标准的内存分配确保对齐
- 避免使用非对齐的内存地址

## 与官方示例的对比

### 官方示例特点
```cpp
// 官方示例通常使用直接的API调用
rga_buffer_t src = wrapbuffer_virtualaddr(src_ptr, src_w, src_h, src_format);
rga_buffer_t dst = wrapbuffer_virtualaddr(dst_ptr, dst_w, dst_h, dst_format);

imresize(src, dst);
```

### 我们的封装优势
```cpp
// 我们的封装提供更高级的接口
RGAAccelerator rga;
rga.convert_and_scale(src_frame, dst_frame, 640, 640, V4L2_PIX_FMT_RGB24);
```

**优势**:
1. **类型安全**: 使用Frame结构体而不是原始指针
2. **错误处理**: 统一的错误处理和日志记录
3. **资源管理**: 自动的初始化和清理
4. **易用性**: 简化的API接口

## 调试和诊断

### 1. 启用RGA调试
```bash
export RGA_DEBUG=1
```

### 2. 检查RGA设备
```bash
# 检查设备节点
ls -la /dev/rga

# 检查驱动模块
lsmod | grep rga

# 检查设备权限
sudo chmod 666 /dev/rga
```

### 3. 常见问题
```cpp
// 1. 格式不支持
if (rga_format == 0) {
    LOG_E("Unsupported format: 0x%08x", v4l2_format);
}

// 2. 缓冲区大小错误
if (frame.data.size() < expected_size) {
    LOG_E("Buffer size mismatch: %zu < %zu", frame.data.size(), expected_size);
}

// 3. 内存对齐问题
if ((uintptr_t)frame.data.data() % 16 != 0) {
    LOG_W("Buffer not aligned to 16 bytes");
}
```

## 测试验证

### 1. 功能测试
- 格式转换测试
- 缩放测试
- 裁剪测试
- 组合操作测试

### 2. 性能测试
```cpp
// 测试100帧的处理时间
auto start = std::chrono::high_resolution_clock::now();
for (int i = 0; i < 100; i++) {
    rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24);
}
auto end = std::chrono::high_resolution_clock::now();
auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
float fps = 100000.0 / duration.count();
```

### 3. 压力测试
- 长时间运行测试
- 内存泄漏检测
- 多线程并发测试

## 总结

基于librga官方库的RGA Accelerator实现提供了：

1. **标准兼容**: 完全基于官方API
2. **高性能**: 充分利用硬件加速
3. **易用性**: 简化的C++接口
4. **可靠性**: 完善的错误处理
5. **可维护性**: 清晰的代码结构

这个实现可以作为video_converter中高效图像处理的核心组件，特别适合实时视频处理场景。
