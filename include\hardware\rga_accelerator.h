#ifndef RGA_ACCELERATOR_H
#define RGA_ACCELERATOR_H

#include "common.h"

// RGA硬件加速头文件
// 禁用第三方库中的匿名结构体警告（来自im_color结构体定义）
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wpedantic"
#include <im2d.hpp>
#include <RgaUtils.h>
#include <rga.h>
#pragma GCC diagnostic pop

// RGA硬件加速接口
class RGAAccelerator {
private:
    bool rga_available_ = false;

public:
    RGAAccelerator() = default;
    ~RGAAccelerator() { cleanup(); }

    bool init();
    bool convert_and_scale(const Frame& src, Frame& dst, int target_width, int target_height, int32_t target_format);
    bool resize(const Frame& src, Frame& dst, int target_width, int target_height);
    bool crop_and_resize(const Frame& src, Frame& dst, int crop_x, int crop_y, int crop_w, int crop_h, int target_width, int target_height);
    bool format_convert(const Frame& src, Frame& dst, int32_t target_format);
    void cleanup();

private:
    int v4l2_to_rga_format(int32_t v4l2_format);
    bool allocate_frame_buffer(Frame& frame, int width, int height, int32_t format);
    int align_width_to_16(int width);
    int calculate_stride(int width, int32_t format);
};

// RGAAccelerator实现
inline bool RGAAccelerator::init() {
    // RGA不需要显式初始化，直接标记为可用
    // 实际的RGA设备检查在第一次使用时进行
    rga_available_ = true;
    LOG_I("RGA accelerator initialized successfully");
    return true;
}

inline bool RGAAccelerator::convert_and_scale(const Frame& src, Frame& dst, int target_width, int target_height, int32_t target_format) {
    if (!rga_available_) {
        return false;
    }

    // 分配目标帧缓冲区
    if (!allocate_frame_buffer(dst, target_width, target_height, target_format)) {
        LOG_E("Failed to allocate destination frame buffer");
        return false;
    }

    // 获取RGA格式
    int src_rga_format = v4l2_to_rga_format(src.format);
    int dst_rga_format = v4l2_to_rga_format(target_format);

    if (src_rga_format == 0 || dst_rga_format == 0) {
        LOG_E("Unsupported format for RGA conversion");
        return false;
    }

    // 计算stride对齐
    int src_stride = calculate_stride(src.width, src.format);
    int dst_stride = calculate_stride(target_width, target_format);

    // 使用wrapbuffer_virtualaddr_t创建RGA缓冲区
    rga_buffer_t src_buf = wrapbuffer_virtualaddr_t((void*)src.data.data(),
                                                  src.width, src.height,
                                                  src_stride, src.height,
                                                  src_rga_format);

    rga_buffer_t dst_buf = wrapbuffer_virtualaddr_t((void*)dst.data.data(),
                                                  target_width, target_height,
                                                  dst_stride, target_height,
                                                  dst_rga_format);

    // 检查和记录stride对齐信息
    LOG_D("RGA operation: src %dx%d (stride %d, format 0x%x) -> dst %dx%d (stride %d, format 0x%x)",
          src.width, src.height, src_stride, src_rga_format,
          target_width, target_height, dst_stride, dst_rga_format);

    // 执行RGA操作：格式转换 + 缩放
    int ret = imresize(src_buf, dst_buf);
    if (ret != IM_STATUS_SUCCESS) {
        LOG_E("RGA resize operation failed: %d", ret);
        return false;
    }

    // 更新目标帧信息
    dst.frame_id = src.frame_id;
    dst.timestamp = src.timestamp;
    dst.source_type = src.source_type;
    dst.is_keyframe = src.is_keyframe;
    dst.valid = true;

    return true;
}

inline bool RGAAccelerator::resize(const Frame& src, Frame& dst, int target_width, int target_height) {
    if (!rga_available_) {
        return false;
    }

    // 保持原格式，只进行缩放
    return convert_and_scale(src, dst, target_width, target_height, src.format);
}

inline bool RGAAccelerator::crop_and_resize(const Frame& src, Frame& dst, int crop_x, int crop_y, int crop_w, int crop_h, int target_width, int target_height) {
    if (!rga_available_) {
        return false;
    }

    // 分配目标帧缓冲区
    if (!allocate_frame_buffer(dst, target_width, target_height, src.format)) {
        LOG_E("Failed to allocate destination frame buffer");
        return false;
    }

    // 获取RGA格式
    int rga_format = v4l2_to_rga_format(src.format);
    if (rga_format == 0) {
        LOG_E("Unsupported format for RGA crop and resize");
        return false;
    }

    // 计算stride对齐
    int src_stride = calculate_stride(src.width, src.format);
    int dst_stride = calculate_stride(target_width, src.format);

    // 使用wrapbuffer_virtualaddr_t创建RGA缓冲区
    rga_buffer_t src_buf = wrapbuffer_virtualaddr_t((void*)src.data.data(),
                                                  src.width, src.height,
                                                  src_stride, src.height,
                                                  rga_format);

    rga_buffer_t dst_buf = wrapbuffer_virtualaddr_t((void*)dst.data.data(),
                                                  target_width, target_height,
                                                  dst_stride, target_height,
                                                  rga_format);

    // 设置裁剪区域
    im_rect src_rect = {crop_x, crop_y, crop_w, crop_h};
    im_rect dst_rect = {0, 0, target_width, target_height};

    LOG_D("RGA crop and resize: src %dx%d crop(%d,%d,%d,%d) -> dst %dx%d",
          src.width, src.height, crop_x, crop_y, crop_w, crop_h, target_width, target_height);

    // 执行RGA操作：裁剪 + 缩放
    int ret = improcess(src_buf, dst_buf, {}, src_rect, dst_rect, {}, 0);
    if (ret != IM_STATUS_SUCCESS) {
        LOG_E("RGA crop and resize operation failed: %d", ret);
        return false;
    }

    // 更新目标帧信息
    dst.frame_id = src.frame_id;
    dst.timestamp = src.timestamp;
    dst.source_type = src.source_type;
    dst.is_keyframe = src.is_keyframe;
    dst.valid = true;

    return true;
}

inline bool RGAAccelerator::format_convert(const Frame& src, Frame& dst, int32_t target_format) {
    if (!rga_available_) {
        return false;
    }

    // 保持原尺寸，只进行格式转换
    return convert_and_scale(src, dst, src.width, src.height, target_format);
}


inline bool RGAAccelerator::allocate_frame_buffer(Frame& frame, int width, int height, int32_t format) {
    frame.width = width;
    frame.height = height;
    frame.format = format;

    // 计算stride对齐后的宽度
    int aligned_width = align_width_to_16(width);

    // 计算所需的缓冲区大小（使用对齐后的宽度）
    size_t buffer_size = 0;
    switch (format) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
            buffer_size = aligned_width * height * 2;  // 16 bits per pixel
            break;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
            buffer_size = aligned_width * height * 3;  // 24 bits per pixel
            break;
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            buffer_size = aligned_width * height * 3 / 2;  // 12 bits per pixel
            break;
        case V4L2_PIX_FMT_RGBA32:
        case V4L2_PIX_FMT_BGRA32:
            buffer_size = aligned_width * height * 4;  // 32 bits per pixel
            break;
        default:
            LOG_E("Unsupported format for buffer allocation: 0x%08x", format);
            return false;
    }

    frame.data.resize(buffer_size);
    return true;
}

inline int RGAAccelerator::v4l2_to_rga_format(int32_t v4l2_format) {
    switch (v4l2_format) {
        case V4L2_PIX_FMT_YUYV:
            return RK_FORMAT_YUYV_422;
        case V4L2_PIX_FMT_UYVY:
            return RK_FORMAT_UYVY_422;
        case V4L2_PIX_FMT_RGB24:
            return RK_FORMAT_RGB_888;
        case V4L2_PIX_FMT_BGR24:
            return RK_FORMAT_BGR_888;
        case V4L2_PIX_FMT_NV12:
            return RK_FORMAT_YCbCr_420_SP;
        case V4L2_PIX_FMT_NV21:
            return RK_FORMAT_YCrCb_420_SP;
        case V4L2_PIX_FMT_RGBA32:
            return RK_FORMAT_RGBA_8888;
        case V4L2_PIX_FMT_BGRA32:
            return RK_FORMAT_BGRA_8888;
        case V4L2_PIX_FMT_GREY:
            return RK_FORMAT_YCbCr_400;
        default:
            LOG_W("Unsupported V4L2 format for RGA: 0x%08x", v4l2_format);
            return 0;  // 返回0表示不支持的格式
    }
}

inline int RGAAccelerator::align_width_to_16(int width) {
    // 将宽度对齐到16字节边界
    // 对于不同的像素格式，16字节对应的像素数不同
    return (width + 15) & ~15;
}

inline int RGAAccelerator::calculate_stride(int width, int32_t format) {
    // 计算stride，确保16字节对齐
    int bytes_per_pixel = 0;

    switch (format) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
            bytes_per_pixel = 2;  // 16 bits per pixel
            break;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
            bytes_per_pixel = 3;  // 24 bits per pixel
            break;
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            bytes_per_pixel = 1;  // 8 bits per pixel for Y plane
            break;
        case V4L2_PIX_FMT_RGBA32:
        case V4L2_PIX_FMT_BGRA32:
            bytes_per_pixel = 4;  // 32 bits per pixel
            break;
        default:
            bytes_per_pixel = 3;  // 默认RGB24
            break;
    }

    // 计算字节宽度并对齐到16字节
    int byte_width = width * bytes_per_pixel;
    int aligned_byte_width = align_width_to_16(byte_width);

    // 返回对齐后的像素宽度
    return aligned_byte_width / bytes_per_pixel;
}

inline void RGAAccelerator::cleanup() {
    // RGA不需要显式清理，只需要标记为不可用
    rga_available_ = false;
    LOG_D("RGA accelerator cleanup completed");
}

#endif // RGA_ACCELERATOR_H
