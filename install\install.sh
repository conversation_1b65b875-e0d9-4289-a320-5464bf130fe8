#!/bin/bash

# Video Service Installation Script
# This script installs the video service system to the target system

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Installation directories
PREFIX="${PREFIX:-/usr/local}"
BINDIR="${PREFIX}/bin"
INCLUDEDIR="${PREFIX}/include/video_service"
LIBDIR="${PREFIX}/lib"
DATADIR="${PREFIX}/share/video_service"
DOCDIR="${PREFIX}/share/doc/video_service"
SYSTEMDDIR="/lib/systemd/system"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if running as root for system installation
check_permissions() {
    if [[ "$PREFIX" == "/usr"* ]] && [[ $EUID -ne 0 ]]; then
        error "System installation requires root privileges"
        echo "Run with sudo or set PREFIX to a user directory:"
        echo "  PREFIX=\$HOME/.local $0"
        exit 1
    fi
}

# Check if build exists
check_build() {
    local build_dir="${PROJECT_ROOT}/build"
    
    if [[ ! -d "$build_dir" ]]; then
        error "Build directory not found. Please build the project first:"
        echo "  mkdir build && cd build"
        echo "  cmake .. && make"
        exit 1
    fi
    
    # Check if binaries exist
    local binaries=("video_capture" "video_converter" "ai_processor" "ai_processor")
    for binary in "${binaries[@]}"; do
        if [[ ! -f "${build_dir}/${binary}" ]]; then
            error "Binary not found: ${binary}"
            echo "Please build the project first."
            exit 1
        fi
    done
}

# Create installation directories
create_directories() {
    log "Creating installation directories..."
    
    mkdir -p "$BINDIR"
    mkdir -p "$INCLUDEDIR"
    mkdir -p "$DATADIR"
    mkdir -p "$DOCDIR"
    
    success "Directories created"
}

# Install binaries
install_binaries() {
    log "Installing binaries..."
    
    local build_dir="${PROJECT_ROOT}/build"
    local binaries=("video_capture" "video_converter" "ai_processor_main" "ai_processor")
    
    for binary in "${binaries[@]}"; do
        cp "${build_dir}/${binary}" "$BINDIR/"
        chmod 755 "${BINDIR}/${binary}"
        log "Installed: ${binary}"
    done
    
    success "Binaries installed"
}

# Install headers
install_headers() {
    log "Installing header files..."
    
    cp "${PROJECT_ROOT}/include"/*.h "$INCLUDEDIR/"
    chmod 644 "${INCLUDEDIR}"/*.h
    
    success "Headers installed"
}

# Install configuration files
install_config() {
    log "Installing configuration files..."
    
    cp -f "${PROJECT_ROOT}/config/*.json" "$DATADIR/"
    chmod 644 "${DATADIR}/*.json"
    
    success "Configuration files installed"
}

# Install scripts
install_scripts() {
    log "Installing management scripts..."
    
    mkdir -p "${DATADIR}/scripts"
    cp "${PROJECT_ROOT}/scripts"/*.sh "${DATADIR}/scripts/"
    chmod 755 "${DATADIR}/scripts"/*.sh
    
    success "Scripts installed"
}

# Install documentation
install_docs() {
    log "Installing documentation..."
    
    cp "${PROJECT_ROOT}/docs/README.md" "$DOCDIR/"
    cp "${PROJECT_ROOT}/README.md" "${DOCDIR}/README-project.md"
    chmod 644 "${DOCDIR}"/*.md
    
    success "Documentation installed"
}

# Install systemd service
install_systemd() {
    if [[ -d "$SYSTEMDDIR" ]] && [[ $EUID -eq 0 ]]; then
        log "Installing systemd service..."
        
        # Generate service file from template
        sed "s|@BINDIR@|${DATADIR}/scripts|g; s|@DATADIR@|${DATADIR}|g" \
            "${PROJECT_ROOT}/config/video_service.service.in" > "/tmp/video_service.service"
        
        cp "/tmp/video_service.service" "$SYSTEMDDIR/"
        chmod 644 "${SYSTEMDDIR}/video_service.service"
        
        # Reload systemd
        systemctl daemon-reload
        
        success "Systemd service installed"
        log "Enable with: systemctl enable video_service"
        log "Start with: systemctl start video_service"
    else
        warning "Systemd service not installed (requires root or systemd not available)"
    fi
}

# Create symlinks for easy access
create_symlinks() {
    if [[ "$PREFIX" != "/usr/local" ]]; then
        log "Creating convenience symlinks..."
        
        # Create symlinks in project root for development
        ln -sf "${DATADIR}/scripts/video_service_manager.sh" "${PROJECT_ROOT}/video_service_manager.sh"
        ln -sf "${DATADIR}/scripts/monitor_video_service.sh" "${PROJECT_ROOT}/monitor_video_service.sh"
        ln -sf "${DATADIR}/scripts/test_video_service.sh" "${PROJECT_ROOT}/test_video_service.sh"
        ln -sf "${DATADIR}/scripts/debug_video_service.sh" "${PROJECT_ROOT}/debug_video_service.sh"
        
        success "Convenience symlinks created"
    fi
}

# Setup system configuration
setup_system() {
    if [[ $EUID -eq 0 ]]; then
        log "Setting up system configuration..."
        
        # Create log and run directories
        mkdir -p /var/log/video_service
        mkdir -p /var/run/video_service
        chmod 755 /var/log/video_service
        chmod 755 /var/run/video_service
        
        # Setup logrotate
        cat > /etc/logrotate.d/video_service << 'EOF'
/var/log/video_service/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        /bin/kill -HUP `cat /var/run/video_service/*.pid 2> /dev/null` 2> /dev/null || true
    endscript
}
EOF
        
        # Setup udev rules
        cat > /etc/udev/rules.d/99-video-service.rules << 'EOF'
# Video Service udev rules
SUBSYSTEM=="video4linux", GROUP="video", MODE="0664"
SUBSYSTEM=="media", GROUP="video", MODE="0664"
KERNEL=="dma_heap", GROUP="video", MODE="0664"
EOF
        
        # Reload udev rules
        udevadm control --reload-rules
        udevadm trigger
        
        success "System configuration completed"
    else
        warning "System configuration skipped (requires root)"
    fi
}

# Print installation summary
print_summary() {
    echo
    success "Installation completed successfully!"
    echo "=================================="
    echo
    echo "Installation paths:"
    echo "  Binaries: $BINDIR"
    echo "  Headers: $INCLUDEDIR"
    echo "  Data: $DATADIR"
    echo "  Documentation: $DOCDIR"
    echo
    echo "Quick start:"
    echo "  1. Configure: edit ${DATADIR}/config.json"
    echo "  2. Test: ${DATADIR}/scripts/test_video_service.sh"
    echo "  3. Start: sudo ${DATADIR}/scripts/video_service_manager.sh start"
    echo
    if [[ -f "${SYSTEMDDIR}/video_service.service" ]]; then
        echo "Systemd service:"
        echo "  sudo systemctl enable video_service"
        echo "  sudo systemctl start video_service"
        echo
    fi
}

# Main installation function
main() {
    log "Starting Video Service installation..."
    echo "Installation prefix: $PREFIX"
    echo
    
    check_permissions
    check_build
    create_directories
    install_binaries
    install_headers
    install_config
    install_scripts
    install_docs
    install_systemd
    create_symlinks
    setup_system
    print_summary
}

# Handle command line arguments
case "${1:-install}" in
    install)
        main
        ;;
    uninstall)
        log "Uninstalling Video Service..."
        
        # Remove binaries
        rm -f "${BINDIR}/video_capture"
        rm -f "${BINDIR}/video_converter"
        rm -f "${BINDIR}/ai_processor"
        rm -f "${BINDIR}/cloud_streamer"
        
        # Remove installation directories
        rm -rf "$INCLUDEDIR"
        rm -rf "$DATADIR"
        rm -rf "$DOCDIR"
        
        # Remove systemd service
        if [[ -f "${SYSTEMDDIR}/video_service.service" ]]; then
            systemctl stop video_service 2>/dev/null || true
            systemctl disable video_service 2>/dev/null || true
            rm -f "${SYSTEMDDIR}/video_service.service"
            systemctl daemon-reload
        fi
        
        # Remove system configuration
        rm -f /etc/logrotate.d/video_service
        rm -f /etc/udev/rules.d/99-video-service.rules
        
        success "Video Service uninstalled"
        ;;
    *)
        echo "Usage: $0 [install|uninstall]"
        echo
        echo "Environment variables:"
        echo "  PREFIX - Installation prefix (default: /usr/local)"
        echo
        echo "Examples:"
        echo "  $0 install                    # Install to /usr/local"
        echo "  PREFIX=/opt/video $0 install # Install to /opt/video"
        echo "  PREFIX=\$HOME/.local $0 install # Install to user directory"
        exit 1
        ;;
esac
