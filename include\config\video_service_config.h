#ifndef VIDEO_SERVICE_CONFIG_H
#define VIDEO_SERVICE_CONFIG_H

#include "../capture/v4l2_capture_interface.h"
#include "../transport/video_transport_interface.h"
#include <json/json.h>
#include <string>
#include <fstream>
#include <sstream>

// ============================================================================
// Core Configuration Structures
// ============================================================================

// Video source types
enum VideoSource {
    V4L2_SOURCE,
    RTSP_SOURCE
};

// ============================================================================
// V4L2 and Transport Configuration (Modernized)
// ============================================================================

namespace video_config {

// Simplified V4L2 device parameters (from video_config_params.h)
struct V4L2Params {
    std::string device_path = "/dev/video0";
    uint32_t width = 1920;
    uint32_t height = 1080;
    uint32_t pixel_format = V4L2_PIX_FMT_YUYV;
    uint32_t fps = 30;
    uint32_t buffer_count = 4;
    bool use_dmabuf = false;
    int timeout_ms = 1000;
};

// Simplified transport parameters (from video_config_params.h)
struct TransportParams {
    video_transport::TransportType type = video_transport::TransportType::FASTDDS;
    std::string topic_name = "video_frames";
    int timeout_ms = 1000;
    
    // FastDDS parameters
    int domain_id = 0;
    int max_samples = 10;
    
    // DMA/SHMEM parameters
    size_t buffer_size = 1920 * 1080 * 2;
    size_t ring_buffer_size = 8;
    std::string socket_path = "/tmp/video_transport.sock";
    std::string shm_name = "video_service_shm";
};

// Complete session parameters
struct SessionParams {
    std::string name = "video_session";
    V4L2Params v4l2;
    TransportParams transport;
    
    // Basic compatibility check
    bool is_compatible() const {
        // SHMEM cannot use DMABUF
        if (transport.type == video_transport::TransportType::SHMEM && v4l2.use_dmabuf) {
            return false;
        }
        return true;
    }
    
    // Convert to existing interfaces
    V4L2Capture::V4L2DeviceConfig to_v4l2_config(video_transport::IBufferProvider* provider = nullptr) const {
        V4L2Capture::V4L2DeviceConfig config;
        config.device_path = v4l2.device_path;
        config.width = v4l2.width;
        config.height = v4l2.height;
        config.pixel_format = v4l2.pixel_format;
        config.fps = v4l2.fps;
        config.buffer_count = v4l2.buffer_count;
        config.use_dmabuf = v4l2.use_dmabuf;
        config.buffer_provider = provider;
        return config;
    }
    
    video_transport::TransportConfig to_transport_config() const {
        switch (transport.type) {
            case video_transport::TransportType::FASTDDS:
                return video_transport::TransportConfig(transport.type, transport.topic_name, 
                                                       transport.domain_id, transport.max_samples, transport.timeout_ms);
            case video_transport::TransportType::DMA:
            case video_transport::TransportType::SHMEM:
                return video_transport::TransportConfig(transport.type, transport.topic_name, 
                                                       transport.buffer_size, transport.ring_buffer_size, transport.timeout_ms);
            default:
                return video_transport::TransportConfig(transport.type, transport.topic_name);
        }
    }
};

// Preset configurations
inline SessionParams create_fastdds_session(const std::string& name = "fastdds_session") {
    SessionParams params;
    params.name = name;
    params.v4l2.use_dmabuf = false;  // MMAP mode
    params.transport.type = video_transport::TransportType::FASTDDS;
    params.transport.topic_name = "video_frames";
    return params;
}

inline SessionParams create_dma_session(const std::string& name = "dma_session") {
    SessionParams params;
    params.name = name;
    params.v4l2.use_dmabuf = true;   // DMABUF mode
    params.v4l2.buffer_count = 8;    // More buffers
    params.transport.type = video_transport::TransportType::DMA;
    params.transport.topic_name = "video_dma";
    params.transport.socket_path = "/tmp/video_dma.sock";
    return params;
}

inline SessionParams create_shmem_session(const std::string& name = "shmem_session") {
    SessionParams params;
    params.name = name;
    params.v4l2.use_dmabuf = false;  // SHMEM must use MMAP
    params.transport.type = video_transport::TransportType::SHMEM;
    params.transport.topic_name = "video_shmem";
    params.transport.socket_path = "/tmp/video_shmem.sock";
    return params;
}

} // namespace video_config

// ============================================================================
// JSON Configuration Management
// ============================================================================

namespace video_transport {

// Transport configuration loader with JSON support
class TransportConfigLoader {
public:
    // JSON file operations
    static bool load_from_file(const std::string& config_file, video_transport::TransportConfig& config);
    static bool save_to_file(const std::string& config_file, const video_transport::TransportConfig& config);
    
    // JSON string operations
    static bool load_from_json_string(const std::string& json_str, video_transport::TransportConfig& config);
    static std::string config_to_json_string(const video_transport::TransportConfig& config);
    
    // JSON object operations
    static bool load_from_json_object(const Json::Value& json_obj, video_transport::TransportConfig& config);
    static Json::Value config_to_json_object(const video_transport::TransportConfig& config);
    
    // Default configurations
    static video_transport::TransportConfig create_default_fastdds_config(const std::string& topic_name = "video_frames");
    static video_transport::TransportConfig create_default_dma_config(const std::string& socket_path = "/tmp/video_transport.sock");
    static video_transport::TransportConfig create_default_shmem_config(const std::string& socket_path = "/tmp/video_shmem.sock");
    
    // Validation
    static bool validate_config(const video_transport::TransportConfig& config, std::string& error_msg);

private:
    // JSON field constants
    static constexpr const char* FIELD_TRANSPORT_TYPE = "transport_type";
    static constexpr const char* FIELD_TOPIC_NAME = "topic_name";
    static constexpr const char* FIELD_SOCKET_PATH = "socket_path";
    static constexpr const char* FIELD_DOMAIN_ID = "domain_id";
    static constexpr const char* FIELD_MAX_SAMPLES = "max_samples";
    static constexpr const char* FIELD_BUFFER_SIZE = "buffer_size";
    static constexpr const char* FIELD_RING_BUFFER_SIZE = "ring_buffer_size";
    static constexpr const char* FIELD_TIMEOUT_MS = "timeout_ms";
    
    // Helper functions
    static bool parse_transport_type(const std::string& type_str, video_transport::TransportType& type);
    static std::string transport_type_to_string(video_transport::TransportType type);
};

// Complete video service configuration
struct VideoServiceConfig {
    struct PublisherConfig {
        bool enabled = true;
        video_transport::TransportConfig transport_config;
        std::string name;
        std::string description;
    };
    
    struct SubscriberConfig {
        bool enabled = true;
        video_transport::TransportConfig transport_config;
        std::string name;
        std::string description;
        std::vector<std::string> subscribed_topics;
    };
    
    std::vector<PublisherConfig> publishers;
    std::vector<SubscriberConfig> subscribers;
    
    // Global configuration
    struct {
        bool enable_stats_logging = true;
        int stats_logging_interval_ms = 5000;
        std::string log_level = "INFO";
        std::string log_file = "";
    } global;
};

// Video service configuration loader
class VideoServiceConfigLoader {
public:
    // JSON file operations
    static bool load_from_file(const std::string& config_file, VideoServiceConfig& config);
    static bool save_to_file(const std::string& config_file, const VideoServiceConfig& config);
    
    // Default and validation
    static VideoServiceConfig create_default_config();
    static bool validate_config(const VideoServiceConfig& config, std::string& error_msg);

private:
    static bool load_publisher_config(const Json::Value& json_obj, VideoServiceConfig::PublisherConfig& config);
    static bool load_subscriber_config(const Json::Value& json_obj, VideoServiceConfig::SubscriberConfig& config);
    static Json::Value publisher_config_to_json(const VideoServiceConfig::PublisherConfig& config);
    static Json::Value subscriber_config_to_json(const VideoServiceConfig::SubscriberConfig& config);
};

// Modern video configuration with JSON support
class VideoConfigManager {
public:
    // SessionParams JSON operations
    static bool save_session_to_file(const std::string& config_file, const video_config::SessionParams& session);
    static bool load_session_from_file(const std::string& config_file, video_config::SessionParams& session);
    static std::string session_to_json_string(const video_config::SessionParams& session);
    static bool session_from_json_string(const std::string& json_str, video_config::SessionParams& session);
    
    // Helper functions
    static Json::Value session_to_json(const video_config::SessionParams& session);
    static bool json_to_session(const Json::Value& json, video_config::SessionParams& session);
};

} // namespace video_transport

// ============================================================================
// Legacy Configuration Structures (from capture_config.h)
// ============================================================================

// Capture configuration (legacy, supports both V4L2 and RTSP)
struct CaptureConfig {
    VideoSource source_type;

    // V4L2 source config
    std::string device = "/dev/video0";

    // Video parameters - supports auto-selection and strict matching
    // Set to 0: auto-select optimal parameters
    // Specify value: strict matching, exit if not supported
    // Priority: format > fps > size (high to low)
    int width = 1280;           // Width, 0 for auto-select
    int height = 720;           // Height, 0 for auto-select
    int fps = 30;               // Frame rate, 0 for auto-select
    int format = 0;             // Pixel format, 0 for auto-select

    bool use_dma = true;

    // RTSP source config
    std::string url;
    bool use_tcp = false;
    int timeout_us = 1000000;   // RTSP timeout (microseconds)

    // Common config
    int buffer_count = 4;
    bool enable_timestamp = true;
    std::string dds_topic = "Video_Frames";

    // DDS config
    int domain_id = 0;
    int max_samples = 5;

    // Performance config
    int thread_priority = 90;
    int stats_interval_sec = 15;
};

// Stream configuration for cloud streaming
struct StreamConfig {
    enum Type { WEBRTC, RTMP } type = RTMP;
    std::string url;

    // Encoding config
    int bitrate = 2000000;      // 2Mbps
    int gop_size = 15;
    bool use_hw_encoder = true;
    int width = 1280;
    int height = 720;
    int fps = 30;
    std::string codec = "H264";
    std::string preset = "veryfast";
    std::string tune = "zerolatency";
    std::string profile = "baseline";

    // Quality control
    bool adaptive_bitrate = true;
    int min_bitrate = 500000;
    int max_bitrate = 5000000;

    // DDS config
    std::string dds_topic = "Cloud_Frames";
    int domain_id = 0;
    int max_samples = 3;

    // Performance config
    int thread_priority = 60;
    int stats_interval_sec = 10;
    bool low_latency_mode = true;
    int max_queue_size = 30;
};

// AI processing configuration
struct AIConfig {
    std::string model_path = "model.onnx";
    std::string engine_type = "onnx";      // tensorrt, onnx, openvino
    int batch_size = 1;
    bool use_gpu = true;
    float confidence_threshold = 0.5f;
    int max_detections = 100;

    // Preprocessing config
    int input_width = 640;
    int input_height = 640;
    bool normalize = true;
    std::string color_format = "RGB";

    // Postprocessing config
    float nms_threshold = 0.4f;
    float score_threshold = 0.25f;
    int max_output_boxes = 100;
    bool class_agnostic_nms = false;

    // DDS config
    std::string input_topic = "AI_Frames";
    std::string output_topic = "AI_Results";
    int domain_id = 0;
    int input_max_samples = 3;
    int output_max_samples = 3;

    // Performance config
    int thread_priority = 70;
    int process_interval_ms = 0;    // 0=process every frame
    int stats_interval_sec = 10;
};

// Video converter configuration
struct VideoConverterConfig {
    bool enable_hardware_acceleration = true;

    // Processing control
    bool enable_ai = true;
    bool enable_cloud_streaming = true;

    // DDS config
    std::string input_topic = "Video_Frames";
    std::string ai_output_topic = "AI_Frames";
    std::string cloud_output_topic = "Cloud_Frames";
    int domain_id = 0;
    int input_max_samples = 5;
    int output_max_samples = 5;

    // AI output config
    std::string ai_format = "RGB24";
    int ai_width = 640;
    int ai_height = 640;
    bool ai_enable_resize = true;
    std::string ai_resize_algorithm = "bilinear";

    // Cloud output config
    std::string cloud_format = "H264";
    int cloud_width = 1280;
    int cloud_height = 720;
    int cloud_bitrate = 2000000;
    int cloud_fps = 30;
    int cloud_gop_size = 15;
    std::string cloud_profile = "baseline";
    std::string cloud_preset = "ultrafast";
    std::string cloud_tune = "zerolatency";

    // Hardware acceleration config
    bool enable_gpu = true;
    bool enable_vaapi = true;
    bool enable_nvenc = true;
    bool enable_qsv = false;
    bool fallback_to_software = true;

    // Performance config
    int thread_priority = 80;
    int thread_pool_size = 2;
    int stats_interval_sec = 15;
    bool enable_zero_copy = true;
    int buffer_pool_size = 10;

    // Quality control
    bool enable_adaptive_quality = true;
    int min_quality = 20;
    int max_quality = 95;
    int target_fps = 30;
    float drop_frame_threshold = 0.1f;
};

// RTSP server configuration
struct RTSPServerConfig {
    std::string dds_topic = "Video_Frames";    // DDS input topic name
    std::string server_address = "0.0.0.0";   // RTSP server bind address
    int server_port = 8554;                    // RTSP server port
    std::string mount_point = "/stream";       // RTSP mount point

    // Output video parameters (keep original size, no scaling)
    int output_fps = 30;
    std::string output_codec = "H264";         // H264, H265
    int output_bitrate = 2000000;              // 2Mbps
    int gop_size = 15;                         // GOP size - set to 1 for immediate frame output

    // Performance optimization parameters
    bool use_hw_encoder_h264 = true;           // H264 hardware encoder
    bool use_hw_encoder_h265 = false;          // H265 hardware encoder
    bool zero_copy_mode = true;                // Zero-copy mode
    int buffer_size = 5;                       // DDS buffer size
    int max_clients = 10;                      // Maximum clients
    int thread_priority = 85;                  // Thread priority

    // DDS config
    int domain_id = 0;

    // Quality control
    bool adaptive_bitrate = true;              // Adaptive bitrate
    int min_bitrate = 500000;                  // Minimum bitrate 500Kbps
    int max_bitrate = 5000000;                 // Maximum bitrate 5Mbps

    // Debug options
    int gst_debug_level = 2;                   // GStreamer debug level (0-5)
    int stats_interval_sec = 10;              // Statistics output interval
};

#endif // VIDEO_SERVICE_CONFIG_H