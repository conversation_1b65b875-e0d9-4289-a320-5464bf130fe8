# 视频传输抽象层 (Video Transport Abstraction)

这是一个为Linux视频服务系统设计的抽象传输层，支持FastDDS和DMA共享内存两种传输机制的统一接口。

## 功能特性

- **统一接口**: 为不同传输机制提供一致的API
- **可配置传输**: 通过配置文件选择传输方式
- **FastDDS支持**: 基于eProsima Fast DDS的跨进程通信
- **DMA共享内存**: 基于io_uring和DMA缓冲区的高性能传输
- **统计监控**: 内置传输性能统计
- **类型安全**: C++17强类型接口设计

## 架构设计

### 核心接口

1. **IVideoPublisher**: 视频帧发布者抽象接口
2. **IVideoSubscriber**: 视频帧订阅者抽象接口
3. **VideoTransportFactory**: 传输实例工厂
4. **TransportConfig**: 传输配置结构

### 传输实现

1. **FastDDSVideoPublisher/Subscriber**: FastDDS传输适配器
2. **DMAVideoPublisher/Subscriber**: DMA共享内存传输适配器

## 快速开始

### 1. 基本使用

```cpp
#include \"video_transport_interface.h\"

using namespace video_transport;

// 创建FastDDS发布者
auto config = TransportConfigLoader::create_default_fastdds_config(\"video_frames\");
auto publisher = VideoTransportFactory::create_publisher(config);

// 发布视频帧
Frame frame = create_test_frame();
publisher->publish(frame);

// 创建订阅者
auto subscriber = VideoTransportFactory::create_subscriber(config);
Frame received_frame;
if (subscriber->receive_frame(received_frame, 1000)) {
    // 处理接收到的帧
}
```

### 2. 配置文件使用

```json
{
  \"transport_type\": \"fastdds\",
  \"topic_name\": \"main_video_frames\",
  \"domain_id\": 0,
  \"max_samples\": 5,
  \"timeout_ms\": 1000
}
```

```cpp
TransportConfig config;
TransportConfigLoader::load_from_file(\"transport_config.json\", config);
auto publisher = VideoTransportFactory::create_publisher(config);
```

### 3. DMA传输使用

```cpp
// DMA配置
auto dma_config = TransportConfigLoader::create_default_dma_config(\"/tmp/video.sock\");
auto dma_publisher = VideoTransportFactory::create_publisher(dma_config);
auto dma_subscriber = VideoTransportFactory::create_subscriber(dma_config);
```

## 配置参数

### FastDDS传输配置
- `transport_type`: \"fastdds\"
- `topic_name`: DDS主题名称
- `domain_id`: DDS域ID (0-255)
- `max_samples`: 最大样本缓存数
- `timeout_ms`: 操作超时时间

### DMA传输配置
- `transport_type`: \"dma_shared\"
- `socket_path`: Unix socket路径
- `buffer_size`: 缓冲区大小（字节）
- `ring_buffer_size`: 环形缓冲区数量
- `timeout_ms`: 操作超时时间

## 编译构建

### 依赖项
- CMake 3.16+
- C++17编译器
- FastDDS (eProsima Fast DDS)
- JsonCpp
- liburing (可选，用于DMA传输)

### 构建步骤

```bash
mkdir build
cd build
cmake -DCMAKE_BUILD_TYPE=Release ..
make -j$(nproc)
```

### 集成到现有项目

在主CMakeLists.txt中添加：
```cmake
include(cmake/VideoTransport.cmake)
target_link_libraries(your_target video_transport)
```

## API 文档

### 发布者接口 (IVideoPublisher)

```cpp
class IVideoPublisher {
public:
    virtual bool initialize(const TransportConfig& config) = 0;
    virtual bool publish(const Frame& frame) = 0;
    virtual bool has_subscribers() const = 0;
    virtual std::string get_status() const = 0;
    virtual void cleanup() = 0;
};
```

### 订阅者接口 (IVideoSubscriber)

```cpp
class IVideoSubscriber {
public:
    using FrameCallback = std::function<void(const Frame&)>;
    
    virtual bool initialize(const TransportConfig& config) = 0;
    virtual void set_frame_callback(FrameCallback callback) = 0;
    virtual bool receive_frame(Frame& frame, int timeout_ms = -1) = 0;
    virtual bool is_connected() const = 0;
    virtual std::string get_status() const = 0;
    virtual void cleanup() = 0;
};
```

### 统计信息接口 (IVideoTransportWithStats)

```cpp
struct TransportStats {
    uint64_t frames_sent;
    uint64_t frames_received;
    uint64_t bytes_sent;
    uint64_t bytes_received;
    uint64_t dropped_frames;
    double average_latency_ms;
};
```

## 示例程序

### 完整的发布订阅示例

参见 `examples/video_transport_example.cpp`，包含：
- FastDDS传输测试
- DMA传输测试
- 配置文件加载测试
- 性能统计示例

### 运行示例

```bash
# 测试FastDDS传输
./video_transport_example fastdds

# 测试DMA传输
./video_transport_example dma

# 测试配置加载
./video_transport_example config

# 运行所有测试
./video_transport_example
```

## 性能考虑

### FastDDS传输
- 适合跨网络通信
- 自动服务发现
- QoS配置灵活
- 延迟: 1-10ms

### DMA传输
- 适合本地高性能通信
- 零拷贝数据传输
- 需要内核DMA-BUF支持
- 延迟: 0.1-1ms

## 迁移指南

### 从原有FastDDS代码迁移

1. 替换直接的DDS调用：
```cpp
// 原代码
DDSWriter writer;
writer.init(topic_name, domain_id, max_samples);
writer.write(dds_frame);

// 新代码
auto config = TransportConfigLoader::create_default_fastdds_config(topic_name);
auto publisher = VideoTransportFactory::create_publisher(config);
publisher->publish(frame);
```

2. 统一帧格式：
```cpp
// 使用统一的Frame结构体，而不是DDSVideoFrame
Frame frame;
frame.frame_id = ...;
frame.data = ...;
```

### 配置文件迁移

将原有的配置参数转换为新的传输配置格式，可以使用配置迁移脚本。

## 故障排查

### 常见问题

1. **FastDDS初始化失败**
   - 检查domain_id是否冲突
   - 确认网络配置正确
   - 查看FastDDS日志

2. **DMA传输不可用**
   - 检查liburing是否安装
   - 确认内核支持DMA-BUF
   - 验证socket路径权限

3. **性能问题**
   - 调整缓冲区大小
   - 检查系统负载
   - 优化帧大小和频率

### 调试工具

```cpp
// 启用统计信息
auto stats_interface = dynamic_cast<IVideoTransportWithStats*>(publisher.get());
if (stats_interface) {
    auto stats = stats_interface->get_stats();
    std::cout << \"Frames sent: \" << stats.frames_sent << std::endl;
}

// 获取状态信息
std::cout << publisher->get_status() << std::endl;
```

## 扩展开发

### 添加新传输类型

1. 实现IVideoPublisher和IVideoSubscriber接口
2. 在TransportConfig中添加新类型
3. 在VideoTransportFactory中注册新实现
4. 添加配置加载支持

### 自定义帧格式

可以扩展Frame结构体来支持特定的元数据需求。

## 许可证

本项目遵循与主项目相同的许可证。