#include "gstreamer_encoder.h"
#include "common.h"
#include <iostream>

// 创建测试帧数据
Frame create_test_frame(int width, int height, uint32_t format) {
    Frame frame;
    frame.width = width;
    frame.height = height;
    frame.format = format;
    
    // 简单的RGB24测试数据
    size_t data_size = width * height * 3;
    frame.data.resize(data_size);
    
    // 填充简单的渐变图案
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            size_t offset = (y * width + x) * 3;
            frame.data[offset] = (x * 255) / width;     // R
            frame.data[offset + 1] = (y * 255) / height; // G
            frame.data[offset + 2] = 128;               // B
        }
    }
    
    return frame;
}

int main() {
    std::cout << "GStreamer Encoder Example" << std::endl;
    
    // 创建测试帧
    Frame input_frame = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
    std::cout << "Created test frame: " << input_frame.width << "x" << input_frame.height 
              << ", size: " << input_frame.data.size() << " bytes" << std::endl;
    
    // 测试H264编码器 - RGB24输入
    {
        std::cout << "\n=== Testing H264 Encoder (RGB24 input) ===" << std::endl;
        GStreamerEncoder h264_encoder(EncoderType::H264, 2000000, 640, 480, 30, V4L2_PIX_FMT_RGB24);

        if (h264_encoder.init()) {
            std::cout << "H264 encoder initialized successfully" << std::endl;
            std::cout << "  Input format: RGB24" << std::endl;
            std::cout << "  Resolution: " << h264_encoder.get_width() << "x" << h264_encoder.get_height() << std::endl;

            Frame encoded_frame;
            if (h264_encoder.encode(input_frame, encoded_frame)) {
                std::cout << "H264 encoding successful!" << std::endl;
                std::cout << "  Input size: " << input_frame.data.size() << " bytes" << std::endl;
                std::cout << "  Output size: " << encoded_frame.data.size() << " bytes" << std::endl;
                std::cout << "  Compression ratio: " <<
                    (float)input_frame.data.size() / encoded_frame.data.size() << ":1" << std::endl;
            } else {
                std::cout << "H264 encoding failed!" << std::endl;
            }

            h264_encoder.cleanup();
        } else {
            std::cout << "Failed to initialize H264 encoder" << std::endl;
        }
    }
    
    // 测试H265编码器
    {
        std::cout << "\n=== Testing H265 Encoder ===" << std::endl;
        GStreamerEncoder h265_encoder(EncoderType::H265, 2000000);
        
        if (h265_encoder.init()) {
            std::cout << "H265 encoder initialized successfully" << std::endl;
            
            Frame encoded_frame;
            if (h265_encoder.encode(input_frame, encoded_frame)) {
                std::cout << "H265 encoding successful!" << std::endl;
                std::cout << "  Input size: " << input_frame.data.size() << " bytes" << std::endl;
                std::cout << "  Output size: " << encoded_frame.data.size() << " bytes" << std::endl;
                std::cout << "  Compression ratio: " << 
                    (float)input_frame.data.size() / encoded_frame.data.size() << ":1" << std::endl;
            } else {
                std::cout << "H265 encoding failed!" << std::endl;
            }
            
            h265_encoder.cleanup();
        } else {
            std::cout << "Failed to initialize H265 encoder" << std::endl;
        }
    }
    
    // 测试JPEG编码器
    {
        std::cout << "\n=== Testing JPEG Encoder ===" << std::endl;
        GStreamerEncoder jpeg_encoder(EncoderType::JPEG, 85);  // 质量85
        
        if (jpeg_encoder.init()) {
            std::cout << "JPEG encoder initialized successfully" << std::endl;
            
            Frame encoded_frame;
            if (jpeg_encoder.encode(input_frame, encoded_frame)) {
                std::cout << "JPEG encoding successful!" << std::endl;
                std::cout << "  Input size: " << input_frame.data.size() << " bytes" << std::endl;
                std::cout << "  Output size: " << encoded_frame.data.size() << " bytes" << std::endl;
                std::cout << "  Compression ratio: " << 
                    (float)input_frame.data.size() / encoded_frame.data.size() << ":1" << std::endl;
            } else {
                std::cout << "JPEG encoding failed!" << std::endl;
            }
            
            jpeg_encoder.cleanup();
        } else {
            std::cout << "Failed to initialize JPEG encoder" << std::endl;
        }
    }
    
    // 测试不同输入格式
    {
        std::cout << "\n=== Testing Different Input Formats ===" << std::endl;

        // 测试YUV420输入
        {
            std::cout << "\n--- YUV420 Input Format ---" << std::endl;
            GStreamerEncoder yuv_encoder(EncoderType::H264, 2000000, 640, 480, 30, V4L2_PIX_FMT_YUV420);

            if (yuv_encoder.init()) {
                std::cout << "✓ YUV420 H264 encoder initialized" << std::endl;
                yuv_encoder.cleanup();
            } else {
                std::cout << "✗ YUV420 H264 encoder failed to initialize" << std::endl;
            }
        }

        // 测试YUYV输入
        {
            std::cout << "\n--- YUYV Input Format ---" << std::endl;
            GStreamerEncoder yuyv_encoder(EncoderType::H265, 3000000, 1280, 720, 30, V4L2_PIX_FMT_YUYV);

            if (yuyv_encoder.init()) {
                std::cout << "✓ YUYV H265 encoder initialized" << std::endl;
                yuyv_encoder.cleanup();
            } else {
                std::cout << "✗ YUYV H265 encoder failed to initialize" << std::endl;
            }
        }

        // 测试NV12输入
        {
            std::cout << "\n--- NV12 Input Format ---" << std::endl;
            GStreamerEncoder nv12_encoder(EncoderType::JPEG, 90, 640, 480, 30, V4L2_PIX_FMT_NV12);

            if (nv12_encoder.init()) {
                std::cout << "✓ NV12 JPEG encoder initialized" << std::endl;
                nv12_encoder.cleanup();
            } else {
                std::cout << "✗ NV12 JPEG encoder failed to initialize" << std::endl;
            }
        }
    }
    
    std::cout << "\nExample completed!" << std::endl;
    return 0;
}
