#ifndef UNIFIED_VIDEO_FRAME_H
#define UNIFIED_VIDEO_FRAME_H

#include <cstdint>
#include <vector>
#include <memory>
#include <atomic>

namespace video_transport {

// 前向声明
class SharedBufferManager;

// 缓冲区类型
enum class BufferType {
    UNKNOWN = 0,
    V4L2_MMAP,      // V4L2 MMAP缓冲区
    V4L2_DMABUF,    // V4L2 DMABUF缓冲区
    DMA_HEAP,       // DMA堆缓冲区
    SHARED_MEMORY,  // 共享内存缓冲区
    USER_MEMORY     // 用户内存缓冲区（如FastDDS的vector）
};

// 传输类型
enum class TransportType {
    UNKNOWN = 0,
    V4L2_DIRECT,    // 直接V4L2访问
    DMA_SHARED,     // DMA共享缓冲区
    FASTDDS,        // FastDDS网络传输
    LOCAL_COPY      // 本地内存拷贝
};

// 统一的视频帧结构 - 零拷贝设计
struct UnifiedVideoFrame {
    // === 核心元数据 ===
    uint64_t frame_id = 0;              // 帧ID
    uint64_t timestamp_us = 0;          // 时间戳（微秒）
    uint32_t width = 0;                 // 帧宽度
    uint32_t height = 0;                // 帧高度
    uint32_t stride = 0;                // 行步长
    uint32_t format = 0;                // 像素格式（V4L2格式）
    uint32_t data_size = 0;             // 有效数据大小
    bool is_keyframe = false;           // 是否关键帧
    
    // === 缓冲区管理 ===
    BufferType buffer_type = BufferType::UNKNOWN;
    TransportType transport_type = TransportType::UNKNOWN;
    uint64_t buffer_id = 0;             // 缓冲区ID（用于跨进程引用）
    
    // === 数据访问 ===
    void* data = nullptr;               // 数据指针
    size_t capacity = 0;                // 缓冲区容量
    
    // === 资源管理 ===
    union ResourceHandle {
        struct {
            int fd;                     // 文件描述符（DMA/DMABUF）
            void* mapped_addr;          // 映射地址
            size_t mapped_size;         // 映射大小
        } dma;
        
        struct {
            uint32_t index;             // V4L2缓冲区索引
            void* planes[4];            // 多平面地址
            size_t plane_sizes[4];      // 平面大小
            uint32_t num_planes;        // 平面数量
        } v4l2;
        
        struct {
            std::vector<uint8_t>* vector_data;  // FastDDS向量数据
            bool owns_vector;           // 是否拥有向量
        } fastdds;
        
        void* generic_ptr;              // 通用指针
    } resource;
    
    // === 生命周期管理 ===
    std::atomic<int> ref_count{1};      // 引用计数
    std::function<void(UnifiedVideoFrame*)> deleter; // 自定义删除器
    
    // === 构造函数 ===
    UnifiedVideoFrame() {
        memset(&resource, 0, sizeof(resource));
    }
    
    // 禁用拷贝，只允许移动
    UnifiedVideoFrame(const UnifiedVideoFrame&) = delete;
    UnifiedVideoFrame& operator=(const UnifiedVideoFrame&) = delete;
    
    UnifiedVideoFrame(UnifiedVideoFrame&& other) noexcept {
        *this = std::move(other);
    }
    
    UnifiedVideoFrame& operator=(UnifiedVideoFrame&& other) noexcept {
        if (this != &other) {
            // 移动所有字段
            frame_id = other.frame_id;
            timestamp_us = other.timestamp_us;
            width = other.width;
            height = other.height;
            stride = other.stride;
            format = other.format;
            data_size = other.data_size;
            is_keyframe = other.is_keyframe;
            buffer_type = other.buffer_type;
            transport_type = other.transport_type;
            buffer_id = other.buffer_id;
            data = other.data;
            capacity = other.capacity;
            resource = other.resource;
            ref_count.store(other.ref_count.load());
            deleter = std::move(other.deleter);
            
            // 重置源对象
            other.reset();
        }
        return *this;
    }
    
    ~UnifiedVideoFrame() {
        release();
    }
    
    // === 引用计数管理 ===
    void add_ref() {
        ref_count.fetch_add(1);
    }
    
    void release() {
        if (ref_count.fetch_sub(1) == 1) {
            // 最后一个引用，执行清理
            if (deleter) {
                deleter(this);
            } else {
                default_cleanup();
            }
        }
    }
    
    int get_ref_count() const {
        return ref_count.load();
    }
    
    // === 工厂方法 ===
    
    // 创建V4L2帧
    static std::unique_ptr<UnifiedVideoFrame> create_v4l2_frame(
        uint32_t index, void* addr, size_t size, uint32_t width, uint32_t height, 
        uint32_t format, uint64_t timestamp_us, bool is_dmabuf = false) {
        
        auto frame = std::make_unique<UnifiedVideoFrame>();
        frame->buffer_type = is_dmabuf ? BufferType::V4L2_DMABUF : BufferType::V4L2_MMAP;
        frame->transport_type = TransportType::V4L2_DIRECT;
        frame->width = width;
        frame->height = height;
        frame->format = format;
        frame->timestamp_us = timestamp_us;
        frame->data = addr;
        frame->capacity = size;
        frame->resource.v4l2.index = index;
        frame->resource.v4l2.planes[0] = addr;
        frame->resource.v4l2.plane_sizes[0] = size;
        frame->resource.v4l2.num_planes = 1;
        
        return frame;
    }
    
    // 创建DMA共享帧
    static std::unique_ptr<UnifiedVideoFrame> create_dma_frame(
        int fd, void* mapped_addr, size_t mapped_size, uint64_t buffer_id,
        uint32_t width, uint32_t height, uint32_t format, uint64_t timestamp_us) {
        
        auto frame = std::make_unique<UnifiedVideoFrame>();
        frame->buffer_type = BufferType::DMA_HEAP;
        frame->transport_type = TransportType::DMA_SHARED;
        frame->buffer_id = buffer_id;
        frame->width = width;
        frame->height = height;
        frame->format = format;
        frame->timestamp_us = timestamp_us;
        frame->data = mapped_addr;
        frame->capacity = mapped_size;
        frame->resource.dma.fd = fd;
        frame->resource.dma.mapped_addr = mapped_addr;
        frame->resource.dma.mapped_size = mapped_size;
        
        return frame;
    }
    
    // 创建FastDDS帧
    static std::unique_ptr<UnifiedVideoFrame> create_fastdds_frame(
        std::vector<uint8_t>&& data_vector, uint32_t width, uint32_t height,
        uint32_t format, uint64_t timestamp_us, uint64_t frame_id) {
        
        auto frame = std::make_unique<UnifiedVideoFrame>();
        frame->buffer_type = BufferType::USER_MEMORY;
        frame->transport_type = TransportType::FASTDDS;
        frame->frame_id = frame_id;
        frame->width = width;
        frame->height = height;
        frame->format = format;
        frame->timestamp_us = timestamp_us;
        frame->data_size = data_vector.size();
        
        // 转移向量所有权
        frame->resource.fastdds.vector_data = new std::vector<uint8_t>(std::move(data_vector));
        frame->resource.fastdds.owns_vector = true;
        frame->data = frame->resource.fastdds.vector_data->data();
        frame->capacity = frame->resource.fastdds.vector_data->size();
        
        // 设置删除器
        frame->deleter = [](UnifiedVideoFrame* f) {
            if (f->resource.fastdds.owns_vector && f->resource.fastdds.vector_data) {
                delete f->resource.fastdds.vector_data;
            }
        };
        
        return frame;
    }
    
    // === 便利方法 ===
    
    bool is_valid() const {
        return data != nullptr && capacity > 0 && width > 0 && height > 0;
    }
    
    bool is_dma_buffer() const {
        return buffer_type == BufferType::DMA_HEAP || buffer_type == BufferType::V4L2_DMABUF;
    }
    
    bool is_v4l2_buffer() const {
        return buffer_type == BufferType::V4L2_MMAP || buffer_type == BufferType::V4L2_DMABUF;
    }
    
    int get_dma_fd() const {
        if (is_dma_buffer()) {
            return resource.dma.fd;
        }
        return -1;
    }
    
    uint32_t get_v4l2_index() const {
        if (is_v4l2_buffer()) {
            return resource.v4l2.index;
        }
        return UINT32_MAX;
    }

private:
    void reset() {
        frame_id = 0;
        timestamp_us = 0;
        width = height = stride = format = data_size = 0;
        is_keyframe = false;
        buffer_type = BufferType::UNKNOWN;
        transport_type = TransportType::UNKNOWN;
        buffer_id = 0;
        data = nullptr;
        capacity = 0;
        memset(&resource, 0, sizeof(resource));
        ref_count.store(0);
        deleter = nullptr;
    }
    
    void default_cleanup() {
        // 默认清理逻辑
        switch (buffer_type) {
            case BufferType::USER_MEMORY:
                if (resource.fastdds.owns_vector && resource.fastdds.vector_data) {
                    delete resource.fastdds.vector_data;
                }
                break;
            default:
                // 其他类型由外部管理
                break;
        }
    }
};

// 智能指针类型别名
using UnifiedVideoFramePtr = std::unique_ptr<UnifiedVideoFrame>;
using SharedVideoFramePtr = std::shared_ptr<UnifiedVideoFrame>;

} // namespace video_transport

#endif // UNIFIED_VIDEO_FRAME_H
