# Video Transport Architecture V2 - Redesigned Buffer Management

## Overview

This document describes the redesigned video transport architecture that properly addresses the key requirements for DMA buffer management and cross-process video frame transmission.

## Key Design Principles

### 1. **Separation of Concerns**
- **V4L2 Operations**: Pure driver interface interactions (QBUF/DQBUF)
- **Buffer Management**: Centralized DMA buffer lifecycle management
- **Transport Layer**: Protocol-specific transmission (FastDDS vs DMA)

### 2. **Buffer Lifecycle Management**
The system enforces proper buffer borrowing and returning:

```cpp
// Producer workflow
BufferHandle handle;
publisher->get_buffer_for_frame(handle);      // Borrow buffer
// Fill buffer with data
publisher->publish_frame(handle);             // Return buffer (triggers transmission)

// Consumer workflow  
BufferHandle handle;
subscriber->receive_frame_buffer(handle);     // Borrow buffer
// Process buffer data
subscriber->return_frame_buffer(handle);      // Return buffer
```

### 3. **Dual Transport Modes**

#### **DMA Transport (Zero-Copy)**
- Transmits only `FrameMetadata` across processes
- Shares actual data via DMA file descriptors
- Uses `BufferManager` for centralized buffer management
- Optimal for high-bandwidth local communication

#### **FastDDS Transport (Full-Copy)**
- Transmits complete frame data
- Uses traditional serialization/deserialization
- Maintains compatibility with existing FastDDS infrastructure
- Better for network communication and cross-machine scenarios

## Architecture Components

### Core Interfaces

```cpp
// Buffer abstraction for different transport types
struct BufferHandle {
    void* data;                    // Data pointer
    size_t size;                   // Buffer capacity
    size_t used_size;              // Actual data size
    FrameMetadata metadata;        // Frame metadata
    TransportType transport_type;  // DMA_SHARED or FASTDDS
    
    union {
        struct {
            BufferManager::BufferSlot* slot;
            bool borrowed_from_v4l2;
        } dma;
        struct {
            std::vector<uint8_t>* frame_data;
        } fastdds;
    } transport_data;
};

// Buffer provider interface
class IBufferProvider {
    virtual BufferResult acquire_buffer_for_production(BufferHandle& handle) = 0;
    virtual BufferResult release_buffer_from_production(BufferHandle& handle) = 0;
    virtual BufferResult acquire_buffer_for_consumption(BufferHandle& handle, int timeout_ms) = 0;
    virtual BufferResult release_buffer_from_consumption(BufferHandle& handle) = 0;
};

// Enhanced transport interfaces
class IVideoPublisher {
    // New buffer management interface
    virtual BufferResult get_buffer_for_frame(BufferHandle& handle) = 0;
    virtual BufferResult publish_frame(BufferHandle& handle) = 0;
    
    // Legacy compatibility interface
    virtual bool publish_frame(const fastdds::video::Frame& frame) = 0;
};

class IVideoSubscriber {
    // New buffer management interface
    virtual BufferResult receive_frame_buffer(BufferHandle& handle, int timeout_ms) = 0;
    virtual BufferResult return_frame_buffer(BufferHandle& handle) = 0;
    
    // Legacy compatibility interface
    virtual bool receive_frame(fastdds::video::Frame& frame, int timeout_ms) = 0;
    virtual void set_frame_callback(std::function<void(const fastdds::video::Frame&)> callback) = 0;
    virtual void set_buffer_callback(std::function<void(BufferHandle&)> callback) = 0;
};
```

### V4L2 Integration

The new design properly integrates with V4L2 buffer management:

```cpp
class V4L2BufferAdapter {
    // Wrap V4L2 DMA buffer as BufferHandle (zero-copy)
    static BufferResult wrap_v4l2_buffer(const V4L2Frame& v4l2_frame, BufferHandle& handle);
    
    // Copy data from BufferHandle to V4L2 buffer
    static BufferResult copy_to_v4l2_buffer(const BufferHandle& handle, V4L2Frame& v4l2_frame);
};
```

### DMA Buffer Management

```cpp
class DMABufferProvider : public IBufferProvider {
    BufferManager* buffer_manager_;  // Central DMA buffer manager
    
    // Implements proper acquire/release semantics
    BufferResult acquire_buffer_for_production(BufferHandle& handle) override {
        auto* slot = buffer_manager_->acquire_for_production();
        // Setup handle with DMA slot information
    }
    
    BufferResult release_buffer_from_production(BufferHandle& handle) override {
        auto* slot = handle.transport_data.dma.slot;
        slot->meta = handle.metadata;  // Update metadata
        buffer_manager_->release_from_production(slot);  // Triggers broadcast
    }
};
```

## Workflow Examples

### DMA Zero-Copy Workflow

```mermaid
sequenceDiagram
    participant V4L2 as V4L2 Device
    participant Producer as DMA Producer
    participant BufferMgr as Buffer Manager
    participant Consumer as DMA Consumer
    
    Producer->>BufferMgr: acquire_for_production()
    BufferMgr-->>Producer: BufferSlot*
    Producer->>V4L2: Use buffer for V4L2 capture
    V4L2-->>Producer: Frame captured in buffer
    Producer->>BufferMgr: release_from_production(slot)
    BufferMgr->>Consumer: broadcast_buffer(metadata + fd)
    Consumer->>BufferMgr: acquire_for_consumption()
    BufferMgr-->>Consumer: BufferSlot* (same buffer)
    Consumer->>Consumer: Process frame data
    Consumer->>BufferMgr: release_from_consumption()
    BufferMgr->>BufferMgr: Buffer back to free pool
```

### FastDDS Full-Copy Workflow

```mermaid
sequenceDiagram
    participant Source as Video Source
    participant Producer as FastDDS Producer
    participant DDS as FastDDS Infrastructure
    participant Consumer as FastDDS Consumer
    
    Producer->>Producer: get_buffer_for_frame()
    Source->>Producer: Copy frame data to buffer
    Producer->>Producer: publish_frame(handle)
    Producer->>DDS: Serialize and send DDSVideoFrame
    DDS->>Consumer: Deliver DDSVideoFrame
    Consumer->>Consumer: Deserialize to Frame
    Consumer->>Consumer: Process frame data
    Consumer->>Consumer: Frame automatically freed
```

## Key Improvements

### 1. **Proper Buffer Lifecycle**
- Clear ownership semantics (borrow/return)
- Automatic resource management
- Prevention of buffer leaks

### 2. **V4L2 Integration**
- Support for both DMABUF and MMAP modes
- Proper V4L2 workflow (enqueue before streaming)
- Zero-copy when possible, fallback to copy when needed

### 3. **Cross-Process Metadata Transmission**
- Only `FrameMetadata` crosses process boundaries for DMA
- DMA file descriptors shared via Unix domain sockets
- Efficient memory usage and reduced bandwidth

### 4. **Backward Compatibility**
- Legacy FastDDS Frame interface still supported
- Gradual migration path for existing code
- Dual interface support (buffer-based and frame-based)

### 5. **Error Handling**
- Comprehensive `BufferResult` error codes
- Graceful degradation on resource exhaustion
- Proper cleanup on failures

## Usage Guidelines

### When to Use DMA Transport
- High-bandwidth local video streaming
- Low-latency requirements
- V4L2 hardware with DMABUF support
- Single-machine deployment

### When to Use FastDDS Transport
- Network-based video distribution
- Cross-machine communication
- Legacy system integration
- Complex routing and discovery requirements

### V4L2 Buffer Integration
```cpp
// For V4L2 devices with DMABUF support
V4L2DeviceConfig config;
config.use_dmabuf = true;
config.dma_buffer_manager = &my_dma_manager;  // Share manager with transport

// For V4L2 devices with MMAP only
V4L2DeviceConfig config;
config.use_dmabuf = false;  // Will use MMAP and copy to DMA buffers
```

## Performance Characteristics

| Transport | Memory Copies | Latency | Bandwidth | Cross-Process |
|-----------|---------------|---------|-----------|---------------|
| DMA       | 0-1           | Low     | High      | Metadata only |
| FastDDS   | 2-3           | Medium  | Medium    | Full frame    |

## Migration Guide

Existing code using the old interface can be gradually migrated:

1. **Phase 1**: Use new factories with old interfaces
2. **Phase 2**: Adopt buffer-based interfaces for new components
3. **Phase 3**: Replace frame-based callbacks with buffer-based ones
4. **Phase 4**: Optimize V4L2 integration with direct buffer sharing

This architecture provides a solid foundation for high-performance video transport while maintaining compatibility and providing clear upgrade paths.