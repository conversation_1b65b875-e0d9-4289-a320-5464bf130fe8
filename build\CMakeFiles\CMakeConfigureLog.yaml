
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineSystem.cmake:211 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.6.3+07e294721 for .NET Framework
      生成启动时间为 2025/6/25 18:31:34。
      
      节点 1 上的项目“C:\\Users\\<USER>\\OneDrive\\Desktop\\video_service\\build\\CMakeFiles\\3.27.6\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.36.32532\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.36.32532\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\video_service\\build\\CMakeFiles\\3.27.6\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.36.32532\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“C:\\Users\\<USER>\\OneDrive\\Desktop\\video_service\\build\\CMakeFiles\\3.27.6\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:16.35
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive/Desktop/video_service/build/CMakeFiles/3.27.6/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/video_service/build/CMakeFiles/CMakeScratch/TryCompile-jlxnxw"
      binary: "C:/Users/<USER>/OneDrive/Desktop/video_service/build/CMakeFiles/CMakeScratch/TryCompile-jlxnxw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/video_service/build/CMakeFiles/CMakeScratch/TryCompile-jlxnxw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_28ca1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.6.3+07e294721 for .NET Framework
        生成启动时间为 2025/6/25 18:31:52。
        
        节点 1 上的项目“C:\\Users\\<USER>\\OneDrive\\Desktop\\video_service\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jlxnxw\\cmTC_28ca1.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_28ca1.dir\\Debug\\”。
          正在创建目录“C:\\Users\\<USER>\\OneDrive\\Desktop\\video_service\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jlxnxw\\Debug\\”。
          正在创建目录“cmTC_28ca1.dir\\Debug\\cmTC_28ca1.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_28ca1.dir\\Debug\\cmTC_28ca1.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.36.32532\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_28ca1.dir\\Debug\\\\" /Fd"cmTC_28ca1.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.27\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.36.32534 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_28ca1.dir\\Debug\\\\" /Fd"cmTC_28ca1.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.27\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.36.32532\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\video_service\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jlxnxw\\Debug\\cmTC_28ca1.exe" /INCREMENTAL /ILK:"cmTC_28ca1.dir\\Debug\\cmTC_28ca1.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/video_service/build/CMakeFiles/CMakeScratch/TryCompile-jlxnxw/Debug/cmTC_28ca1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/video_service/build/CMakeFiles/CMakeScratch/TryCompile-jlxnxw/Debug/cmTC_28ca1.lib" /MACHINE:X64  /machine:x64 cmTC_28ca1.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_28ca1.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\video_service\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jlxnxw\\Debug\\cmTC_28ca1.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_28ca1.dir\\Debug\\cmTC_28ca1.tlog\\unsuccessfulbuild”。
          正在对“cmTC_28ca1.dir\\Debug\\cmTC_28ca1.tlog\\cmTC_28ca1.lastbuildstate”执行 Touch 任务。
        已完成生成项目“C:\\Users\\<USER>\\OneDrive\\Desktop\\video_service\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jlxnxw\\cmTC_28ca1.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:03.69
        
      exitCode: 0
...
