#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "transport/shared_buffer_subcriber.h"
#include "transport/video_transport_interface.h"

using namespace video_transport;

class SharedBufferSubscriberTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试环境
    }

    void TearDown() override {
        // 清理测试环境
    }
};

// 测试IoUringConsumer的基本功能
TEST_F(SharedBufferSubscriberTest, BasicInitialization) {
    IoUringConsumer consumer;
    
    // 测试初始状态
    EXPECT_FALSE(consumer.is_connected());
    
    // 测试配置
    TransportConfig config(TransportType::DMA, "/tmp/test_socket");
    
    // 注意：这个测试需要实际的服务器才能成功连接
    // 在单元测试中，我们主要测试接口的正确性
    EXPECT_NO_THROW({
        auto stats = consumer.get_stats();
        auto status = consumer.get_status();
        consumer.reset_stats();
    });
}

// 测试消息池功能
TEST_F(SharedBufferSubscriberTest, MessagePoolFunctionality) {
    MessagePool pool(4);
    
    // 测试消息获取和释放
    auto* msg1 = pool.acquire();
    ASSERT_NE(msg1, nullptr);
    
    auto* msg2 = pool.acquire();
    ASSERT_NE(msg2, nullptr);
    EXPECT_NE(msg1, msg2);
    
    // 释放消息
    pool.release(msg1);
    pool.release(msg2);
    
    // 再次获取应该重用之前的消息
    auto* msg3 = pool.acquire();
    EXPECT_TRUE(msg3 == msg1 || msg3 == msg2);
}

// 测试同步缓冲区结构
TEST_F(SharedBufferSubscriberTest, SyncBufferStructure) {
    FrameMetadata meta;
    meta.buffer_id = 123;
    meta.width = 1920;
    meta.height = 1080;
    meta.format = 0x56595559; // YUYV
    meta.data_size = 1920 * 1080 * 2;
    meta.type = BufferType::DMA;
    
    SyncBuffer sync_buffer(meta, 42);
    
    EXPECT_EQ(sync_buffer.metadata.buffer_id, 123);
    EXPECT_EQ(sync_buffer.metadata.width, 1920);
    EXPECT_EQ(sync_buffer.metadata.height, 1080);
    EXPECT_EQ(sync_buffer.fd, 42);
}

// 测试映射缓冲区信息结构
TEST_F(SharedBufferSubscriberTest, MappedBufferInfo) {
    MappedBufferInfo info;
    info.addr = reinterpret_cast<void*>(0x12345678);
    info.size = 1024;
    info.fd = 10;
    
    EXPECT_EQ(info.addr, reinterpret_cast<void*>(0x12345678));
    EXPECT_EQ(info.size, 1024);
    EXPECT_EQ(info.fd, 10);
}

// 测试操作数据结构
TEST_F(SharedBufferSubscriberTest, OperationDataStructure) {
    MessagePool pool;
    auto* msg = pool.acquire();
    
    OperationData recv_data(OperationData::RECV, msg);
    EXPECT_EQ(recv_data.type, OperationData::RECV);
    EXPECT_EQ(recv_data.msg, msg);
    
    OperationData send_data(OperationData::SEND, nullptr);
    EXPECT_EQ(send_data.type, OperationData::SEND);
    EXPECT_EQ(send_data.msg, nullptr);
    
    pool.release(msg);
}

// 测试释放消息结构
TEST_F(SharedBufferSubscriberTest, ReleaseMessageStructure) {
    ReleaseMessage msg;
    msg.buffer_id = 456;
    msg.msg_type = 1;
    
    EXPECT_EQ(msg.buffer_id, 456);
    EXPECT_EQ(msg.msg_type, 1);
}

// 测试回调设置
TEST_F(SharedBufferSubscriberTest, CallbackSetting) {
    IoUringConsumer consumer;
    
    bool frame_callback_called = false;
    bool buffer_callback_called = false;
    
    // 设置FastDDS帧回调
    consumer.set_frame_callback([&](const fastdds::video::Frame& frame) {
        frame_callback_called = true;
    });
    
    // 设置缓冲区回调
    consumer.set_buffer_callback([&](BufferHandle& handle) {
        buffer_callback_called = true;
    });
    
    // 回调设置不应该抛出异常
    EXPECT_NO_THROW({
        consumer.set_frame_callback(nullptr);
        consumer.set_buffer_callback(nullptr);
    });
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
