# Video Converter 使用指南

## 快速开始

### 1. 编译

```bash
cd video_service
mkdir build && cd build
cmake ..
cmake --build . --target video_converter
```

### 2. 配置

编辑配置文件 `config/video_converter.json`:

```json
{
  "video_converter": {
    "enable_hardware_acceleration": true,
    "ai_output": {
      "width": 640,
      "height": 640,
      "format": "RGB24"
    },
    "cloud_output": {
      "format": "H264",
      "bitrate": 2000000,
      "fps": 30
    }
  }
}
```

### 3. 运行

```bash
./video_converter --config config/video_converter.json
```

## 详细配置

### DDS配置

```json
"dds": {
  "input_topic": "Video_Frames",
  "ai_output_topic": "AI_Frames", 
  "cloud_output_topic": "Cloud_Frames",
  "domain_id": 0,
  "input_max_samples": 5,
  "output_max_samples": 5
}
```

### AI输出配置

```json
"ai_output": {
  "format": "RGB24",           // 输出格式: RGB24
  "width": 640,                // 输出宽度
  "height": 640,               // 输出高度  
  "enable_resize": true,       // 启用缩放
  "resize_algorithm": "bilinear" // 缩放算法
}
```

### Cloud输出配置

```json
"cloud_output": {
  "format": "H264",            // 输出格式: H264/H265
  "width": 1280,               // 输出宽度(可选)
  "height": 720,               // 输出高度(可选)
  "bitrate": 2000000,          // 码率(bps)
  "fps": 30,                   // 帧率
  "gop_size": 15,              // GOP大小
  "profile": "baseline",       // H264 profile
  "preset": "ultrafast",       // 编码预设
  "tune": "zerolatency"        // 编码调优
}
```

### 硬件加速配置

```json
"hardware_acceleration": {
  "enable_gpu": true,          // 启用GPU加速
  "enable_vaapi": true,        // 启用VAAPI
  "enable_nvenc": true,        // 启用NVENC
  "enable_qsv": false,         // 启用Intel QSV
  "fallback_to_software": true // 硬件失败时回退到软件
}
```

## 命令行参数

```bash
./video_converter [OPTIONS]

Options:
  -c, --config FILE     配置文件路径 (默认: config/video_converter.json)
  --input-topic TOPIC   输入DDS topic (默认: Video_Frames)
  --ai-topic TOPIC      AI输出DDS topic (默认: AI_Frames)  
  --cloud-topic TOPIC   Cloud输出DDS topic (默认: Cloud_Frames)
  -v, --verbose         启用详细日志
  --help                显示帮助信息
```

## 输入格式支持

| 格式 | V4L2常量 | 处理方式 |
|------|----------|----------|
| YUYV | V4L2_PIX_FMT_YUYV | RGA转换+缩放 |
| UYVY | V4L2_PIX_FMT_UYVY | RGA转换+缩放 |
| RGB24 | V4L2_PIX_FMT_RGB24 | RGA缩放 |
| BGR24 | V4L2_PIX_FMT_BGR24 | RGA转换+缩放 |
| MJPEG | V4L2_PIX_FMT_MJPEG | MPP解码+RGA缩放 |
| H264 | V4L2_PIX_FMT_H264 | GStreamer解码+RGA缩放 |
| H265 | V4L2_PIX_FMT_H265 | GStreamer解码+RGA缩放 |

## 处理流程图

```
输入帧 (DDS Video_Frames)
    |
    v
格式检测
    |
    +-- YUV/RGB --> RGA转换+缩放 --> AI输出 (640x640 RGB888)
    |                    |
    |                    v
    |               GStreamer编码 --> Cloud输出 (H264 NALU)
    |
    +-- MJPEG --> MPP解码 --> RGA缩放 --> AI输出
    |                    |
    |                    v  
    |               GStreamer编码 --> Cloud输出
    |
    +-- H264/H265 --> GStreamer解码 --> RGA缩放 --> AI输出
                           |
                           v
                      直接传递 --> Cloud输出
```

## 性能调优

### 1. 硬件加速优化

```json
"performance": {
  "thread_priority": 80,       // 线程优先级
  "cpu_affinity": [2, 3],      // CPU亲和性
  "thread_pool_size": 2,       // 线程池大小
  "enable_zero_copy": true,    // 零拷贝模式
  "buffer_pool_size": 10       // 缓冲池大小
}
```

### 2. 质量控制

```json
"quality_control": {
  "enable_adaptive_quality": true,  // 自适应质量
  "min_quality": 20,               // 最小质量
  "max_quality": 95,               // 最大质量
  "target_fps": 30,                // 目标帧率
  "drop_frame_threshold": 0.1      // 丢帧阈值
}
```

## 监控和调试

### 1. 日志配置

```json
"logging": {
  "level": "INFO",                    // 日志级别: DEBUG/INFO/WARN/ERROR
  "enable_conversion_stats": true,    // 启用转换统计
  "enable_performance_stats": true,   // 启用性能统计
  "log_frame_details": false         // 记录帧详细信息
}
```

### 2. 统计信息

程序运行时会定期输出统计信息:

```
Stats - Processed: 1500, Dropped: 2, AI: 1498, Cloud: 1498, CPU: 45.2%
```

### 3. 调试模式

```bash
# 启用详细日志
./video_converter --verbose

# 设置GStreamer调试级别
export GST_DEBUG=3
./video_converter
```

## 故障排除

### 常见问题

1. **RGA初始化失败**
   ```
   错误: Failed to initialize RGA
   解决: 检查RGA库安装和权限
   ```

2. **MPP解码失败**
   ```
   错误: MPP MJPEG decode failed
   解决: 检查MPP驱动和库安装
   ```

3. **GStreamer管道错误**
   ```
   错误: Failed to create H264 encode pipeline
   解决: 检查GStreamer插件安装
   ```

4. **DDS连接失败**
   ```
   错误: Failed to initialize input reader
   解决: 检查FastDDS配置和网络
   ```

### 诊断命令

```bash
# 检查硬件支持
lsmod | grep rga
lsmod | grep mpp

# 检查GStreamer插件
gst-inspect-1.0 mpph264enc
gst-inspect-1.0 mppjpegdec

# 检查DDS通信
fastdds discovery -i 0
```

## 示例用法

### 基本用法

```bash
# 使用默认配置
./video_converter

# 指定配置文件
./video_converter --config my_config.json

# 自定义topic名称
./video_converter \
  --input-topic Camera_Frames \
  --ai-topic AI_Processing \
  --cloud-topic Stream_Output
```

### 高性能配置

```bash
# 启用所有硬件加速
./video_converter \
  --config config/high_performance.json \
  --verbose
```

### 调试配置

```bash
# 启用详细日志和调试
export GST_DEBUG=4
./video_converter \
  --config config/debug.json \
  --verbose
```
