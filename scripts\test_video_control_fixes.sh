#!/bin/bash

# Test script to verify the fixes for video_control test issues
# 1. SD card status detection waits for status_reporter_loop
# 2. UDP socket test connects to service instead of binding same port

set -e

echo "=== Video Control Test Fixes Verification ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}[OK]${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[WARN]${NC} $message"
    else
        echo -e "${RED}[ERROR]${NC} $message"
    fi
}

# Check if we're in the project root
if [ ! -f "CMakeLists.txt" ]; then
    print_status "ERROR" "Please run this script from the project root directory"
    exit 1
fi

print_status "OK" "Found project root directory"

# Create test directories
TEST_DIRS=(
    "/mnt/sdcard"
    "/mnt/sdcard/photos" 
    "/mnt/sdcard/videos"
)

echo -e "\n--- Creating Test Directories ---"
for dir in "${TEST_DIRS[@]}"; do
    if sudo mkdir -p "$dir" 2>/dev/null; then
        print_status "OK" "Created directory: $dir"
    else
        print_status "WARN" "Failed to create directory: $dir (may already exist)"
    fi
done

# Set permissions
sudo chmod 777 /mnt/sdcard /mnt/sdcard/photos /mnt/sdcard/videos 2>/dev/null || true

# Build the test
echo -e "\n--- Building Test ---"
if [ ! -d "build" ]; then
    mkdir build
fi

cd build

if cmake .. -DCMAKE_BUILD_TYPE=Debug; then
    print_status "OK" "CMake configuration successful"
else
    print_status "ERROR" "CMake configuration failed"
    exit 1
fi

if make test_video_control -j$(nproc); then
    print_status "OK" "Test compilation successful"
else
    print_status "ERROR" "Test compilation failed"
    exit 1
fi

# Check if test executable exists
if [ -f "test_video_control" ]; then
    print_status "OK" "Test executable created"
else
    print_status "ERROR" "Test executable not found"
    exit 1
fi

# Run the test
echo -e "\n--- Running Video Control Tests ---"
echo "Testing fixes:"
echo "1. SD card status detection waits for status_reporter_loop"
echo "2. UDP socket test connects to service instead of binding same port"
echo ""

# Run test with timeout to prevent hanging
timeout 120 ./test_video_control > test_output.log 2>&1 &
TEST_PID=$!

# Monitor test progress
echo "Test running (PID: $TEST_PID), monitoring progress..."
sleep 5

# Check if test is still running
if kill -0 $TEST_PID 2>/dev/null; then
    print_status "OK" "Test is running"
    
    # Wait for test to complete
    wait $TEST_PID
    TEST_RESULT=$?
else
    print_status "ERROR" "Test process died early"
    TEST_RESULT=1
fi

# Analyze test output
echo -e "\n--- Test Results Analysis ---"

if [ -f "test_output.log" ]; then
    # Check for specific test results
    if grep -q "SD Card Status Detection" test_output.log; then
        if grep -q "Service started, waiting for status_reporter_loop" test_output.log; then
            print_status "OK" "SD card test waits for status_reporter_loop"
        else
            print_status "WARN" "SD card test may not be waiting properly"
        fi
        
        if grep -q "✓.*SD card status detection working" test_output.log; then
            print_status "OK" "SD card status detection working"
        else
            print_status "WARN" "SD card status detection may have issues"
        fi
    fi
    
    if grep -q "UDP Socket Communication" test_output.log; then
        if grep -q "Service started, waiting for UDP socket" test_output.log; then
            print_status "OK" "UDP test starts service first"
        else
            print_status "WARN" "UDP test may not be starting service properly"
        fi
        
        if grep -q "Successfully sent.*bytes to VideoControlService" test_output.log; then
            print_status "OK" "UDP communication with service successful"
        else
            print_status "WARN" "UDP communication may have issues"
        fi
    fi
    
    # Check overall test result
    if grep -q "All dual video tests PASSED" test_output.log; then
        print_status "OK" "All tests passed"
    elif grep -q "Some dual video tests FAILED" test_output.log; then
        print_status "WARN" "Some tests failed"
    fi
    
    # Show test summary
    echo -e "\n--- Test Output Summary ---"
    grep -E "(✓|✗|Test:|---)" test_output.log | tail -20
    
else
    print_status "ERROR" "Test output log not found"
    TEST_RESULT=1
fi

# Cleanup
cd ..

# Final result
echo -e "\n--- Final Results ---"
if [ $TEST_RESULT -eq 0 ]; then
    print_status "OK" "🎉 Video Control Test Fixes Verification PASSED!"
    echo ""
    echo "Fixes verified:"
    echo "✓ SD card status detection waits for status_reporter_loop to run"
    echo "✓ UDP socket test connects to VideoControlService instead of binding same port"
    echo "✓ Service lifecycle properly managed in tests"
    echo ""
    echo "The test now properly:"
    echo "- Starts the service before checking SD card status"
    echo "- Waits for status_reporter_loop to update SD card information"
    echo "- Creates client socket to communicate with service UDP port"
    echo "- Manages service start/stop lifecycle correctly"
else
    print_status "ERROR" "❌ Video Control Test Fixes Verification FAILED!"
    echo ""
    echo "Check the test output for specific issues:"
    echo "- build/test_output.log"
    echo ""
    echo "Common issues to check:"
    echo "- DDS dependencies missing"
    echo "- GStreamer plugins not available"
    echo "- Insufficient permissions for /mnt/sdcard"
    echo "- Network/UDP socket issues"
fi

echo ""
echo "Test log location: build/test_output.log"
echo "To run test manually: cd build && ./test_video_control"

exit $TEST_RESULT
