// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file DDSVideoFrameTypeObjectSupport.cxx
 * Source file containing the implementation to register the TypeObject representation of the described types in the IDL file
 *
 * This file was generated by the tool fastddsgen.
 */

#include "DDSVideoFrameTypeObjectSupport.hpp"

#include <mutex>
#include <string>

#include <fastcdr/xcdr/external.hpp>
#include <fastcdr/xcdr/optional.hpp>
#include <fastdds/dds/domain/DomainParticipantFactory.hpp>
#include <fastdds/dds/log/Log.hpp>
#include <fastdds/dds/xtypes/common.hpp>
#include <fastdds/dds/xtypes/type_representation/ITypeObjectRegistry.hpp>
#include <fastdds/dds/xtypes/type_representation/TypeObject.hpp>
#include <fastdds/dds/xtypes/type_representation/TypeObjectUtils.hpp>

#include "DDSVideoFrame.hpp"


using namespace eprosima::fastdds::dds::xtypes;

// TypeIdentifier is returned by reference: dependent structures/unions are registered in this same method
void register_DDSVideoFrame_type_identifier(
        TypeIdentifierPair& type_ids_DDSVideoFrame)
{

    ReturnCode_t return_code_DDSVideoFrame {eprosima::fastdds::dds::RETCODE_OK};
    return_code_DDSVideoFrame =
        eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
        "DDSVideoFrame", type_ids_DDSVideoFrame);
    if (eprosima::fastdds::dds::RETCODE_OK != return_code_DDSVideoFrame)
    {
        StructTypeFlag struct_flags_DDSVideoFrame = TypeObjectUtils::build_struct_type_flag(eprosima::fastdds::dds::xtypes::ExtensibilityKind::APPENDABLE,
                false, false);
        QualifiedTypeName type_name_DDSVideoFrame = "DDSVideoFrame";
        eprosima::fastcdr::optional<AppliedBuiltinTypeAnnotations> type_ann_builtin_DDSVideoFrame;
        eprosima::fastcdr::optional<AppliedAnnotationSeq> ann_custom_DDSVideoFrame;
        CompleteTypeDetail detail_DDSVideoFrame = TypeObjectUtils::build_complete_type_detail(type_ann_builtin_DDSVideoFrame, ann_custom_DDSVideoFrame, type_name_DDSVideoFrame.to_string());
        CompleteStructHeader header_DDSVideoFrame;
        header_DDSVideoFrame = TypeObjectUtils::build_complete_struct_header(TypeIdentifier(), detail_DDSVideoFrame);
        CompleteStructMemberSeq member_seq_DDSVideoFrame;
        {
            TypeIdentifierPair type_ids_frame_id;
            ReturnCode_t return_code_frame_id {eprosima::fastdds::dds::RETCODE_OK};
            return_code_frame_id =
                eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
                "_uint64_t", type_ids_frame_id);

            if (eprosima::fastdds::dds::RETCODE_OK != return_code_frame_id)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                        "frame_id Structure member TypeIdentifier unknown to TypeObjectRegistry.");
                return;
            }
            StructMemberFlag member_flags_frame_id = TypeObjectUtils::build_struct_member_flag(eprosima::fastdds::dds::xtypes::TryConstructFailAction::DISCARD,
                    false, false, false, false);
            MemberId member_id_frame_id = 0x00000000;
            bool common_frame_id_ec {false};
            CommonStructMember common_frame_id {TypeObjectUtils::build_common_struct_member(member_id_frame_id, member_flags_frame_id, TypeObjectUtils::retrieve_complete_type_identifier(type_ids_frame_id, common_frame_id_ec))};
            if (!common_frame_id_ec)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION, "Structure frame_id member TypeIdentifier inconsistent.");
                return;
            }
            MemberName name_frame_id = "frame_id";
            eprosima::fastcdr::optional<AppliedBuiltinMemberAnnotations> member_ann_builtin_frame_id;
            ann_custom_DDSVideoFrame.reset();
            CompleteMemberDetail detail_frame_id = TypeObjectUtils::build_complete_member_detail(name_frame_id, member_ann_builtin_frame_id, ann_custom_DDSVideoFrame);
            CompleteStructMember member_frame_id = TypeObjectUtils::build_complete_struct_member(common_frame_id, detail_frame_id);
            TypeObjectUtils::add_complete_struct_member(member_seq_DDSVideoFrame, member_frame_id);
        }
        {
            TypeIdentifierPair type_ids_timestamp;
            ReturnCode_t return_code_timestamp {eprosima::fastdds::dds::RETCODE_OK};
            return_code_timestamp =
                eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
                "_uint64_t", type_ids_timestamp);

            if (eprosima::fastdds::dds::RETCODE_OK != return_code_timestamp)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                        "timestamp Structure member TypeIdentifier unknown to TypeObjectRegistry.");
                return;
            }
            StructMemberFlag member_flags_timestamp = TypeObjectUtils::build_struct_member_flag(eprosima::fastdds::dds::xtypes::TryConstructFailAction::DISCARD,
                    false, false, false, false);
            MemberId member_id_timestamp = 0x00000001;
            bool common_timestamp_ec {false};
            CommonStructMember common_timestamp {TypeObjectUtils::build_common_struct_member(member_id_timestamp, member_flags_timestamp, TypeObjectUtils::retrieve_complete_type_identifier(type_ids_timestamp, common_timestamp_ec))};
            if (!common_timestamp_ec)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION, "Structure timestamp member TypeIdentifier inconsistent.");
                return;
            }
            MemberName name_timestamp = "timestamp";
            eprosima::fastcdr::optional<AppliedBuiltinMemberAnnotations> member_ann_builtin_timestamp;
            ann_custom_DDSVideoFrame.reset();
            CompleteMemberDetail detail_timestamp = TypeObjectUtils::build_complete_member_detail(name_timestamp, member_ann_builtin_timestamp, ann_custom_DDSVideoFrame);
            CompleteStructMember member_timestamp = TypeObjectUtils::build_complete_struct_member(common_timestamp, detail_timestamp);
            TypeObjectUtils::add_complete_struct_member(member_seq_DDSVideoFrame, member_timestamp);
        }
        {
            TypeIdentifierPair type_ids_width;
            ReturnCode_t return_code_width {eprosima::fastdds::dds::RETCODE_OK};
            return_code_width =
                eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
                "_uint16_t", type_ids_width);

            if (eprosima::fastdds::dds::RETCODE_OK != return_code_width)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                        "width Structure member TypeIdentifier unknown to TypeObjectRegistry.");
                return;
            }
            StructMemberFlag member_flags_width = TypeObjectUtils::build_struct_member_flag(eprosima::fastdds::dds::xtypes::TryConstructFailAction::DISCARD,
                    false, false, false, false);
            MemberId member_id_width = 0x00000002;
            bool common_width_ec {false};
            CommonStructMember common_width {TypeObjectUtils::build_common_struct_member(member_id_width, member_flags_width, TypeObjectUtils::retrieve_complete_type_identifier(type_ids_width, common_width_ec))};
            if (!common_width_ec)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION, "Structure width member TypeIdentifier inconsistent.");
                return;
            }
            MemberName name_width = "width";
            eprosima::fastcdr::optional<AppliedBuiltinMemberAnnotations> member_ann_builtin_width;
            ann_custom_DDSVideoFrame.reset();
            CompleteMemberDetail detail_width = TypeObjectUtils::build_complete_member_detail(name_width, member_ann_builtin_width, ann_custom_DDSVideoFrame);
            CompleteStructMember member_width = TypeObjectUtils::build_complete_struct_member(common_width, detail_width);
            TypeObjectUtils::add_complete_struct_member(member_seq_DDSVideoFrame, member_width);
        }
        {
            TypeIdentifierPair type_ids_height;
            ReturnCode_t return_code_height {eprosima::fastdds::dds::RETCODE_OK};
            return_code_height =
                eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
                "_uint16_t", type_ids_height);

            if (eprosima::fastdds::dds::RETCODE_OK != return_code_height)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                        "height Structure member TypeIdentifier unknown to TypeObjectRegistry.");
                return;
            }
            StructMemberFlag member_flags_height = TypeObjectUtils::build_struct_member_flag(eprosima::fastdds::dds::xtypes::TryConstructFailAction::DISCARD,
                    false, false, false, false);
            MemberId member_id_height = 0x00000003;
            bool common_height_ec {false};
            CommonStructMember common_height {TypeObjectUtils::build_common_struct_member(member_id_height, member_flags_height, TypeObjectUtils::retrieve_complete_type_identifier(type_ids_height, common_height_ec))};
            if (!common_height_ec)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION, "Structure height member TypeIdentifier inconsistent.");
                return;
            }
            MemberName name_height = "height";
            eprosima::fastcdr::optional<AppliedBuiltinMemberAnnotations> member_ann_builtin_height;
            ann_custom_DDSVideoFrame.reset();
            CompleteMemberDetail detail_height = TypeObjectUtils::build_complete_member_detail(name_height, member_ann_builtin_height, ann_custom_DDSVideoFrame);
            CompleteStructMember member_height = TypeObjectUtils::build_complete_struct_member(common_height, detail_height);
            TypeObjectUtils::add_complete_struct_member(member_seq_DDSVideoFrame, member_height);
        }
        {
            TypeIdentifierPair type_ids_format;
            ReturnCode_t return_code_format {eprosima::fastdds::dds::RETCODE_OK};
            return_code_format =
                eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
                "_int32_t", type_ids_format);

            if (eprosima::fastdds::dds::RETCODE_OK != return_code_format)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                        "format Structure member TypeIdentifier unknown to TypeObjectRegistry.");
                return;
            }
            StructMemberFlag member_flags_format = TypeObjectUtils::build_struct_member_flag(eprosima::fastdds::dds::xtypes::TryConstructFailAction::DISCARD,
                    false, false, false, false);
            MemberId member_id_format = 0x00000004;
            bool common_format_ec {false};
            CommonStructMember common_format {TypeObjectUtils::build_common_struct_member(member_id_format, member_flags_format, TypeObjectUtils::retrieve_complete_type_identifier(type_ids_format, common_format_ec))};
            if (!common_format_ec)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION, "Structure format member TypeIdentifier inconsistent.");
                return;
            }
            MemberName name_format = "format";
            eprosima::fastcdr::optional<AppliedBuiltinMemberAnnotations> member_ann_builtin_format;
            ann_custom_DDSVideoFrame.reset();
            CompleteMemberDetail detail_format = TypeObjectUtils::build_complete_member_detail(name_format, member_ann_builtin_format, ann_custom_DDSVideoFrame);
            CompleteStructMember member_format = TypeObjectUtils::build_complete_struct_member(common_format, detail_format);
            TypeObjectUtils::add_complete_struct_member(member_seq_DDSVideoFrame, member_format);
        }
        {
            TypeIdentifierPair type_ids_is_keyframe;
            ReturnCode_t return_code_is_keyframe {eprosima::fastdds::dds::RETCODE_OK};
            return_code_is_keyframe =
                eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
                "_bool", type_ids_is_keyframe);

            if (eprosima::fastdds::dds::RETCODE_OK != return_code_is_keyframe)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                        "is_keyframe Structure member TypeIdentifier unknown to TypeObjectRegistry.");
                return;
            }
            StructMemberFlag member_flags_is_keyframe = TypeObjectUtils::build_struct_member_flag(eprosima::fastdds::dds::xtypes::TryConstructFailAction::DISCARD,
                    false, false, false, false);
            MemberId member_id_is_keyframe = 0x00000005;
            bool common_is_keyframe_ec {false};
            CommonStructMember common_is_keyframe {TypeObjectUtils::build_common_struct_member(member_id_is_keyframe, member_flags_is_keyframe, TypeObjectUtils::retrieve_complete_type_identifier(type_ids_is_keyframe, common_is_keyframe_ec))};
            if (!common_is_keyframe_ec)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION, "Structure is_keyframe member TypeIdentifier inconsistent.");
                return;
            }
            MemberName name_is_keyframe = "is_keyframe";
            eprosima::fastcdr::optional<AppliedBuiltinMemberAnnotations> member_ann_builtin_is_keyframe;
            ann_custom_DDSVideoFrame.reset();
            CompleteMemberDetail detail_is_keyframe = TypeObjectUtils::build_complete_member_detail(name_is_keyframe, member_ann_builtin_is_keyframe, ann_custom_DDSVideoFrame);
            CompleteStructMember member_is_keyframe = TypeObjectUtils::build_complete_struct_member(common_is_keyframe, detail_is_keyframe);
            TypeObjectUtils::add_complete_struct_member(member_seq_DDSVideoFrame, member_is_keyframe);
        }
        {
            TypeIdentifierPair type_ids_data_length;
            ReturnCode_t return_code_data_length {eprosima::fastdds::dds::RETCODE_OK};
            return_code_data_length =
                eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
                "_uint32_t", type_ids_data_length);

            if (eprosima::fastdds::dds::RETCODE_OK != return_code_data_length)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                        "data_length Structure member TypeIdentifier unknown to TypeObjectRegistry.");
                return;
            }
            StructMemberFlag member_flags_data_length = TypeObjectUtils::build_struct_member_flag(eprosima::fastdds::dds::xtypes::TryConstructFailAction::DISCARD,
                    false, false, false, false);
            MemberId member_id_data_length = 0x00000006;
            bool common_data_length_ec {false};
            CommonStructMember common_data_length {TypeObjectUtils::build_common_struct_member(member_id_data_length, member_flags_data_length, TypeObjectUtils::retrieve_complete_type_identifier(type_ids_data_length, common_data_length_ec))};
            if (!common_data_length_ec)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION, "Structure data_length member TypeIdentifier inconsistent.");
                return;
            }
            MemberName name_data_length = "data_length";
            eprosima::fastcdr::optional<AppliedBuiltinMemberAnnotations> member_ann_builtin_data_length;
            ann_custom_DDSVideoFrame.reset();
            CompleteMemberDetail detail_data_length = TypeObjectUtils::build_complete_member_detail(name_data_length, member_ann_builtin_data_length, ann_custom_DDSVideoFrame);
            CompleteStructMember member_data_length = TypeObjectUtils::build_complete_struct_member(common_data_length, detail_data_length);
            TypeObjectUtils::add_complete_struct_member(member_seq_DDSVideoFrame, member_data_length);
        }
        {
            TypeIdentifierPair type_ids_data;
            ReturnCode_t return_code_data {eprosima::fastdds::dds::RETCODE_OK};
            return_code_data =
                eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
                "anonymous_sequence_byte_unbounded", type_ids_data);

            if (eprosima::fastdds::dds::RETCODE_OK != return_code_data)
            {
                return_code_data =
                    eprosima::fastdds::dds::DomainParticipantFactory::get_instance()->type_object_registry().get_type_identifiers(
                    "_byte", type_ids_data);

                if (eprosima::fastdds::dds::RETCODE_OK != return_code_data)
                {
                    EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                            "Sequence element TypeIdentifier unknown to TypeObjectRegistry.");
                    return;
                }
                bool element_identifier_anonymous_sequence_byte_unbounded_ec {false};
                TypeIdentifier* element_identifier_anonymous_sequence_byte_unbounded {new TypeIdentifier(TypeObjectUtils::retrieve_complete_type_identifier(type_ids_data, element_identifier_anonymous_sequence_byte_unbounded_ec))};
                if (!element_identifier_anonymous_sequence_byte_unbounded_ec)
                {
                    EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION, "Sequence element TypeIdentifier inconsistent.");
                    return;
                }
                EquivalenceKind equiv_kind_anonymous_sequence_byte_unbounded = EK_COMPLETE;
                if (TK_NONE == type_ids_data.type_identifier2()._d())
                {
                    equiv_kind_anonymous_sequence_byte_unbounded = EK_BOTH;
                }
                CollectionElementFlag element_flags_anonymous_sequence_byte_unbounded = 0;
                PlainCollectionHeader header_anonymous_sequence_byte_unbounded = TypeObjectUtils::build_plain_collection_header(equiv_kind_anonymous_sequence_byte_unbounded, element_flags_anonymous_sequence_byte_unbounded);
                {
                    SBound bound = 0;
                    PlainSequenceSElemDefn seq_sdefn = TypeObjectUtils::build_plain_sequence_s_elem_defn(header_anonymous_sequence_byte_unbounded, bound,
                                eprosima::fastcdr::external<TypeIdentifier>(element_identifier_anonymous_sequence_byte_unbounded));
                    if (eprosima::fastdds::dds::RETCODE_BAD_PARAMETER ==
                            TypeObjectUtils::build_and_register_s_sequence_type_identifier(seq_sdefn, "anonymous_sequence_byte_unbounded", type_ids_data))
                    {
                        EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                            "anonymous_sequence_byte_unbounded already registered in TypeObjectRegistry for a different type.");
                    }
                }
            }
            StructMemberFlag member_flags_data = TypeObjectUtils::build_struct_member_flag(eprosima::fastdds::dds::xtypes::TryConstructFailAction::DISCARD,
                    false, false, false, false);
            MemberId member_id_data = 0x00000007;
            bool common_data_ec {false};
            CommonStructMember common_data {TypeObjectUtils::build_common_struct_member(member_id_data, member_flags_data, TypeObjectUtils::retrieve_complete_type_identifier(type_ids_data, common_data_ec))};
            if (!common_data_ec)
            {
                EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION, "Structure data member TypeIdentifier inconsistent.");
                return;
            }
            MemberName name_data = "data";
            eprosima::fastcdr::optional<AppliedBuiltinMemberAnnotations> member_ann_builtin_data;
            ann_custom_DDSVideoFrame.reset();
            CompleteMemberDetail detail_data = TypeObjectUtils::build_complete_member_detail(name_data, member_ann_builtin_data, ann_custom_DDSVideoFrame);
            CompleteStructMember member_data = TypeObjectUtils::build_complete_struct_member(common_data, detail_data);
            TypeObjectUtils::add_complete_struct_member(member_seq_DDSVideoFrame, member_data);
        }
        CompleteStructType struct_type_DDSVideoFrame = TypeObjectUtils::build_complete_struct_type(struct_flags_DDSVideoFrame, header_DDSVideoFrame, member_seq_DDSVideoFrame);
        if (eprosima::fastdds::dds::RETCODE_BAD_PARAMETER ==
                TypeObjectUtils::build_and_register_struct_type_object(struct_type_DDSVideoFrame, type_name_DDSVideoFrame.to_string(), type_ids_DDSVideoFrame))
        {
            EPROSIMA_LOG_ERROR(XTYPES_TYPE_REPRESENTATION,
                    "DDSVideoFrame already registered in TypeObjectRegistry for a different type.");
        }
    }
}

