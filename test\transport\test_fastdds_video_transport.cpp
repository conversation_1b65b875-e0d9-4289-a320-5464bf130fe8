#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "transport/fastdds_video_transport.h"
#include "transport/video_transport_interface.h"

using namespace video_transport;

class FastDDSVideoTransportTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试环境
    }

    void TearDown() override {
        // 清理测试环境
    }
};

// 测试FastDDSVideoPublisher的基本功能
TEST_F(FastDDSVideoTransportTest, PublisherBasicFunctionality) {
    FastDDSVideoPublisher publisher;
    
    // 测试初始状态
    EXPECT_FALSE(publisher.has_subscribers());
    EXPECT_FALSE(publisher.supports_v4l2_zero_copy());
    
    // 测试配置
    TransportConfig config(TransportType::FASTDDS, "test_topic", 0, 10, 1000);
    
    // 注意：这个测试需要实际的FastDDS环境才能成功初始化
    // 在单元测试中，我们主要测试接口的正确性
    EXPECT_NO_THROW({
        auto stats = publisher.get_stats();
        auto status = publisher.get_status();
        publisher.reset_stats();
    });
}

// 测试缓冲区管理接口
TEST_F(FastDDSVideoTransportTest, BufferManagement) {
    FastDDSVideoPublisher publisher;
    
    BufferHandle handle;
    
    // 在未初始化状态下应该返回错误
    EXPECT_EQ(publisher.acquire_buffer(handle), BufferResult::TRANSPORT_ERROR);
    EXPECT_EQ(publisher.publish_buffer(handle), BufferResult::TRANSPORT_ERROR);
    
    // 测试DMA文件描述符（FastDDS不支持）
    EXPECT_EQ(publisher.get_dma_fd(handle), -1);
}

// 测试FastDDSVideoSubscriber的基本功能
TEST_F(FastDDSVideoTransportTest, SubscriberBasicFunctionality) {
    FastDDSVideoSubscriber subscriber;
    
    // 测试初始状态
    EXPECT_FALSE(subscriber.is_connected());
    
    // 测试配置
    TransportConfig config(TransportType::FASTDDS, "test_topic", 0, 10, 1000);
    
    // 测试接口方法
    EXPECT_NO_THROW({
        auto stats = subscriber.get_stats();
        auto status = subscriber.get_status();
        subscriber.reset_stats();
    });
}

// 测试订阅者缓冲区管理
TEST_F(FastDDSVideoTransportTest, SubscriberBufferManagement) {
    FastDDSVideoSubscriber subscriber;
    
    BufferHandle handle;
    
    // 在未初始化状态下应该返回超时
    EXPECT_EQ(subscriber.receive_frame_buffer(handle, 100), BufferResult::TIMEOUT);
    
    // 测试缓冲区返回
    handle.is_valid = true;
    handle.transport_data.fastdds.frame_data = new std::vector<uint8_t>(1024);
    EXPECT_EQ(subscriber.return_frame_buffer(handle), BufferResult::SUCCESS);
    EXPECT_FALSE(handle.is_valid);
}

// 测试回调设置
TEST_F(FastDDSVideoTransportTest, CallbackSetting) {
    FastDDSVideoSubscriber subscriber;
    
    bool callback_called = false;
    
    // 设置缓冲区回调
    subscriber.set_buffer_callback([&](BufferHandle& handle) {
        callback_called = true;
        // 清理缓冲区
        if (handle.transport_data.fastdds.frame_data) {
            delete handle.transport_data.fastdds.frame_data;
            handle.is_valid = false;
        }
    });
    
    // 回调设置不应该抛出异常
    EXPECT_NO_THROW({
        subscriber.set_buffer_callback(nullptr);
    });
}

// 测试统计信息
TEST_F(FastDDSVideoTransportTest, Statistics) {
    FastDDSVideoPublisher publisher;
    FastDDSVideoSubscriber subscriber;
    
    // 测试初始统计信息
    auto pub_stats = publisher.get_stats();
    auto sub_stats = subscriber.get_stats();
    
    EXPECT_EQ(pub_stats.frames_sent.load(), 0);
    EXPECT_EQ(pub_stats.bytes_sent.load(), 0);
    EXPECT_EQ(sub_stats.frames_received.load(), 0);
    EXPECT_EQ(sub_stats.bytes_received.load(), 0);
    
    // 测试统计重置
    publisher.reset_stats();
    subscriber.reset_stats();
    
    pub_stats = publisher.get_stats();
    sub_stats = subscriber.get_stats();
    
    EXPECT_EQ(pub_stats.frames_sent.load(), 0);
    EXPECT_EQ(sub_stats.frames_received.load(), 0);
}

// 测试状态字符串
TEST_F(FastDDSVideoTransportTest, StatusString) {
    FastDDSVideoPublisher publisher;
    FastDDSVideoSubscriber subscriber;
    
    auto pub_status = publisher.get_status();
    auto sub_status = subscriber.get_status();
    
    // 状态字符串应该包含基本信息
    EXPECT_FALSE(pub_status.empty());
    EXPECT_FALSE(sub_status.empty());
    
    // 应该包含初始化状态信息
    EXPECT_NE(pub_status.find("Initialized"), std::string::npos);
    EXPECT_NE(sub_status.find("Initialized"), std::string::npos);
}

// 测试配置验证
TEST_F(FastDDSVideoTransportTest, ConfigValidation) {
    FastDDSVideoPublisher publisher;
    FastDDSVideoSubscriber subscriber;
    
    // 测试错误的传输类型
    TransportConfig wrong_config(TransportType::DMA, "test_topic");
    
    EXPECT_FALSE(publisher.initialize(wrong_config));
    EXPECT_FALSE(subscriber.initialize(wrong_config));
    
    // 测试正确的传输类型
    TransportConfig correct_config(TransportType::FASTDDS, "test_topic");
    
    // 注意：实际初始化可能失败，因为需要FastDDS环境
    // 但至少不应该因为配置类型而立即失败
    EXPECT_NO_THROW({
        publisher.initialize(correct_config);
        subscriber.initialize(correct_config);
    });
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
