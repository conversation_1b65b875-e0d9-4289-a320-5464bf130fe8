{"_description": "Test Configuration for Dual Video Control Service", "_version": "1.0", "_test_topics": {"visible": "main_video_frames", "infrared": "thermal_video_frames"}, "video_control": {"_comment": "Dual Video Control Service Test Configuration", "sdcard": {"_comment": "SD card configuration and monitoring for testing", "mount_path": "/mnt/sdcard", "photo_save_path": "/mnt/sdcard/photos", "video_save_path": "/mnt/sdcard/videos", "status_report_interval_sec": 1, "min_free_space_mb": 100, "auto_cleanup_enabled": false, "max_storage_usage_percent": 90}, "photo_capture": {"_comment": "Dual photo capture configuration", "format": "JPEG", "enable_timestamp_overlay": true, "enable_gps_metadata": false, "max_photo_size_mb": 10}, "dual_video_streams": {"_comment": "Dual video stream configuration for 30-minute recording test", "segment_duration_min": 5, "visible_stream": {"_comment": "Visible light camera - main_video_frames topic", "name": "visible", "dds_topic": "main_video_frames", "width": 1920, "height": 1080, "fps": 25, "bitrate": 4000000, "codec": "H265", "jpeg_quality": 95, "gop_size": 25, "profile": "main", "preset": "medium"}, "infrared_stream": {"_comment": "Infrared camera - thermal_video_frames topic", "name": "infrared", "dds_topic": "thermal_video_frames", "width": 640, "height": 512, "fps": 30, "bitrate": 1000000, "codec": "H265", "jpeg_quality": 90, "gop_size": 30, "profile": "main", "preset": "medium"}}, "dds": {"_comment": "DDS configuration for dual video frame input", "domain_id": 0, "max_samples": 5, "timeout_ms": 100}, "performance": {"_comment": "Performance configuration for 30-minute test", "thread_priority": 80, "cpu_affinity": [2, 3], "stats_interval_sec": 1, "enable_performance_monitoring": true, "max_frame_queue_size": 10}, "logging": {"_comment": "Logging configuration for testing", "level": "INFO", "enable_debug": true, "enable_frame_stats": true, "log_file_path": "/mnt/sdcard/test_video_control.log", "max_log_file_size_mb": 100, "log_rotation_count": 3}, "safety": {"_comment": "Safety configuration for 30-minute test", "max_recording_duration_hours": 1, "emergency_stop_on_low_space": true, "auto_restart_on_error": true, "max_restart_attempts": 3, "watchdog_timeout_sec": 30}, "test_configuration": {"_comment": "Special configuration for testing", "enable_mock_dds": false, "mock_frame_rate": 30, "mock_frame_size": 1024, "enable_file_validation": true, "enable_performance_profiling": true, "test_duration_minutes": 30, "expected_segments_per_stream": 6, "expected_total_segments": 12}}, "test_scenarios": {"_description": "Test scenarios for dual video control service", "30_minute_recording": {"description": "30-minute dual video recording test", "duration_minutes": 30, "segment_duration_minutes": 5, "expected_visible_segments": 6, "expected_infrared_segments": 6, "expected_total_data_mb": 900, "min_sd_space_mb": 2000, "test_commands": [{"time_sec": 0, "command": "start_recording"}, {"time_sec": 300, "command": "take_photo"}, {"time_sec": 600, "command": "get_status"}, {"time_sec": 900, "command": "take_photo"}, {"time_sec": 1200, "command": "get_status"}, {"time_sec": 1500, "command": "take_photo"}, {"time_sec": 1800, "command": "stop_recording"}]}, "stress_test": {"description": "High-frequency command stress test", "duration_minutes": 5, "photo_interval_sec": 10, "status_interval_sec": 30, "expected_photos": 60, "max_response_time_ms": 1000}, "error_recovery": {"description": "Error recovery and resilience test", "test_cases": ["sd_card_full", "network_interruption", "encoder_failure", "power_interruption", "invalid_commands"]}}, "expected_results": {"_description": "Expected results for validation", "30_minute_test": {"visible_stream": {"segments": 6, "total_frames": 45000, "estimated_size_mb": 600, "file_pattern": "video_visible_*_seg*.mp4"}, "infrared_stream": {"segments": 6, "total_frames": 54000, "estimated_size_mb": 300, "file_pattern": "video_infrared_*_seg*.mp4"}, "photos": {"visible_photos": 3, "infrared_photos": 3, "file_pattern": "photo_*_*.jpg"}, "performance": {"max_cpu_usage": 80, "max_memory_mb": 500, "max_write_rate_mbps": 2}}}}