#include "../../include/transport/video_transport_simple.h"
#include "../../include/transport/video_transport_interface.h"
#include "../../include/transport/dma_video_transport.h"
#include "../../include/transport/fastdds_video_transport.h"
#include <iostream>
#include <thread>
#include <queue>
#include <mutex>

namespace video_transport {

// ========================================
// VideoPublisher 实现
// ========================================

struct VideoPublisher::Impl {
    TransportConfig config;
    std::unique_ptr<IVideoPublisher> transport_publisher;
    std::atomic<bool> initialized{false};
    
    // DMA特定 - 直接使用transport_publisher管理缓冲区
    BufferManager* buffer_manager = nullptr;
    
    // FastDDS特定  
    std::unique_ptr<FastDDSBufferProvider> fastdds_provider;
    
    bool initialize_dma(const TransportConfig& cfg) {
        try {
            auto dma_pub = std::make_unique<DMAVideoPublisher>();
            
            // 转换配置
            TransportConfig config(TransportConfig::Type::DMA_SHARED, cfg.topic_name,
                                     cfg.buffer_size, cfg.buffer_count, cfg.timeout_ms);
            
            if (!dma_pub->initialize(config)) {
                return false;
            }
            
            transport_publisher = std::move(dma_pub);
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "DMA publisher initialization failed: " << e.what() << std::endl;
            return false;
        }
    }
    
    bool initialize_fastdds(const TransportConfig& cfg) {
        try {
            auto fastdds_pub = std::make_unique<FastDDSVideoPublisher>();
            
            // 转换配置
            TransportConfig config(TransportConfig::Type::FASTDDS, cfg.topic_name,
                                     cfg.domain_id, cfg.max_samples, cfg.timeout_ms);
            
            if (!fastdds_pub->initialize(config)) {
                return false;
            }
            
            transport_publisher = std::move(fastdds_pub);
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "FastDDS publisher initialization failed: " << e.what() << std::endl;
            return false;
        }
    }
};

VideoPublisher::VideoPublisher() : impl_(std::make_unique<Impl>()) {}

VideoPublisher::~VideoPublisher() {
    cleanup();
}

bool VideoPublisher::initialize(const TransportConfig& config) {
    if (impl_->initialized.load()) {
        return true;
    }
    
    impl_->config = config;
    
    bool success = false;
    if (config.type == TransportType::DMA_ZERO_COPY) {
        success = impl_->initialize_dma(config);
    } else if (config.type == TransportType::FASTDDS) {
        success = impl_->initialize_fastdds(config);
    }
    
    impl_->initialized.store(success);
    return success;
}

void VideoPublisher::cleanup() {
    if (impl_->initialized.load()) {
        if (impl_->transport_publisher) {
            impl_->transport_publisher->cleanup();
            impl_->transport_publisher.reset();
        }
        impl_->fastdds_provider.reset();
        impl_->initialized.store(false);
    }
}

bool VideoPublisher::get_frame(VideoFrame& frame) {
    if (!impl_->initialized.load() || !impl_->transport_publisher) {
        return false;
    }
    
    BufferHandle handle;
    if (impl_->transport_publisher->get_buffer_for_frame(handle) != BufferResult::SUCCESS) {
        return false;
    }
    
    // 填充VideoFrame
    frame.data = handle.data;
    frame.data_size = handle.size;  // 可用大小
    frame._internal.transport_type = impl_->config.type;
    frame._internal.borrowed = true;
    
    if (impl_->config.type == TransportType::DMA_ZERO_COPY) {
        frame._internal.dma_slot = handle.transport_data.dma.slot;
    } else {
        frame._internal.fastdds_data = handle.transport_data.fastdds.frame_data;
    }
    
    return true;
}

bool VideoPublisher::publish(VideoFrame& frame) {
    if (!impl_->initialized.load() || !impl_->transport_publisher || !frame._internal.borrowed) {
        return false;
    }
    
    // 重建BufferHandle
    BufferHandle handle;
    handle.data = frame.data;
    handle.size = frame.data_size;
    handle.used_size = frame.data_size;  // 假设全部使用
    handle.buffer_id = frame.frame_id;
    handle.transport_type = (frame._internal.transport_type == TransportType::DMA_ZERO_COPY) ? 
                           TransportType::DMA_SHARED : TransportType::FASTDDS;
    handle.is_valid = true;
    
    // 填充元数据
    handle.metadata.buffer_id = frame.frame_id;
    handle.metadata.timestamp = frame.timestamp_us;
    handle.metadata.width = frame.width;
    handle.metadata.height = frame.height;
    handle.metadata.format = frame.format;
    handle.metadata.data_size = frame.data_size;
    
    if (frame._internal.transport_type == TransportType::DMA_ZERO_COPY) {
        handle.transport_data.dma.slot = frame._internal.dma_slot;
        handle.transport_data.dma.borrowed_from_v4l2 = false;
    } else {
        handle.transport_data.fastdds.frame_data = frame._internal.fastdds_data;
    }
    
    bool success = (impl_->transport_publisher->publish_frame(handle) == BufferResult::SUCCESS);
    
    // 清理frame状态
    frame._internal.borrowed = false;
    frame._internal.dma_slot = nullptr;
    frame._internal.fastdds_data = nullptr;
    
    return success;
}

bool VideoPublisher::publish_v4l2_frame(const V4L2Frame& v4l2_frame) {
    if (!impl_->initialized.load() || !impl_->transport_publisher) {
        return false;
    }
    
    if (impl_->config.type == TransportType::DMA_ZERO_COPY && 
        !v4l2_frame.buffer.planes.empty() && 
        v4l2_frame.buffer.planes[0].dma_fd >= 0) {
        
        // 零拷贝路径：直接使用V4L2的DMA缓冲区
        BufferHandle v4l2_handle;
        if (V4L2BufferAdapter::wrap_v4l2_buffer(v4l2_frame, v4l2_handle) == BufferResult::SUCCESS) {
            return (impl_->transport_publisher->publish_frame(v4l2_handle) == BufferResult::SUCCESS);
        }
    }
    
    // 复制路径：获取缓冲区并复制数据
    VideoFrame frame;
    if (!get_frame(frame)) {
        return false;
    }
    
    // 复制V4L2数据
    if (!v4l2_frame.buffer.planes.empty()) {
        const auto& plane = v4l2_frame.buffer.planes[0];
        size_t copy_size = std::min(plane.bytes_used, frame.data_size);
        memcpy(frame.data, plane.addr, copy_size);
        frame.data_size = copy_size;
    }
    
    // 设置帧信息
    frame.frame_id = v4l2_frame.buffer.index;
    frame.timestamp_us = v4l2_frame.capture_time_us;
    frame.width = v4l2_frame.width;
    frame.height = v4l2_frame.height;
    frame.format = v4l2_frame.format;
    
    return publish(frame);
}

bool VideoPublisher::publish_raw_data(const void* data, size_t size, 
                                     uint32_t width, uint32_t height, uint32_t format) {
    VideoFrame frame;
    if (!get_frame(frame)) {
        return false;
    }
    
    if (size > frame.data_size) {
        return false;  // 数据太大
    }
    
    // 复制数据
    memcpy(frame.data, data, size);
    frame.data_size = size;
    
    // 设置帧信息
    static uint64_t frame_counter = 0;
    frame.frame_id = ++frame_counter;
    frame.timestamp_us = get_current_us();
    frame.width = width;
    frame.height = height;
    frame.format = format;
    
    return publish(frame);
}

bool VideoPublisher::has_subscribers() const {
    return impl_->initialized.load() && impl_->transport_publisher && 
           impl_->transport_publisher->has_subscribers();
}

std::string VideoPublisher::get_status() const {
    if (!impl_->initialized.load() || !impl_->transport_publisher) {
        return "Not initialized";
    }
    return impl_->transport_publisher->get_status();
}

// ========================================
// VideoSubscriber 实现
// ========================================

struct VideoSubscriber::Impl {
    TransportConfig config;
    std::unique_ptr<IVideoSubscriber> transport_subscriber;
    std::atomic<bool> initialized{false};
    
    // 回调模式
    std::function<void(const VideoFrame&)> user_callback;
    std::atomic<bool> callback_mode{false};
    
    // 同步接收队列
    ThreadSafeQueue<VideoFrame> frame_queue{10};
    
    bool initialize_dma(const TransportConfig& cfg) {
        try {
            auto dma_sub = std::make_unique<DMAVideoSubscriber>();
            
            // 转换配置
            TransportConfig config(TransportConfig::Type::DMA_SHARED, cfg.topic_name,
                                     cfg.buffer_size, cfg.buffer_count, cfg.timeout_ms);
            
            if (!dma_sub->initialize(config)) {
                return false;
            }
            
            // 设置内部回调
            dma_sub->set_buffer_callback([this](BufferHandle& handle) {
                this->on_buffer_received(handle);
            });
            
            transport_subscriber = std::move(dma_sub);
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "DMA subscriber initialization failed: " << e.what() << std::endl;
            return false;
        }
    }
    
    bool initialize_fastdds(const TransportConfig& cfg) {
        try {
            auto fastdds_sub = std::make_unique<FastDDSVideoSubscriber>();
            
            // 转换配置
            TransportConfig config(TransportConfig::Type::FASTDDS, cfg.topic_name,
                                     cfg.domain_id, cfg.max_samples, cfg.timeout_ms);
            
            if (!fastdds_sub->initialize(config)) {
                return false;
            }
            
            // 设置内部回调
            fastdds_sub->set_frame_callback([this](const fastdds::video::Frame& dds_frame) {
                this->on_fastdds_frame_received(dds_frame);
            });
            
            transport_subscriber = std::move(fastdds_sub);
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "FastDDS subscriber initialization failed: " << e.what() << std::endl;
            return false;
        }
    }
    
    void on_buffer_received(BufferHandle& handle) {
        VideoFrame frame;
        convert_handle_to_frame(handle, frame);
        
        if (callback_mode.load() && user_callback) {
            user_callback(frame);
            // 自动归还缓冲区
            return_frame_internal(frame);
        } else {
            frame_queue.push(frame);
        }
    }
    
    void on_fastdds_frame_received(const fastdds::video::Frame& dds_frame) {
        VideoFrame frame;
        convert_fastdds_to_frame(dds_frame, frame);
        
        if (callback_mode.load() && user_callback) {
            user_callback(frame);
            // FastDDS模式不需要手动归还
        } else {
            frame_queue.push(frame);
        }
    }
    
    void convert_handle_to_frame(const BufferHandle& handle, VideoFrame& frame) {
        frame.frame_id = handle.metadata.buffer_id;
        frame.timestamp_us = handle.metadata.timestamp;
        frame.width = handle.metadata.width;
        frame.height = handle.metadata.height;
        frame.format = handle.metadata.format;
        frame.data = handle.data;
        frame.data_size = handle.used_size;
        
        frame._internal.transport_type = TransportType::DMA_ZERO_COPY;
        frame._internal.dma_slot = handle.transport_data.dma.slot;
        frame._internal.borrowed = true;
    }
    
    void convert_fastdds_to_frame(const fastdds::video::Frame& dds_frame, VideoFrame& frame) {
        frame.frame_id = dds_frame.frame_id;
        frame.timestamp_us = dds_frame.timestamp;
        frame.width = dds_frame.width;
        frame.height = dds_frame.height;
        frame.format = dds_frame.format;
        frame.data = const_cast<void*>(static_cast<const void*>(dds_frame.data.data()));
        frame.data_size = dds_frame.data.size();
        
        frame._internal.transport_type = TransportType::FASTDDS;
        frame._internal.borrowed = false;  // FastDDS数据已复制
    }
    
    void return_frame_internal(VideoFrame& frame) {
        if (frame._internal.transport_type == TransportType::DMA_ZERO_COPY && 
            frame._internal.borrowed && frame._internal.dma_slot) {
            
            // 重建BufferHandle并归还
            BufferHandle handle;
            handle.transport_data.dma.slot = frame._internal.dma_slot;
            handle.transport_type = TransportType::DMA_SHARED;
            handle.is_valid = true;
            
            if (auto* dma_sub = dynamic_cast<DMAVideoSubscriber*>(transport_subscriber.get())) {
                dma_sub->return_frame_buffer(handle);
            }
        }
        
        frame._internal.borrowed = false;
        frame._internal.dma_slot = nullptr;
    }
};

VideoSubscriber::VideoSubscriber() : impl_(std::make_unique<Impl>()) {}

VideoSubscriber::~VideoSubscriber() {
    cleanup();
}

bool VideoSubscriber::initialize(const TransportConfig& config) {
    if (impl_->initialized.load()) {
        return true;
    }
    
    impl_->config = config;
    
    bool success = false;
    if (config.type == TransportType::DMA_ZERO_COPY) {
        success = impl_->initialize_dma(config);
    } else if (config.type == TransportType::FASTDDS) {
        success = impl_->initialize_fastdds(config);
    }
    
    impl_->initialized.store(success);
    return success;
}

void VideoSubscriber::cleanup() {
    if (impl_->initialized.load()) {
        impl_->callback_mode.store(false);
        impl_->user_callback = nullptr;
        
        if (impl_->transport_subscriber) {
            impl_->transport_subscriber->cleanup();
            impl_->transport_subscriber.reset();
        }
        
        impl_->initialized.store(false);
    }
}

bool VideoSubscriber::receive(VideoFrame& frame, int timeout_ms) {
    if (!impl_->initialized.load() || impl_->callback_mode.load()) {
        return false;
    }
    
    return impl_->frame_queue.wait_and_pop(frame, timeout_ms);
}

void VideoSubscriber::return_frame(VideoFrame& frame) {
    impl_->return_frame_internal(frame);
}

void VideoSubscriber::set_callback(std::function<void(const VideoFrame&)> callback) {
    impl_->user_callback = callback;
    impl_->callback_mode.store(callback != nullptr);
}

bool VideoSubscriber::is_connected() const {
    return impl_->initialized.load() && impl_->transport_subscriber && 
           impl_->transport_subscriber->is_connected();
}

std::string VideoSubscriber::get_status() const {
    if (!impl_->initialized.load() || !impl_->transport_subscriber) {
        return "Not initialized";
    }
    return impl_->transport_subscriber->get_status();
}

} // namespace video_transport