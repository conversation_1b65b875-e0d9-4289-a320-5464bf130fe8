    // DMA缓冲区分配相关函数
    bool init_dma_heap() {
        // 尝试打开DMA heap设备
        const char* dma_heap_paths[] = {
            "/dev/dma_heap/system-uncached",
            "/dev/dma_heap/system",
            "/dev/dma_heap/reserved",
            nullptr
        };

        for (int i = 0; dma_heap_paths[i] != nullptr; i++) {
            dma_heap_fd_ = open(dma_heap_paths[i], O_RDWR);
            if (dma_heap_fd_ >= 0) {
                LOG_I("Opened DMA heap: %s", dma_heap_paths[i]);
                return true;
            } else {
                LOG_D("Failed to open %s: %s", dma_heap_paths[i], strerror(errno));
            }
        }

        LOG_W("No DMA heap device found, will try memfd fallback");
        return false;
    }

    int allocate_dma_buffer(const size_t size) {
        LOG_D("Attempting to allocate DMA buffer of size %zu", size);

        // 首先尝试DMA heap分配
        if (dma_heap_fd_ >= 0) {
            struct dma_heap_allocation_data alloc_data = {
                .len       = size,
                .fd_flags  = O_RDWR | O_CLOEXEC,
                .heap_flags = 0,  // 保留字段，目前未使用
            };

            if (ioctl(dma_heap_fd_, DMA_HEAP_IOCTL_ALLOC, &alloc_data) == 0 && alloc_data.fd > 0) {
                LOG_D("Allocated real DMA buffer fd=%d, alloc_fd=%d, size=%zu", dma_heap_fd_, alloc_data.fd, size);
                return alloc_data.fd;
            } else {
                LOG_W("DMA heap allocation failed: %s", strerror(errno));
                return -1;
            }
        }
        LOG_W("Using memfd as DMA buffer (may not work with all drivers): fd=%d, size=%zu", dma_heap_fd_, size);
        return -1;
    }

    // 简单的DMA缓冲区有效性检查
    bool validate_dma_buffer(int dma_fd, size_t size) {
        if (dma_fd < 0) {
            return false;
        }

        // 检查文件描述符是否有效
        struct stat st;
        if (fstat(dma_fd, &st) < 0) {
            LOG_E("DMA buffer fd is invalid: %s", strerror(errno));
            return false;
        }

        // 检查文件大小是否匹配
        if ((size_t)st.st_size < size) {
            LOG_E("DMA buffer size mismatch: expected %zu, got %ld", size, st.st_size);
            return false;
        }

        // 尝试mmap来验证缓冲区是否可访问
        void* ptr = mmap(nullptr, size, PROT_READ | PROT_WRITE, MAP_SHARED, dma_fd, 0);
        if (ptr == MAP_FAILED) {
            LOG_E("DMA buffer mmap failed: %s", strerror(errno));
            return false;
        }

        munmap(ptr, size);
        LOG_D("DMA buffer validation: PASSED (fd=%d, size=%zu)", dma_fd, size);
        return true;
    }

    std::string mmap_dump(int dma_fd, size_t size) {
        void* ptr = mmap(nullptr, size, PROT_READ | PROT_WRITE, MAP_SHARED, dma_fd, 0);
        if (ptr == MAP_FAILED) {
            LOG_E("DMA buffer mmap failed: %s", strerror(errno));
            return "";
        }

        std::string filename = "/tmp/dma_buffer_dump_" + std::to_string(dma_fd) + ".bin";
        std::ofstream file(filename, std::ios::binary);
        if (file.is_open()) {
            file.write(static_cast<char*>(ptr), size);
            file.close();
            LOG_I("DMA buffer dumped to %s", filename.c_str());
        } else {
            LOG_E("Failed to open file for DMA buffer dump");
        }

        munmap(ptr, size);
        return filename;
    }

    void cleanup_dma_buffers() {
        if (dma_heap_fd_ >= 0) {
            close(dma_heap_fd_);
            dma_heap_fd_ = -1;
        }

        // 清理所有DMA文件描述符
        for (auto& buffer : v4l2_buffers_) {
            for (int plane = 0; plane < buffer.num_planes; plane++) {
                if (buffer.dma_fd[plane] >= 0) {
                    close(buffer.dma_fd[plane]);
                    buffer.dma_fd[plane] = -1;
                }
            }
        }
    }