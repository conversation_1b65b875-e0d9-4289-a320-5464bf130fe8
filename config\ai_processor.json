{"_description": "AI Processor Service Default Configuration", "_version": "1.0", "ai_processor": {"engine_type": "onnx", "model_path": "model.onnx", "confidence_threshold": 0.5, "batch_size": 1, "use_gpu": true, "max_detections": 100, "dds": {"input_topic": "AI_Frames", "output_topic": "AI_Results", "domain_id": 0, "input_max_samples": 3, "output_max_samples": 3}, "performance": {"thread_priority": 70, "cpu_affinity": [4, 5], "process_interval_ms": 0, "_process_interval_note": "0=process every frame, >0=skip frames for performance", "stats_interval_sec": 10}, "preprocessing": {"input_width": 640, "input_height": 640, "normalize": true, "mean": [0.485, 0.456, 0.406], "std": [0.229, 0.224, 0.225], "color_format": "RGB"}, "postprocessing": {"nms_threshold": 0.4, "score_threshold": 0.25, "max_output_boxes": 100, "class_agnostic_nms": false}, "logging": {"level": "INFO", "enable_inference_stats": true, "enable_performance_stats": true, "log_detections": false}}, "engine_configs": {"tensorrt": {"precision": "FP16", "max_batch_size": 1, "max_workspace_size": **********, "_workspace_note": "1GB workspace size", "enable_fp16": true, "enable_int8": false, "calibration_cache": "calibration.cache"}, "onnx": {"execution_provider": "CUDAExecutionProvider", "device_id": 0, "enable_cpu_mem_arena": true, "enable_mem_pattern": true, "execution_mode": "ORT_SEQUENTIAL", "inter_op_num_threads": 1, "intra_op_num_threads": 4}, "openvino": {"device": "GPU", "precision": "FP16", "num_streams": 1, "num_threads": 4, "enable_dynamic_shapes": false}}, "model_profiles": {"_description": "Predefined model configurations", "yolov5s": {"model_path": "models/yolov5s.onnx", "input_width": 640, "input_height": 640, "confidence_threshold": 0.25, "nms_threshold": 0.45}, "yolov8n": {"model_path": "models/yolov8n.onnx", "input_width": 640, "input_height": 640, "confidence_threshold": 0.25, "nms_threshold": 0.7}, "efficientdet": {"model_path": "models/efficientdet.onnx", "input_width": 512, "input_height": 512, "confidence_threshold": 0.5, "nms_threshold": 0.5}}}