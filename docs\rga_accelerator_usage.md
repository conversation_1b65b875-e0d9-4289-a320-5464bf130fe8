# RGA Accelerator 使用指南

## 概述

RGA (Raster Graphic Acceleration) 是Rockchip平台的2D图形硬件加速器，支持高效的图像处理操作。本实现基于官方librga库，提供了简化的C++接口。

## 参考资料

- **官方仓库**: https://github.com/airockchip/librga
- **官方示例**: https://github.com/airockchip/librga/tree/main/samples
- **API文档**: librga提供的im2d.hpp头文件

## 支持的功能

### 1. 图像缩放 (Resize)
```cpp
RGAAccelerator rga;
rga.init();

Frame src, dst;
// 设置源帧数据...
rga.resize(src, dst, 640, 480);  // 缩放到640x480
```

### 2. 格式转换 (Format Convert)
```cpp
Frame src, dst;
// 源帧为YUYV格式
rga.format_convert(src, dst, V4L2_PIX_FMT_RGB24);  // 转换为RGB24
```

### 3. 裁剪和缩放 (Crop & Resize)
```cpp
Frame src, dst;
// 从(100,100)位置裁剪320x240区域，然后缩放到640x480
rga.crop_and_resize(src, dst, 100, 100, 320, 240, 640, 480);
```

### 4. 组合操作 (Convert & Scale)
```cpp
Frame src, dst;
// 同时进行格式转换和缩放
rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24);
```

## 支持的像素格式

| V4L2格式 | RGA格式 | 描述 |
|----------|---------|------|
| V4L2_PIX_FMT_YUYV | RK_FORMAT_YUYV_422 | YUYV 4:2:2 |
| V4L2_PIX_FMT_UYVY | RK_FORMAT_UYVY_422 | UYVY 4:2:2 |
| V4L2_PIX_FMT_RGB24 | RK_FORMAT_RGB_888 | RGB 24位 |
| V4L2_PIX_FMT_BGR24 | RK_FORMAT_BGR_888 | BGR 24位 |
| V4L2_PIX_FMT_NV12 | RK_FORMAT_YCbCr_420_SP | NV12 4:2:0 |
| V4L2_PIX_FMT_NV21 | RK_FORMAT_YCrCb_420_SP | NV21 4:2:0 |
| V4L2_PIX_FMT_RGBA32 | RK_FORMAT_RGBA_8888 | RGBA 32位 |
| V4L2_PIX_FMT_BGRA32 | RK_FORMAT_BGRA_8888 | BGRA 32位 |

## 实现细节

### 核心API使用

基于librga的im2d API:
```cpp
// 缩放操作
int ret = imresize(src_buf, dst_buf);

// 复杂操作（裁剪+缩放）
int ret = improcess(src_buf, dst_buf, {}, src_rect, dst_rect, {}, 0);
```

### 缓冲区管理

```cpp
// RGA缓冲区结构
rga_buffer_t buf;
buf.width = frame.width;
buf.height = frame.height;
buf.format = rga_format;
buf.size = frame.data.size();
buf.vir_addr = frame.data.data();
```

### 内存分配

根据像素格式自动计算缓冲区大小:
```cpp
// YUYV: 16 bits per pixel
buffer_size = width * height * 2;

// RGB24: 24 bits per pixel  
buffer_size = width * height * 3;

// NV12: 12 bits per pixel
buffer_size = width * height * 3 / 2;
```

## 性能优化

### 1. 硬件加速优势
- **高效处理**: 硬件加速比CPU处理快10-50倍
- **低功耗**: 专用硬件功耗更低
- **并行处理**: 可与CPU并行工作

### 2. 最佳实践
```cpp
// 1. 复用RGAAccelerator实例
RGAAccelerator rga;
rga.init();
// 多次使用同一个实例

// 2. 批量处理
for (auto& frame : frames) {
    rga.convert_and_scale(frame, dst, 640, 640, V4L2_PIX_FMT_RGB24);
}

// 3. 避免频繁的格式转换
// 尽量保持统一的像素格式
```

### 3. 内存优化
```cpp
// 预分配目标缓冲区
Frame dst;
dst.data.reserve(640 * 640 * 3);  // 预分配RGB888缓冲区

// 复用缓冲区
for (auto& src : frames) {
    rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24);
    // 处理dst...
    // dst缓冲区会被复用
}
```

## 错误处理

### 常见错误码
```cpp
// RGA初始化失败
if (!rga.init()) {
    LOG_E("RGA initialization failed");
    // 检查RGA驱动是否正确安装
}

// 操作失败
if (!rga.resize(src, dst, 640, 480)) {
    LOG_E("RGA resize failed");
    // 检查输入参数和缓冲区
}
```

### 调试信息
```cpp
// 启用RGA调试日志
export RGA_DEBUG=1

// 检查RGA设备
ls -la /dev/rga

// 检查RGA驱动
lsmod | grep rga
```

## 集成示例

### 在VideoConverter中的使用
```cpp
class VideoConverter {
private:
    std::unique_ptr<RGAAccelerator> rga_accel_;
    
public:
    bool init() {
        rga_accel_ = std::make_unique<RGAAccelerator>();
        return rga_accel_->init();
    }
    
    bool convert_for_ai(const Frame& src, Frame& dst) {
        // 转换为640x640 RGB888用于AI处理
        return rga_accel_->convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24);
    }
};
```

### 独立使用示例
```cpp
#include "rga_accelerator.h"

int main() {
    RGAAccelerator rga;
    if (!rga.init()) {
        return -1;
    }
    
    Frame src, dst;
    
    // 设置源帧数据
    src.width = 1920;
    src.height = 1080;
    src.format = V4L2_PIX_FMT_YUYV;
    src.data.resize(1920 * 1080 * 2);
    // 填充数据...
    
    // 转换为640x640 RGB24
    if (rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24)) {
        printf("Conversion successful: %dx%d -> %dx%d\n", 
               src.width, src.height, dst.width, dst.height);
    }
    
    rga.cleanup();
    return 0;
}
```

## 编译配置

### CMakeLists.txt
```cmake
# 启用RGA支持
option(ENABLE_RGA "Enable RGA hardware acceleration" ON)

if(ENABLE_RGA)
    add_definitions(-DHAVE_RGA)
    
    # 查找RGA库
    find_path(RGA_INCLUDE_DIR im2d.hpp
        PATHS /usr/include/rga
              /usr/local/include/rga
    )
    
    find_library(RGA_LIBRARY rga
        PATHS /usr/lib
              /usr/local/lib
    )
    
    if(RGA_INCLUDE_DIR AND RGA_LIBRARY)
        target_include_directories(${TARGET} PRIVATE ${RGA_INCLUDE_DIR})
        target_link_libraries(${TARGET} ${RGA_LIBRARY})
    else()
        message(WARNING "RGA library not found")
    endif()
endif()
```

### 依赖安装
```bash
# 在Rockchip平台上安装RGA库
sudo apt-get install librga-dev

# 或者从源码编译
git clone https://github.com/airockchip/librga.git
cd librga
mkdir build && cd build
cmake ..
make -j4
sudo make install
```

## 故障排除

### 1. RGA设备不可用
```bash
# 检查RGA设备节点
ls -la /dev/rga

# 检查权限
sudo chmod 666 /dev/rga
```

### 2. 库链接错误
```bash
# 检查库文件
ldconfig -p | grep rga

# 设置库路径
export LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH
```

### 3. 格式不支持
```cpp
// 检查输入格式是否支持
int rga_format = v4l2_to_rga_format(frame.format);
if (rga_format == 0) {
    LOG_E("Unsupported format: 0x%08x", frame.format);
}
```

## 总结

RGA Accelerator提供了高效的硬件加速图像处理能力，特别适合：
- 实时视频处理
- 格式转换
- 图像缩放
- AI预处理

通过合理使用RGA硬件加速，可以显著提升视频处理性能并降低CPU负载。
