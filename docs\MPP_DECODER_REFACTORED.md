# MPP Decoder 重构总结

## 📋 概述

根据 `mpi_dec_nt_test.c` 的实现，对 `mpp_decoder.h` 进行了全面重构，实现了以下要求：

1. ✅ **支持 MJPEG, H264, H265** - 三种主流编码格式
2. ✅ **每个 package 都是完整的 frame** - 禁用 split_parse
3. ✅ **禁用多线程** - 使用 MPP_SET_DISABLE_THREAD
4. ✅ **优化内存管理** - 基于 mpi_dec_nt_test.c 的内存管理策略
5. ✅ **每个实例只支持一种编码格式** - 构造时确定，中途不可修改

## 🔄 主要变更

### 1. 架构变更

#### 旧设计
```cpp
MPPDecoder decoder;
decoder.init(width, height, format);  // 格式在初始化时指定
decoder.decode_mjpeg(src, dst);       // 格式特定的解码方法
```

#### 新设计
```cpp
MPPDecoder decoder(MPP_DECODER_TYPE_MJPEG);  // 格式在构造时确定
decoder.init(width, height);                 // 只需要指定分辨率
decoder.decode_frame(src, dst);               // 统一的解码接口
```

### 2. 类设计变更

#### 构造函数
- **新增**: `MPPDecoder(MPPDecoderType decoder_type)` - 主构造函数
- **新增**: `MPPDecoder(int32_t v4l2_format)` - V4L2兼容构造函数
- **移除**: 默认构造函数

#### 成员变量
- **新增**: `const MPPDecoderType decoder_type_` - 编码格式（不可修改）
- **新增**: `const MppCodingType coding_type_` - MPP编码类型（不可修改）
- **移除**: 运行时格式切换相关变量

#### 方法变更
- **简化**: `init(width, height)` - 只需要分辨率参数
- **统一**: `decode_frame(src, dst)` - 统一的解码接口
- **新增**: `get_decoder_type()`, `get_coding_type()` - 获取解码器信息
- **移除**: `decode_mjpeg()`, `decode_h264()`, `decode_h265()` - 格式特定方法

### 3. 内部实现优化

#### 基于 mpi_dec_nt_test.c 的改进
```cpp
// 使用简化的 decode 接口，不使用任务队列
ret = loop_data_.mpi->decode(loop_data_.ctx, loop_data_.packet, &frame);

// 处理信息变化
if (mpp_frame_get_info_change(frame)) {
    handle_info_change(frame);
}

// 检查错误信息
RK_U32 err_info = mpp_frame_get_errinfo(frame);
RK_U32 discard = mpp_frame_get_discard(frame);
```

#### 内存管理优化
- 动态缓冲区组管理
- 内存使用统计
- 及时资源释放

#### 多线程禁用
```cpp
ret = loop_data_.mpi->control(loop_data_.ctx, MPP_SET_DISABLE_THREAD, NULL);
```

#### 完整帧处理
```cpp
ret = mpp_dec_cfg_set_u32(loop_data_.cfg, "base:split_parse", 0);
```

## 🧪 测试更新

### 1. 独立测试 (`test_mpp_decoder.cpp`)

#### 新增测试用例
- **多格式初始化测试** - 分别测试 MJPEG/H264/H265
- **格式不匹配测试** - 验证格式检查逻辑
- **重复初始化测试** - 验证错误处理
- **无效分辨率测试** - 边界条件测试

#### 测试数据生成
- `generate_test_mjpeg_frame()` - MJPEG 测试数据
- `generate_test_h264_frame()` - H264 测试数据  
- `generate_test_h265_frame()` - H265 测试数据

### 2. Google Test (`test_mpp_decoder_gtest.cpp`)

#### 更新的测试用例
- **InitializationMJPEGFormat** - MJPEG 初始化测试
- **InitializationH264Format** - H264 初始化测试
- **InitializationH265Format** - H265 初始化测试
- **InitializationUnsupportedFormat** - 不支持格式测试
- **DuplicateInitialization** - 重复初始化测试

## 📁 文件结构

### 核心文件
```
include/mpp_decoder.h           # 重构后的解码器头文件
```

### 测试文件
```
test/test_mpp_decoder.cpp       # 独立测试程序
test/test_mpp_decoder_gtest.cpp # Google Test 测试套件
```

### 示例文件
```
examples/mpp_decoder_example.cpp # API 使用示例
```

### 文档文件
```
docs/MPP_DECODER_REFACTORED.md  # 本文档
```

## 🚀 使用示例

### 基本使用
```cpp
// 1. 创建特定格式的解码器
MPPDecoder mjpeg_decoder(MPP_DECODER_TYPE_MJPEG);

// 2. 初始化（只需要分辨率）
if (!mjpeg_decoder.init(1280, 720)) {
    // 处理初始化失败
    return;
}

// 3. 解码
Frame input_frame, output_frame;
// ... 准备输入数据 ...
if (mjpeg_decoder.decode_frame(input_frame, output_frame)) {
    // 处理解码结果
}

// 4. 清理（析构函数自动调用）
mjpeg_decoder.cleanup();
```

### V4L2 兼容性
```cpp
// 使用 V4L2 格式创建解码器
MPPDecoder decoder(V4L2_PIX_FMT_H264);

// 检查创建是否成功
if (decoder.get_state() == MPP_DECODER_STATE_ERROR) {
    // 处理不支持的格式
    return;
}

// 正常初始化和使用
decoder.init(1920, 1080);
```

### 错误处理
```cpp
// 构造时检查
MPPDecoder decoder(format);
if (decoder.get_state() == MPP_DECODER_STATE_ERROR) {
    LOG_E("Unsupported format: 0x%08x", format);
    return;
}

// 初始化检查
if (!decoder.init(width, height)) {
    LOG_E("Failed to initialize decoder");
    return;
}

// 解码检查
if (!decoder.decode_frame(src, dst)) {
    LOG_E("Decode failed");
    return;
}
```

## 🔧 编译和运行

### 编译测试
```bash
# 在项目根目录
mkdir build && cd build
cmake -DBUILD_TESTS=ON ..
make test_mpp_decoder test_mpp_decoder_gtest

# 编译示例
make  # 如果示例包含在 CMakeLists.txt 中
```

### 运行测试
```bash
# 独立测试
./test_mpp_decoder

# Google Test
./test_mpp_decoder_gtest

# CTest 集成
ctest -R MPPDecoder
```

## 📊 性能特性

### 内存优化
- 动态缓冲区管理
- 内存使用统计：`get_max_memory_usage()`
- 及时资源释放

### 性能监控
- 帧计数：`get_frame_count()`
- 解码时间统计
- 错误信息跟踪

### 线程安全
- 禁用 MPP 内部多线程
- 单线程解码模式
- 减少线程切换开销

## ⚠️ 注意事项

### 1. 平台兼容性
- **Rockchip 平台**: 完整功能支持
- **其他平台**: 编译通过，运行时优雅失败

### 2. 格式限制
- 每个解码器实例只支持一种格式
- 格式在构造时确定，不可修改
- 需要多种格式时创建多个实例

### 3. 内存管理
- 解码器会自动管理内部缓冲区
- 建议及时调用 `cleanup()` 释放资源
- 析构函数会自动清理资源

### 4. 错误处理
- 构造失败会设置错误状态
- 初始化失败会清理已分配资源
- 解码失败不会影响解码器状态

## 🎯 未来改进

### 可能的优化方向
1. **异步解码支持** - 可选的异步解码模式
2. **缓冲池优化** - 预分配缓冲区池
3. **硬件加速检测** - 自动检测硬件支持
4. **性能分析工具** - 详细的性能分析接口

### 兼容性考虑
1. **向后兼容** - 保留部分旧接口作为兼容层
2. **配置文件支持** - 支持配置文件初始化
3. **插件架构** - 支持第三方解码器插件

## ✅ 总结

重构后的 MPP 解码器具有以下优势：

1. **更清晰的架构** - 每个实例专用于一种格式
2. **更好的性能** - 基于 mpi_dec_nt_test.c 的优化实现
3. **更强的类型安全** - 编译时确定格式类型
4. **更简单的使用** - 统一的解码接口
5. **更完善的测试** - 全面的测试覆盖

这个重构为视频处理系统提供了更稳定、更高效的解码能力。
