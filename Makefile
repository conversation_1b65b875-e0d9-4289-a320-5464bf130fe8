# Video Service Makefile
# Alternative build system for environments without CMake

# Project directories
PROJECT_ROOT = .
SRC_DIR = $(PROJECT_ROOT)/src
INCLUDE_DIR = $(PROJECT_ROOT)/include
CONFIG_DIR = $(PROJECT_ROOT)/config
SCRIPTS_DIR = $(PROJECT_ROOT)/scripts
DOCS_DIR = $(PROJECT_ROOT)/docs
TEST_DIR = $(PROJECT_ROOT)/test
INSTALL_DIR = $(PROJECT_ROOT)/install

# Compiler and flags
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -Wpedantic -I$(INCLUDE_DIR)
LDFLAGS = -pthread -ldl

# Build type
BUILD_TYPE ?= Release

ifeq ($(BUILD_TYPE), Debug)
    CXXFLAGS += -g -O0 -DDEBUG
else
    CXXFLAGS += -O3 -DNDEBUG
endif

# Package config
PKG_CONFIG = pkg-config

# FFmpeg (for RTSP packet capture)
FFMPEG_CFLAGS = $(shell $(PKG_CONFIG) --cflags libavformat libavcodec libavutil)
FFMPEG_LIBS = $(shell $(PKG_CONFIG) --libs libavformat libavcodec libavutil)

# GStreamer (for video converter, cloud streaming, and RTSP server)
GSTREAMER_CFLAGS = $(shell $(PKG_CONFIG) --cflags gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-webrtc-1.0 gstreamer-sdp-1.0 gstreamer-rtsp-server-1.0)
GSTREAMER_LIBS = $(shell $(PKG_CONFIG) --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-webrtc-1.0 gstreamer-sdp-1.0 gstreamer-rtsp-server-1.0)

# JsonCpp (for RTSP server configuration)
JSONCPP_AVAILABLE = $(shell $(PKG_CONFIG) --exists jsoncpp && echo "yes" || echo "no")
ifeq ($(JSONCPP_AVAILABLE), yes)
    JSONCPP_CFLAGS = $(shell $(PKG_CONFIG) --cflags jsoncpp) -DHAVE_JSONCPP
    JSONCPP_LIBS = $(shell $(PKG_CONFIG) --libs jsoncpp)
else
    JSONCPP_CFLAGS =
    JSONCPP_LIBS =
endif

# Fast DDS
FASTDDS_CFLAGS = -I/usr/include/fastdds -I/usr/include/fastcdr
FASTDDS_LIBS = -lfastdds -lfastcdr

# Optional: OpenCV
OPENCV_AVAILABLE = $(shell $(PKG_CONFIG) --exists opencv4 && echo "yes" || echo "no")
ifeq ($(OPENCV_AVAILABLE), yes)
    OPENCV_CFLAGS = $(shell $(PKG_CONFIG) --cflags opencv4) -DHAVE_OPENCV
    OPENCV_LIBS = $(shell $(PKG_CONFIG) --libs opencv4)
else
    OPENCV_CFLAGS =
    OPENCV_LIBS =
endif

# Optional: TensorRT
TENSORRT_ROOT ?= /usr/local/tensorrt
TENSORRT_AVAILABLE = $(shell test -f $(TENSORRT_ROOT)/include/NvInfer.h && echo "yes" || echo "no")
ifeq ($(TENSORRT_AVAILABLE), yes)
    TENSORRT_CFLAGS = -I$(TENSORRT_ROOT)/include -DHAVE_TENSORRT
    TENSORRT_LIBS = -L$(TENSORRT_ROOT)/lib -lnvinfer -lnvonnxparser
else
    TENSORRT_CFLAGS =
    TENSORRT_LIBS =
endif

# Optional: ONNX Runtime
ONNXRUNTIME_ROOT ?= /usr/local/onnxruntime
ONNXRUNTIME_AVAILABLE = $(shell test -f $(ONNXRUNTIME_ROOT)/include/onnxruntime_cxx_api.h && echo "yes" || echo "no")
ifeq ($(ONNXRUNTIME_AVAILABLE), yes)
    ONNXRUNTIME_CFLAGS = -I$(ONNXRUNTIME_ROOT)/include -DHAVE_ONNXRUNTIME
    ONNXRUNTIME_LIBS = -L$(ONNXRUNTIME_ROOT)/lib -lonnxruntime
else
    ONNXRUNTIME_CFLAGS =
    ONNXRUNTIME_LIBS =
endif

# Combine all flags
ALL_CFLAGS = $(CXXFLAGS) $(FFMPEG_CFLAGS) $(GSTREAMER_CFLAGS) $(FASTDDS_CFLAGS) \
             $(OPENCV_CFLAGS) $(TENSORRT_CFLAGS) $(ONNXRUNTIME_CFLAGS) $(JSONCPP_CFLAGS)

ALL_LIBS = $(LDFLAGS) $(FFMPEG_LIBS) $(GSTREAMER_LIBS) $(FASTDDS_LIBS) \
           $(OPENCV_LIBS) $(TENSORRT_LIBS) $(ONNXRUNTIME_LIBS) $(JSONCPP_LIBS)

# DDS library
DDS_DIR = dds_video_frame
DDS_LIB = $(BUILD_DIR)/libDDSVideoFrame.a
DDS_SOURCES = $(DDS_DIR)/DDSVideoFrameTypeObjectSupport.cxx $(DDS_DIR)/DDSVideoFramePubSubTypes.cxx $(DDS_DIR)/dds_video_reader.cxx $(DDS_DIR)/dds_video_writer.cxx
DDS_OBJECTS = $(DDS_SOURCES:$(DDS_DIR)/%.cxx=$(OBJ_DIR)/%.o)

# Source files
SOURCES = $(SRC_DIR)/video_capture_main.cpp $(SRC_DIR)/video_converter_main.cpp $(SRC_DIR)/ai_processor_main.cpp $(SRC_DIR)/cloud_streamer_main.cpp $(SRC_DIR)/rtsp_server_main.cpp
RTSP_SERVER_SOURCES = $(SRC_DIR)/rtsp_server_main.cpp $(SRC_DIR)/rtsp_server.cpp
RTSP_SERVER_DDS_SOURCES = $(SRC_DIR)/rtsp_server_dds.cpp

# Target executables
TARGETS = video_capture_main video_converter_main ai_processor_main cloud_streamer_main rtsp_server_main rtsp_server_dds

# Build directory
BUILD_DIR = build
OBJ_DIR = $(BUILD_DIR)/obj

# Installation directories
PREFIX ?= /usr/local
BINDIR = $(PREFIX)/bin
DATADIR = $(PREFIX)/share/video_service
SYSTEMDDIR = /lib/systemd/system

# Default target
all: $(DDS_LIB) $(TARGETS)

# Create build directory
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)
	mkdir -p $(OBJ_DIR)

# Build DDS library
$(DDS_LIB): $(DDS_OBJECTS) | $(BUILD_DIR)
	ar rcs $@ $^

$(OBJ_DIR)/%.o: $(DDS_DIR)/%.cxx | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -I$(DDS_DIR) -c -o $@ $<

# Build targets
video_capture_main: $(SRC_DIR)/video_capture_main.cpp $(DDS_LIB) | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -I$(DDS_DIR) -o $(BUILD_DIR)/$@ $< $(DDS_LIB) $(ALL_LIBS)

video_converter_main: $(SRC_DIR)/video_converter_main.cpp $(DDS_LIB) | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -I$(DDS_DIR) -o $(BUILD_DIR)/$@ $< $(DDS_LIB) $(ALL_LIBS)

ai_processor_main: $(SRC_DIR)/ai_processor_main.cpp $(DDS_LIB) | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -I$(DDS_DIR) -o $(BUILD_DIR)/$@ $< $(DDS_LIB) $(ALL_LIBS)

cloud_streamer_main: $(SRC_DIR)/cloud_streamer_main.cpp $(DDS_LIB) | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -I$(DDS_DIR) -o $(BUILD_DIR)/$@ $< $(DDS_LIB) $(ALL_LIBS)

rtsp_server_main: $(RTSP_SERVER_SOURCES) $(DDS_LIB) | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -I$(DDS_DIR) -o $(BUILD_DIR)/$@ $(RTSP_SERVER_SOURCES) $(DDS_LIB) $(ALL_LIBS)

rtsp_server_dds: $(RTSP_SERVER_DDS_SOURCES) $(DDS_LIB) | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -I$(DDS_DIR) -o $(BUILD_DIR)/$@ $(RTSP_SERVER_DDS_SOURCES) $(DDS_LIB) $(ALL_LIBS)

# Install targets
install: all
	install -d $(DESTDIR)$(BINDIR)
	install -d $(DESTDIR)$(DATADIR)
	install -d $(DESTDIR)$(PREFIX)/include/video_service
	install -d $(DESTDIR)$(PREFIX)/share/doc/video_service

	# Install binaries
	install -m 755 $(BUILD_DIR)/video_capture_main $(DESTDIR)$(BINDIR)/
	install -m 755 $(BUILD_DIR)/video_converter_main $(DESTDIR)$(BINDIR)/
	install -m 755 $(BUILD_DIR)/ai_processor_main $(DESTDIR)$(BINDIR)/
	install -m 755 $(BUILD_DIR)/cloud_streamer_main $(DESTDIR)$(BINDIR)/
	install -m 755 $(BUILD_DIR)/rtsp_server_main $(DESTDIR)$(BINDIR)/

	# Install header files
	install -m 644 $(INCLUDE_DIR)/*.h $(DESTDIR)$(PREFIX)/include/video_service/

	# Install configuration files
	install -m 644 $(CONFIG_DIR)/config.json $(DESTDIR)$(DATADIR)/

	# Install scripts
	install -m 755 $(SCRIPTS_DIR)/*.sh $(DESTDIR)$(DATADIR)/

	# Install documentation
	install -m 644 $(DOCS_DIR)/README.md $(DESTDIR)$(PREFIX)/share/doc/video_service/

	# Install systemd service file (if systemd is available)
	if [ -d "$(DESTDIR)$(SYSTEMDDIR)" ]; then \
		sed 's|@BINDIR@|$(BINDIR)|g; s|@DATADIR@|$(DATADIR)|g' $(CONFIG_DIR)/video_service.service.in > video_service.service; \
		install -m 644 video_service.service $(DESTDIR)$(SYSTEMDDIR)/; \
	fi

# Uninstall targets
uninstall:
	rm -f $(DESTDIR)$(BINDIR)/video_capture_main
	rm -f $(DESTDIR)$(BINDIR)/video_converter_main
	rm -f $(DESTDIR)$(BINDIR)/ai_processor_main
	rm -f $(DESTDIR)$(BINDIR)/cloud_streamer_main
	rm -f $(DESTDIR)$(BINDIR)/rtsp_server_main
	rm -rf $(DESTDIR)$(DATADIR)
	rm -f $(DESTDIR)$(SYSTEMDDIR)/video_service.service

# Clean build files
clean:
	rm -rf $(BUILD_DIR)
	rm -f video_service.service

# Create distribution package
dist: clean
	tar -czf video_service-1.0.0.tar.gz \
		*.h *.cpp *.json *.sh *.in Makefile CMakeLists.txt README.md

# Development targets
debug:
	$(MAKE) BUILD_TYPE=Debug

release:
	$(MAKE) BUILD_TYPE=Release

# Check dependencies
check-deps:
	@echo "Checking dependencies..."
	@echo -n "FFmpeg Core: "
	@$(PKG_CONFIG) --exists libavformat libavcodec libavutil && echo "OK" || echo "MISSING"
	@echo -n "GStreamer Core: "
	@$(PKG_CONFIG) --exists gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 && echo "OK" || echo "MISSING"
	@echo -n "Fast DDS: "
	@test -f /usr/include/fastdds/dds/DomainParticipant.hpp && echo "OK" || echo "MISSING"
	@echo -n "OpenCV: "
	@$(PKG_CONFIG) --exists opencv4 && echo "OK" || echo "OPTIONAL"
	@echo -n "TensorRT: "
	@test -f $(TENSORRT_ROOT)/include/NvInfer.h && echo "OK" || echo "OPTIONAL"
	@echo -n "ONNX Runtime: "
	@test -f $(ONNXRUNTIME_ROOT)/include/onnxruntime_cxx_api.h && echo "OK" || echo "OPTIONAL"

# Show configuration
config:
	@echo "Video Service Build Configuration:"
	@echo "  Build type: $(BUILD_TYPE)"
	@echo "  Compiler: $(CXX)"
	@echo "  C++ flags: $(CXXFLAGS)"
	@echo "  FFmpeg: $(if $(FFMPEG_LIBS),enabled,disabled)"
	@echo "  GStreamer: $(if $(GSTREAMER_LIBS),enabled,disabled)"
	@echo "  OpenCV: $(OPENCV_AVAILABLE)"
	@echo "  TensorRT: $(TENSORRT_AVAILABLE)"
	@echo "  ONNX Runtime: $(ONNXRUNTIME_AVAILABLE)"
	@echo "  Install prefix: $(PREFIX)"

# Help target
help:
	@echo "Video Service Build System"
	@echo ""
	@echo "Targets:"
	@echo "  all          - Build all executables (default)"
	@echo "  debug        - Build with debug flags"
	@echo "  release      - Build with release flags"
	@echo "  install      - Install to system"
	@echo "  uninstall    - Remove from system"
	@echo "  clean        - Remove build files"
	@echo "  dist         - Create distribution package"
	@echo "  check-deps   - Check for required dependencies"
	@echo "  config       - Show build configuration"
	@echo "  help         - Show this help"
	@echo ""
	@echo "Variables:"
	@echo "  BUILD_TYPE   - Debug or Release (default: Release)"
	@echo "  PREFIX       - Installation prefix (default: /usr/local)"
	@echo "  TENSORRT_ROOT - TensorRT installation path"
	@echo "  ONNXRUNTIME_ROOT - ONNX Runtime installation path"

.PHONY: all install uninstall clean dist debug release check-deps config help
