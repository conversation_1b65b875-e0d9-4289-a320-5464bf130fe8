# DDS链接问题修复

## 问题描述

编译时出现链接错误：
```
ai_processor_main.cpp:(.text._ZNSt10unique_ptrI11AIProcessorSt14default_deleteIS0_EED2Ev[_ZNSt10unique_ptrI11AIProcessorSt14default_deleteIS0_EED5Ev]+0x5c): undefined reference to `DDSVideoWriter::~DDSVideoWriter()'
```

这个错误表明链接器找不到`DDSVideoWriter`的析构函数定义。

## 根本原因

1. **缺少析构函数实现**：在`include/common.h`中，`DDSVideoWriter`和`DDSVideoReader`类声明了析构函数但没有提供实现：
   ```cpp
   class DDSVideoWriter {
   public:
       ~DDSVideoWriter();  // 只有声明，没有实现
   };
   ```

2. **缺少FastDDS库链接**：在构建系统中缺少FastDDS库的正确链接配置。

3. **Makefile中DDS库构建缺失**：Makefile没有构建DDS相关的库文件。

## 修复方案

### 1. 修复析构函数实现

**修改前：**
```cpp
class DDSVideoWriter {
public:
    ~DDSVideoWriter();  // 只有声明
};

class DDSVideoReader {
public:
    ~DDSVideoReader();  // 只有声明
};
```

**修改后：**
```cpp
class DDSVideoWriter {
public:
    ~DDSVideoWriter() = default;  // 使用默认实现
};

class DDSVideoReader {
public:
    ~DDSVideoReader() = default;  // 使用默认实现
};
```

### 2. 修复CMakeLists.txt中的库链接

**修改前：**
```cmake
set(COMMON_LIBS
    ${FFMPEG_LIBRARIES}
    ${GSTREAMER_LIBRARIES}
    DDSVideoFrame_lib_${PROJECT_NAME}
    pthread
    dl
)
```

**修改后：**
```cmake
set(COMMON_LIBS
    ${FFMPEG_LIBRARIES}
    ${GSTREAMER_LIBRARIES}
    DDSVideoFrame_lib_${PROJECT_NAME}
    fastdds
    fastcdr
    pthread
    dl
)
```

### 3. 修复Makefile中的库名称

**修改前：**
```makefile
FASTDDS_LIBS = -lfastrtps -lfastcdr
```

**修改后：**
```makefile
FASTDDS_LIBS = -lfastdds -lfastcdr
```

### 4. 添加DDS库构建到Makefile

**新增内容：**
```makefile
# DDS library
DDS_DIR = dds_video_frame
DDS_LIB = $(BUILD_DIR)/libDDSVideoFrame.a
DDS_SOURCES = $(DDS_DIR)/DDSVideoFrameTypeObjectSupport.cxx $(DDS_DIR)/DDSVideoFramePubSubTypes.cxx $(DDS_DIR)/dds_video_reader.cxx $(DDS_DIR)/dds_video_writer.cxx
DDS_OBJECTS = $(DDS_SOURCES:$(DDS_DIR)/%.cxx=$(OBJ_DIR)/%.o)

# Build DDS library
$(DDS_LIB): $(DDS_OBJECTS) | $(BUILD_DIR)
	ar rcs $@ $^

$(OBJ_DIR)/%.o: $(DDS_DIR)/%.cxx | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -I$(DDS_DIR) -c -o $@ $<
```

**更新目标构建规则：**
```makefile
ai_processor_main: $(SRC_DIR)/ai_processor_main.cpp $(DDS_LIB) | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -I$(DDS_DIR) -o $(BUILD_DIR)/$@ $< $(DDS_LIB) $(ALL_LIBS)
```

## 技术细节

### 析构函数问题
- C++中，如果类声明了析构函数但没有定义，链接器会报"undefined reference"错误
- 使用`= default`让编译器生成默认析构函数实现
- 由于`DDSVideoWriter`和`DDSVideoReader`使用`std::unique_ptr`管理资源，默认析构函数足够

### 库链接问题
- FastDDS 3.x版本的库名从`fastrtps`改为`fastdds`
- 需要同时链接`fastdds`和`fastcdr`两个库
- CMake和Makefile都需要正确配置

### 构建依赖
- DDS相关的源文件需要编译成库
- 主程序需要链接这个DDS库
- 需要正确的头文件包含路径

## 验证方法

创建了测试程序`test_dds_link.cpp`来验证修复：
```cpp
int main() {
    try {
        {
            DDSVideoWriter writer("test_topic", 3);
            // 测试创建和析构
        }
        {
            DDSVideoReader reader("test_topic", 3);
            // 测试创建和析构
        }
        std::cout << "All DDS tests passed!" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "DDS test failed: " << e.what() << std::endl;
        return 1;
    }
}
```

## 总结

这个链接错误是由多个因素造成的：
1. 缺少析构函数实现
2. 库链接配置错误
3. 构建系统不完整

通过系统性地修复这些问题，解决了DDS相关的链接错误，确保所有服务程序都能正确编译和链接。
