{"_description": "Cloud Streamer Service Default Configuration", "_version": "1.0", "cloud_streamer": {"stream_type": "rtmp", "stream_url": "", "dds": {"input_topic": "Cloud_Frames", "domain_id": 0, "max_samples": 3}, "rtmp": {"url": "rtmp://live.example.com/live/stream_key", "bitrate": 2000000, "gop_size": 15, "preset": "veryfast", "tune": "zerolatency", "profile": "baseline", "level": "3.1", "keyframe_interval": 60, "reconnect_attempts": 5, "reconnect_delay_ms": 1000}, "webrtc": {"signaling_server": "wss://signaling.example.com", "room_id": "room1", "ice_servers": [{"urls": ["stun:stun.l.google.com:19302"]}, {"urls": ["turn:turn.example.com:3478"], "username": "user", "credential": "pass"}], "bitrate": 1500000, "max_bitrate": 3000000, "min_bitrate": 500000, "adaptive_bitrate": true}, "encoding": {"codec": "H264", "width": 1280, "height": 720, "fps": 30, "bitrate": 2000000, "use_hardware_encoder": true, "encoder_preset": "veryfast", "rate_control": "CBR", "buffer_size": 4000000, "max_b_frames": 0}, "performance": {"thread_priority": 60, "cpu_affinity": [6, 7], "stats_interval_sec": 10, "enable_frame_dropping": true, "max_queue_size": 30, "low_latency_mode": true}, "quality_control": {"adaptive_bitrate": true, "min_bitrate": 500000, "max_bitrate": 5000000, "bitrate_step": 100000, "quality_adjustment_interval_sec": 5, "packet_loss_threshold": 0.05, "rtt_threshold_ms": 200}, "logging": {"level": "INFO", "enable_stream_stats": true, "enable_network_stats": true, "enable_quality_stats": true, "log_connection_events": true}}, "stream_profiles": {"_description": "Predefined streaming configurations", "high_quality": {"encoding": {"width": 1920, "height": 1080, "fps": 30, "bitrate": 5000000, "gop_size": 30}}, "medium_quality": {"encoding": {"width": 1280, "height": 720, "fps": 30, "bitrate": 2000000, "gop_size": 15}}, "low_latency": {"encoding": {"width": 1280, "height": 720, "fps": 30, "bitrate": 1500000, "gop_size": 1, "preset": "ultrafast"}, "performance": {"low_latency_mode": true, "max_queue_size": 5}}, "mobile_optimized": {"encoding": {"width": 854, "height": 480, "fps": 25, "bitrate": 800000, "gop_size": 25}}}, "platform_configs": {"_description": "Platform-specific configurations", "youtube": {"rtmp_url": "rtmp://a.rtmp.youtube.com/live2/", "recommended_bitrate": 2500000, "max_bitrate": 9000000, "keyframe_interval": 60}, "twitch": {"rtmp_url": "rtmp://live.twitch.tv/live/", "recommended_bitrate": 3000000, "max_bitrate": 6000000, "keyframe_interval": 60}, "facebook": {"rtmp_url": "rtmps://live-api-s.facebook.com:443/rtmp/", "recommended_bitrate": 4000000, "max_bitrate": 9000000, "keyframe_interval": 60}}}