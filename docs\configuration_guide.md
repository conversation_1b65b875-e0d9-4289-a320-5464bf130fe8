# 视频服务系统配置指南

## 📋 配置概述

本文档详细说明了视频服务系统的统一配置管理，包括DDS主题命名规范、配置文件结构和最佳实践。

## 🏷️ DDS主题命名规范

### 统一命名约定
所有DDS主题名称采用 **下划线分隔** 的命名方式，提高可读性和一致性：

| 服务组件 | 旧主题名称 | 新主题名称 | 用途 |
|----------|------------|------------|------|
| 视频捕获 | VideoFrames | **Video_Frames** | 原始视频帧数据 |
| AI处理 | AIFrames | **AI_Frames** | AI处理后的视频帧 |
| 云端流 | CloudFrames | **Cloud_Frames** | 云端推流视频帧 |
| AI结果 | AIResults | **AI_Results** | AI检测结果数据 |

### 主题用途说明

#### Video_Frames (主要视频流)
- **发布者**: video_capture_main
- **订阅者**: video_converter_main, rtsp_server_main
- **数据格式**: 原始视频帧 (YUYV, RGB, NV12等)
- **用途**: 系统主要视频数据流，所有其他处理的源头

#### AI_Frames (AI处理流)
- **发布者**: video_converter_main
- **订阅者**: ai_processor_main
- **数据格式**: RGB24 (640x640)
- **用途**: 专为AI推理优化的视频帧

#### Cloud_Frames (云端推流)
- **发布者**: video_converter_main
- **订阅者**: cloud_streamer_main
- **数据格式**: H.264编码帧
- **用途**: 云端推流和存储

#### AI_Results (AI检测结果)
- **发布者**: ai_processor_main
- **订阅者**: 监控系统, 报警系统
- **数据格式**: JSON格式检测结果
- **用途**: 目标检测、分类等AI分析结果

## 📁 配置文件结构

### 主配置文件 (config/config.json)

```json
{
  "system": {
    "log_level": "INFO",
    "log_file": "/var/log/video_service/video_service.log",
    "pid_file": "/var/run/video_service.pid",
    "working_directory": "/opt/video_service",
    "cpu_affinity": {
      "video_capture": [0, 1],
      "video_converter": [2, 3],
      "ai_processor": [4, 5],
      "cloud_streamer": [6, 7],
      "rtsp_server": [8, 9]
    },
    "priority": {
      "video_capture": 90,
      "video_converter": 80,
      "ai_processor": 70,
      "cloud_streamer": 60,
      "rtsp_server": 85
    }
  },
  "dds": {
    "domain_id": 0,
    "topics": {
      "video_frames": "Video_Frames",
      "ai_frames": "AI_Frames",
      "cloud_frames": "Cloud_Frames",
      "ai_results": "AI_Results"
    }
  },
  "video_capture": {
    "source_type": "v4l2",
    "device": "/dev/video0",
    "rtsp_url": "",
    "width": 1280,
    "height": 720,
    "fps": 30,
    "use_tcp": false,
    "use_dma": true,
    "buffer_count": 4
  },
  "video_converter": {
    "enable_hardware_acceleration": true,
    "ai_output_format": "RGB24",
    "cloud_output_format": "H264",
    "ai_resolution": {
      "width": 640,
      "height": 640
    },
    "cloud_resolution": {
      "width": 1280,
      "height": 720
    }
  },
  "ai_processor": {
    "engine": "onnx",
    "model_path": "/opt/models/yolov5s.onnx",
    "confidence_threshold": 0.5,
    "nms_threshold": 0.4,
    "max_detections": 100
  },
  "cloud_streamer": {
    "type": "rtmp",
    "rtmp_url": "rtmp://live.example.com/live/stream_key",
    "webrtc_signaling_server": "wss://signaling.example.com",
    "webrtc_room_id": "room1",
    "bitrate": 2000000,
    "keyframe_interval": 60
  },
  "rtsp_server": {
    "dds_topic": "Video_Frames",
    "server_address": "0.0.0.0",
    "server_port": 8554,
    "mount_point": "/stream",
    "output_video": {
      "width": 1280,
      "height": 720,
      "fps": 30,
      "codec": "H264",
      "bitrate": 2000000,
      "gop_size": 30
    },
    "encoder": {
      "use_hardware_encoder": true,
      "fallback_to_software": true,
      "preset": "low-latency",
      "rate_control": "CBR"
    },
    "performance": {
      "zero_copy_mode": true,
      "buffer_size": 5,
      "max_clients": 10,
      "thread_priority": 80
    }
  }
}
```

### RTSP服务器专用配置 (config/rtsp_server.json)

专门的RTSP服务器配置文件，支持多流配置和高级功能：

```json
{
  "rtsp_server": {
    "dds_topic": "Video_Frames",
    "server_address": "0.0.0.0",
    "server_port": 8554,
    "mount_point": "/stream"
  },
  "multiple_streams": {
    "enabled": false,
    "streams": [
      {
        "name": "main_stream",
        "dds_topic": "Video_Frames",
        "mount_point": "/main",
        "width": 1280,
        "height": 720,
        "bitrate": 2000000
      },
      {
        "name": "ai_stream",
        "dds_topic": "AI_Frames",
        "mount_point": "/ai",
        "width": 640,
        "height": 640,
        "bitrate": 1000000
      }
    ]
  }
}
```

## 🔧 配置管理最佳实践

### 1. 环境特定配置
```bash
# 开发环境
config/config.dev.json

# 测试环境  
config/config.test.json

# 生产环境
config/config.prod.json
```

### 2. 配置验证
```bash
# 验证配置文件语法
./scripts/validate_config.sh config/config.json

# 测试DDS连接
./scripts/test_dds_topics.sh
```

### 3. 配置热重载
```bash
# 发送SIGHUP信号重新加载配置
kill -HUP $(cat /var/run/video_service.pid)
```

## 🚀 快速配置示例

### 基本视频捕获和RTSP服务
```json
{
  "video_capture": {
    "source_type": "v4l2",
    "device": "/dev/video0",
    "width": 1280,
    "height": 720,
    "fps": 30
  },
  "rtsp_server": {
    "dds_topic": "Video_Frames",
    "server_port": 8554,
    "mount_point": "/stream"
  }
}
```

### 高性能AI处理配置
```json
{
  "video_capture": {
    "use_dma": true,
    "buffer_count": 8
  },
  "ai_processor": {
    "engine": "tensorrt",
    "model_path": "/opt/models/yolov5s.trt",
    "batch_size": 4
  },
  "system": {
    "cpu_affinity": {
      "ai_processor": [4, 5, 6, 7]
    },
    "priority": {
      "ai_processor": 95
    }
  }
}
```

### 多路RTSP流配置
```json
{
  "rtsp_server": {
    "multiple_streams": {
      "enabled": true,
      "streams": [
        {
          "name": "main_stream",
          "dds_topic": "Video_Frames",
          "mount_point": "/main",
          "bitrate": 5000000
        },
        {
          "name": "ai_stream",
          "dds_topic": "AI_Frames", 
          "mount_point": "/ai",
          "bitrate": 1000000
        }
      ]
    }
  }
}
```

## 📊 配置监控和调试

### 配置状态检查
```bash
# 检查当前配置
./scripts/show_config.sh

# 验证DDS主题
./scripts/list_dds_topics.sh

# 监控配置变更
./scripts/monitor_config_changes.sh
```

### 常见配置问题

1. **DDS主题不匹配**
   - 检查发布者和订阅者使用相同的主题名称
   - 确认主题名称拼写正确 (Video_Frames vs VideoFrames)

2. **端口冲突**
   - 确保RTSP服务器端口未被占用
   - 检查防火墙设置

3. **权限问题**
   - 确保视频设备访问权限
   - 检查日志文件写入权限

## 🔄 配置迁移指南

### 从旧版本迁移
```bash
# 自动迁移脚本
./scripts/migrate_config.sh config/old_config.json config/config.json

# 手动检查需要更新的主题名称
grep -r "VideoFrames" config/ src/ include/
```

所有配置已统一为下划线命名规范，确保系统各组件间的一致性和可维护性！
