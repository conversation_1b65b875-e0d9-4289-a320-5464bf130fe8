# MPP Decoder 与 mpi_dec_test.c 对齐修改

## 📋 概述

根据 `src/mpi_dec_test.c` 的参考实现，对 `mpp_decoder.h` 进行了调整，以确保与官方示例代码的一致性，特别是在 JPEG 和非 JPEG 解码的处理方式上。

## 🔄 主要修改

### 1. 保持对外接口不变 ✅

- **构造函数**: `MPPDecoder(MPPDecoderType)` 和 `MPPDecoder(int32_t v4l2_format)` 保持不变
- **初始化方法**: `init(width, height)` 保持不变
- **解码方法**: `decode_frame(src, dst)` 保持不变
- **状态管理**: `cleanup()`, `is_initialized()` 等保持不变

### 2. 去掉"禁用多线程" ✅

#### 修改前
```cpp
// 禁用多线程 (根据要求3)
ret = loop_data_.mpi->control(loop_data_.ctx, MPP_SET_DISABLE_THREAD, NULL);
if (ret != MPP_OK) {
    LOG_W("Failed to disable thread: %d", ret);
}
```

#### 修改后
```cpp
// 去掉了禁用多线程的代码，使用 MPP 默认的多线程模式
```

### 3. 修改 packet 和 frame 的初始化 ✅

#### 修改前
```cpp
// 在 init_mpp_context 中预先初始化
ret = mpp_packet_init(&loop_data_.packet, NULL, 0);
ret = mpp_frame_init(&loop_data_.frame);
```

#### 修改后
```cpp
// 在每次解码时动态创建和销毁
ret = mpp_packet_init(&packet, (void*)data, size);
ret = mpp_frame_init(&frame);
// ... 解码完成后
mpp_frame_deinit(&frame);
mpp_packet_deinit(&packet);
```

### 4. 重写 decode_packet_internal ✅

#### 关键改进
- **动态创建**: packet 和 frame 在每次解码时创建
- **模式区分**: 根据 `simple_mode` 区分 JPEG 和非 JPEG 处理
- **任务队列**: JPEG 使用任务队列，非 JPEG 使用简单接口

#### JPEG 模式 (simple_mode = 0)
```cpp
// 使用任务队列方式
MppTask task = NULL;
ret = loop_data_.mpi->poll(loop_data_.ctx, MPP_PORT_INPUT, MPP_POLL_BLOCK);
ret = loop_data_.mpi->dequeue(loop_data_.ctx, MPP_PORT_INPUT, &task);
mpp_task_meta_set_packet(task, KEY_INPUT_PACKET, packet);
mpp_task_meta_set_frame(task, KEY_OUTPUT_FRAME, frame);
ret = loop_data_.mpi->enqueue(loop_data_.ctx, MPP_PORT_INPUT, task);
// ... 输出处理
```

#### 非 JPEG 模式 (simple_mode = 1)
```cpp
// 使用简单的 decode 接口
ret = loop_data_.mpi->decode(loop_data_.ctx, packet, &frame);
```

### 5. 内存管理对齐 ✅

#### 数据结构更新
```cpp
typedef struct {
    // ... 其他字段
    RK_S64 first_pkt;     // 替代 first_decode_time
    RK_S64 first_frm;     // 替代 total_decode_time
    RK_U32 simple_mode;   // 新增：区分 JPEG/非JPEG 模式
} MpiDecLoopData;
```

#### 缓冲区管理
- **JPEG 模式**: 使用 MPP 内部缓冲区管理
- **非 JPEG 模式**: 设置外部缓冲区组

```cpp
if (loop_data_.simple_mode) {
    // 非 JPEG 模式：设置外部缓冲区组
    if (!setup_buffer_group(buf_size)) {
        return false;
    }
} else {
    // JPEG 模式：使用内部缓冲区管理
    LOG_D("JPEG mode: using internal buffer management");
}
```

#### 清理逻辑
```cpp
// 不再清理 packet 和 frame（因为它们是动态创建的）
// 只清理持久性资源
if (loop_data_.ctx) {
    mpp_destroy(loop_data_.ctx);
}
if (loop_data_.frm_grp) {
    mpp_buffer_group_put(loop_data_.frm_grp);
}
```

## 🔍 JPEG vs 非 JPEG 的关键区别

### JPEG 解码特点
1. **复杂模式**: `simple_mode = 0`
2. **任务队列**: 使用 poll/dequeue/enqueue 方式
3. **内部缓冲**: MPP 内部管理缓冲区
4. **适用格式**: MJPEG

### 非 JPEG 解码特点
1. **简单模式**: `simple_mode = 1`
2. **直接接口**: 使用 `mpi->decode()` 直接调用
3. **外部缓冲**: 需要设置外部缓冲区组
4. **适用格式**: H264, H265

## 📊 性能影响

### 内存使用
- **减少**: 不再预分配 packet 和 frame
- **动态**: 按需创建和释放
- **优化**: 根据格式选择合适的缓冲策略

### 线程性能
- **提升**: 恢复 MPP 默认多线程模式
- **并发**: 允许 MPP 内部并行处理
- **效率**: 更好的 CPU 利用率

### 解码延迟
- **JPEG**: 任务队列可能增加少量延迟，但更稳定
- **非 JPEG**: 直接接口减少延迟
- **整体**: 根据格式优化的处理方式

## 🧪 测试兼容性

### 现有测试无需修改 ✅
- 所有对外接口保持不变
- 测试用例可以直接运行
- 行为语义保持一致

### 内部行为改进
- 更符合官方示例的实现方式
- 更好的格式特定优化
- 更稳定的内存管理

## ⚠️ 注意事项

### 1. 平台兼容性
- 修改基于官方 MPP 示例，兼容性更好
- 在 Rockchip 平台上应该有更好的性能
- 非 Rockchip 平台行为保持不变

### 2. 内存管理
- packet 和 frame 现在是短生命周期对象
- 减少了内存泄漏的风险
- 更符合 MPP 的设计理念

### 3. 错误处理
- 保持了原有的错误检查逻辑
- 增加了格式特定的错误处理
- 更好的资源清理保证

## 🔧 调试建议

### 日志输出
```cpp
LOG_D("Decoder mode: %s (simple_mode=%d)", 
      get_decoder_type_name(), loop_data_.simple_mode);
```

### 性能监控
```cpp
LOG_D("First packet time: %lld, First frame time: %lld", 
      loop_data_.first_pkt, loop_data_.first_frm);
```

### 内存跟踪
```cpp
LOG_D("Frame count: %d, Max memory: %zu", 
      loop_data_.frame_count, loop_data_.max_usage);
```

## ✅ 验证清单

- [x] 对外接口保持不变
- [x] 去掉禁用多线程代码
- [x] packet/frame 动态创建和销毁
- [x] decode_packet_internal 重写完成
- [x] 内存管理对齐 mpi_dec_test.c
- [x] JPEG/非JPEG 模式区分
- [x] 缓冲区管理策略优化
- [x] 错误处理逻辑完善
- [x] 性能跟踪更新
- [x] 清理逻辑简化

## 🎯 总结

通过与 `mpi_dec_test.c` 对齐，MPP 解码器现在：

1. **更符合官方实现**: 遵循 Rockchip 官方示例的最佳实践
2. **格式特定优化**: 针对 JPEG 和非 JPEG 格式的不同特点优化
3. **更好的性能**: 恢复多线程，优化内存管理
4. **更高的稳定性**: 动态资源管理，减少内存泄漏风险
5. **保持兼容性**: 对外接口完全不变，现有代码无需修改

这些修改为视频解码提供了更可靠、更高效的基础实现。
