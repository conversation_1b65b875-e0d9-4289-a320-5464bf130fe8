#ifndef BUFFER_SHARE_VIDEO_TRANSPORT_H
#define BUFFER_SHARE_VIDEO_TRANSPORT_H

#include <liburing.h>
#include <liburing/io_uring.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <atomic>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <thread>
#include <iostream>
#include <mutex>
#include <condition_variable>
#include <cstring>
#include <chrono>
#include <queue>
#include <functional>
#include <sstream>
#include "shared_buffer_manager.h"
#include "video_transport_interface.h"

namespace video_transport {

struct ReleaseMessage {
    uint64_t buffer_id;
    uint32_t msg_type = 1; // 1 means release message
};

// 消息池管理
class MessagePool {
public:
    MessagePool(int pool_size = 16) : pool_size_(pool_size) {}

    struct Message {
        msghdr hdr;
        iovec iov[1];
        char ctrl_buf[CMSG_SPACE(sizeof(int))];
        FrameMetadata meta;
    };

    Message* acquire() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (free_list_.empty()) {
            allocate_batch(pool_size_);
        }
        
        auto* msg = free_list_.back();
        free_list_.pop_back();
        return msg;
    }
    
    void release(Message* msg) {
        std::lock_guard<std::mutex> lock(mutex_);
        free_list_.push_back(msg);
    }

private:
    void allocate_batch(size_t count) {
        for (size_t i = 0; i < count; ++i) {
            auto* msg = new Message();
            memset(&msg->hdr, 0, sizeof(msghdr));
            
            // 设置固定结构
            msg->iov[0].iov_base = &msg->meta;
            msg->iov[0].iov_len = sizeof(FrameMetadata);
            msg->hdr.msg_iov = msg->iov;
            msg->hdr.msg_iovlen = 1;
            msg->hdr.msg_control = msg->ctrl_buf;
            msg->hdr.msg_controllen = sizeof(msg->ctrl_buf);
            
            // 初始化控制消息头
            struct cmsghdr* cmsg = CMSG_FIRSTHDR(&msg->hdr);
            cmsg->cmsg_level = SOL_SOCKET;
            cmsg->cmsg_type = SCM_RIGHTS;
            cmsg->cmsg_len = CMSG_LEN(sizeof(int));
            
            free_list_.push_back(msg);
        }
    }

    std::vector<Message*> free_list_;
    std::mutex mutex_;
    int pool_size_;
};

// Forward declarations
class IoUringConsumer;

// 生产者服务器 - 实现 IVideoPublisher 接口
class IoUringProducer : public IVideoPublisher {
public:
    enum class ConnectionState { ACCEPTING, CONNECTED, CLOSING };

    struct Connection {
        int fd;
        ConnectionState state;
    };
    
    enum class OperationType { ACCEPT, RECV, SEND, CLOSE };
    
    struct OperationData {
        OperationType type;
        int fd;
        MessagePool::Message* msg;
        BufferSlot* slot;
    };

    IoUringProducer() : initialized_(false), has_active_subscribers_(false) {}
    ~IoUringProducer() override { cleanup(); }
    
    // IVideoPublisher接口实现
    bool initialize(const TransportConfig& config) override;
    void cleanup() override;
    BufferResult acquire_buffer(BufferHandle& handle) override;
    BufferResult publish_buffer(BufferHandle& handle) override;
    bool publish_frame(const fastdds::video::Frame& frame) override;
    int get_dma_fd(const BufferHandle& handle) override;
    bool has_subscribers() const override;
    TransportStats get_stats() const override;
    void reset_stats() override;
    std::string get_status() const override;
    
    // **NEW: 明确的V4L2集成支持**
    bool supports_v4l2_zero_copy() const override { return true; }

private:
    void setup_server_socket();
    void setup_io_uring();
    void submit_accept();
    void distribute_frame(BufferSlot* slot);
    void submit_send_frame(int client_fd, BufferSlot* slot);
    std::string generate_consumer_id(int client_fd);
    void process_events();
    void handle_accept(struct io_uring_cqe* cqe, OperationData* data);
    void submit_recv(int client_fd);
    void handle_recv(struct io_uring_cqe* cqe, OperationData* data);
    void handle_send(struct io_uring_cqe* cqe, OperationData* data);
    void handle_close(struct io_uring_cqe* cqe, OperationData* data);
    void close_connection(int fd);
    void update_stats_sent(size_t bytes, bool success);

    std::string generate_consumer_id(int client_fd) {
        return "client_" + std::to_string(client_fd) + "_" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count());
    }

    // 核心组件
    std::string socket_path_;
    SharedBufferManager buffer_manager_;
    MessagePool msg_pool_;
    
    // 网络和IO
    int server_fd_ = -1;
    io_uring ring_;
    std::unordered_map<int, Connection> connections_;
    std::thread event_thread_;
    std::atomic<bool> running_;
    std::atomic<bool> has_active_subscribers_;
    
    // Cleanup timing
    std::chrono::steady_clock::time_point last_cleanup_time_;
    static constexpr std::chrono::seconds CLEANUP_INTERVAL{10};  // Clean up every 10 seconds
    
    // IVideoPublisher required members
    TransportConfig config_;
    std::atomic<bool> initialized_;
    mutable std::mutex stats_mutex_;
    TransportStats stats_;
};

// ========================================
// IoUringProducer 实现
// ========================================

inline bool IoUringProducer::initialize(const TransportConfig& config) {
    if (initialized_.load()) {
        return true;
    }
    
    if (config.type != TransportType::DMA && config.type != TransportType::SHMEM) {
        return false;
    }
    
    try {
        config_ = config;
        socket_path_ = config.topic_name;
        
        // Determine buffer type based on transport type
        BufferType buffer_type = (config.type == TransportType::DMA) ? 
                               BufferType::DMA : BufferType::SHMEM;
        
        // Initialize core components with appropriate buffer type
        buffer_manager_ = SharedBufferManager(buffer_type, config.buffer_size, config.ring_buffer_size);
        msg_pool_ = MessagePool(config.ring_buffer_size);
        
        setup_server_socket();
        setup_io_uring();
        submit_accept();
        running_ = true;
        event_thread_ = std::thread(&IoUringProducer::process_events, this);
        
        initialized_.store(true);
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

inline void IoUringProducer::cleanup() {
    running_ = false;
    if (event_thread_.joinable()) {
        io_uring_submit_and_wait(&ring_, 1);
        event_thread_.join();
    }
    
    for (auto& [fd, conn] : connections_) close(fd);
    if (server_fd_ >= 0) {
        close(server_fd_);
        server_fd_ = -1;
    }
    if (!socket_path_.empty()) {
        unlink(socket_path_.c_str());
    }
    io_uring_queue_exit(&ring_);
    initialized_.store(false);
}

inline BufferResult IoUringProducer::acquire_buffer(BufferHandle& handle) {
    if (!initialized_.load()) {
        return BufferResult::TRANSPORT_ERROR;
    }
    
    try {
        auto* slot = buffer_manager_.acquire_buffer();
        if (!slot) {
            return BufferResult::BUFFER_NOT_AVAILABLE;
        }
        
        // 设置BufferHandle
        handle.data = slot->addr;
        handle.size = slot->size;
        handle.used_size = 0;
        handle.buffer_id = slot->buffer_id;
        handle.transport_data.dma.slot = slot;
        handle.transport_data.dma.borrowed_from_v4l2 = false;
        handle.transport_type = (slot->type == BufferType::DMA) ? TransportType::DMA : TransportType::SHMEM;
        handle.is_valid = true;
        
        return BufferResult::SUCCESS;
    } catch (const std::exception& e) {
        return BufferResult::TRANSPORT_ERROR;
    }
}


inline int IoUringProducer::get_dma_fd(const BufferHandle& handle) {
    if (!handle.is_valid || !handle.transport_data.dma.slot) {
        return -1;
    }
    return handle.transport_data.dma.slot->fd;
}

inline bool IoUringProducer::publish_frame(const fastdds::video::Frame& frame) {
    BufferHandle handle;
    auto result = acquire_buffer(handle);
    if (result != BufferResult::SUCCESS) {
        return false;
    }
    
    // Copy FastDDS frame data to buffer
    if (frame.data.size() > handle.size) {
        update_stats_sent(0, false);
        return false;
    }
    
    memcpy(handle.data, frame.data.data(), frame.data.size());
    handle.used_size = frame.data.size();
    handle.metadata.width = frame.width;
    handle.metadata.height = frame.height;
    handle.metadata.format = frame.format;
    handle.metadata.timestamp = frame.timestamp;
    handle.metadata.data_size = frame.data.size();
    
    return publish_buffer(handle) == BufferResult::SUCCESS;
}

inline BufferResult IoUringProducer::publish_buffer(BufferHandle& handle) {
    if (!handle.is_valid || !handle.transport_data.dma.slot) {
        return BufferResult::INVALID_DATA;
    }
    
    try {
        auto* slot = handle.transport_data.dma.slot;
        
        // 更新元数据
        slot->meta.data_size = handle.used_size;
        slot->meta.width = handle.metadata.width;
        slot->meta.height = handle.metadata.height;
        slot->meta.format = handle.metadata.format;
        slot->meta.timestamp = handle.metadata.timestamp;
        
        // 释放到buffer manager
        buffer_manager_.publish_buffer(slot);
        
        // 分发给所有连接的消费者
        distribute_frame(slot);
        
        handle.is_valid = false;
        
        update_stats_sent(handle.used_size, true);
        return BufferResult::SUCCESS;
    } catch (const std::exception& e) {
        update_stats_sent(0, false);
        return BufferResult::TRANSPORT_ERROR;
    }
}

inline void IoUringProducer::distribute_frame(BufferSlot* slot) {
    if (!slot || connections_.empty()) {
        return;
    }
    
    // Send frame notification to all connected consumers
    for (const auto& [fd, conn] : connections_) {
        if (conn.state == ConnectionState::CONNECTED) {
            submit_send_frame(fd, slot, conn.consumer_id);
        }
    }
}

inline void IoUringProducer::submit_send_frame(int client_fd, BufferSlot* slot, const std::string& consumer_id) {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;
    
    auto* msg = msg_pool_.acquire();
    auto* data = new OperationData{OperationType::SEND, client_fd, msg, slot, consumer_id};
    
    // Copy metadata to message (including buffer_id for confirmation)
    msg->meta = slot->meta;
    msg->meta.buffer_id = slot->buffer_id;  // Ensure buffer_id is set
    
    // Set DMA fd in control message
    struct cmsghdr* cmsg = CMSG_FIRSTHDR(&msg->hdr);
    *((int*)CMSG_DATA(cmsg)) = slot->fd;
    
    io_uring_prep_sendmsg(sqe, client_fd, &msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline bool IoUringProducer::has_subscribers() const {
    return has_active_subscribers_.load();
}

inline TransportStats IoUringProducer::get_stats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

inline void IoUringProducer::reset_stats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = TransportStats{};
}

inline std::string IoUringProducer::get_status() const {
    std::ostringstream oss;
    oss << "IoUringProducer: " 
        << (initialized_.load() ? "initialized" : "not initialized")
        << ", subscribers: " << (has_active_subscribers_.load() ? "yes" : "no")
        << ", socket: " << socket_path_;
    return oss.str();
}

// Private method implementations for IoUringProducer
inline void IoUringProducer::setup_server_socket() {
    server_fd_ = socket(AF_UNIX, SOCK_SEQPACKET, 0);
    if (server_fd_ < 0) {
        throw std::runtime_error("Failed to create server socket");
    }
    
    // Remove existing socket file
    unlink(socket_path_.c_str());
    
    struct sockaddr_un addr = {};
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path)-1);
    
    if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        close(server_fd_);
        throw std::runtime_error("Failed to bind server socket");
    }
    
    if (listen(server_fd_, 10) < 0) {
        close(server_fd_);
        throw std::runtime_error("Failed to listen on server socket");
    }
}

inline void IoUringProducer::setup_io_uring() {
    struct io_uring_params params = {};
    if (io_uring_queue_init_params(1024, &ring_, &params)) {
        throw std::runtime_error("Failed to initialize io_uring");
    }
}

inline void IoUringProducer::submit_accept() {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;
    
    auto* data = new OperationData{OperationType::ACCEPT, server_fd_, nullptr, nullptr};
    
    io_uring_prep_accept(sqe, server_fd_, nullptr, nullptr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void IoUringProducer::process_events() {
    last_cleanup_time_ = std::chrono::steady_clock::now();
    
    while (running_.load()) {
        struct io_uring_cqe* cqe;
        
        // Check for cleanup periodically
        auto now = std::chrono::steady_clock::now();
        if (now - last_cleanup_time_ >= CLEANUP_INTERVAL) {
            buffer_manager_.cleanup_unused_ready_buffers();
            last_cleanup_time_ = now;
        }
        
        int ret = io_uring_wait_cqe(&ring_, &cqe);
        
        if (ret < 0) {
            if (errno == EINTR) continue;
            break;
        }
        
        auto* data = static_cast<OperationData*>(io_uring_cqe_get_data(cqe));
        if (!data) {
            io_uring_cqe_seen(&ring_, cqe);
            continue;
        }
        
        switch (data->type) {
            case OperationType::ACCEPT:
                handle_accept(cqe, data);
                break;
            case OperationType::RECV:
                handle_recv(cqe, data);
                break;
            case OperationType::SEND:
                handle_send(cqe, data);
                break;
            case OperationType::CLOSE:
                handle_close(cqe, data);
                break;
        }
        
        io_uring_cqe_seen(&ring_, cqe);
    }
}

inline void IoUringProducer::handle_accept(struct io_uring_cqe* cqe, OperationData* data) {
    int client_fd = cqe->res;
    delete data;
    
    if (client_fd < 0) {
        if (running_.load()) {
            submit_accept();  // Continue accepting
        }
        return;
    }
    
    // Generate unique consumer ID
    std::string consumer_id = generate_consumer_id(client_fd);
    
    // Add new connection
    connections_[client_fd] = {client_fd, ConnectionState::CONNECTED, consumer_id};
    has_active_subscribers_.store(true);
    
    // Start receiving from this client
    submit_recv(client_fd);
    
    // Continue accepting new connections
    if (running_.load()) {
        submit_accept();
    }
}

inline void IoUringProducer::submit_recv(int client_fd) {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;
    
    auto* msg = msg_pool_.acquire();
    
    // Get consumer_id for this client
    std::string consumer_id;
    auto it = connections_.find(client_fd);
    if (it != connections_.end()) {
        consumer_id = it->second.consumer_id;
    }
    
    auto* data = new OperationData{OperationType::RECV, client_fd, msg, nullptr, consumer_id};
    
    io_uring_prep_recvmsg(sqe, client_fd, &msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void IoUringProducer::handle_recv(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_received = cqe->res;
    int client_fd = data->fd;
    std::string consumer_id = data->consumer_id;
    
    msg_pool_.release(data->msg);
    delete data;
    
    if (bytes_received <= 0) {
        // Client disconnected or error
        close_connection(client_fd);
        return;
    }
    
    // Handle release message
    if (bytes_received == sizeof(ReleaseMessage)) {
        ReleaseMessage release_msg;
        memcpy(&release_msg, data->msg->iov[0].iov_base, sizeof(ReleaseMessage));
        
        // Extract consumer_id from message (new unified format)
        std::string consumer_id_str(release_msg.consumer_id);
        buffer_manager_.return_buffer_from_remote(release_msg.buffer_id, consumer_id_str);
        
        // Clean up local buffer tracking
        {
            std::lock_guard<std::mutex> lock(buffer_tracking_mutex_);
            auto it = buffer_consumers_.find(release_msg.buffer_id);
            if (it != buffer_consumers_.end()) {
                it->second.erase(consumer_id_str);
                if (it->second.empty()) {
                    buffer_consumers_.erase(it);
                }
            }
        }
    }
    // Continue receiving from this client
    submit_recv(client_fd);
}

inline void IoUringProducer::handle_send(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_sent = cqe->res;
    int client_fd = data->fd;
    auto* slot = data->slot;
    std::string consumer_id = data->consumer_id;
    
    msg_pool_.release(data->msg);
    delete data;
    
    if (bytes_sent < 0) {
        // Send failed, close connection
        close_connection(client_fd);
        update_stats_sent(0, false);
    } else {
        // Send successful - NOW increase reference count since consumer actually received the buffer
        if (slot && !consumer_id.empty()) {
            bool acquired = buffer_manager_.loan_ready_buffer(slot->buffer_id, consumer_id);
            if (acquired) {
                // Track this consumer for the buffer
                std::lock_guard<std::mutex> lock(buffer_tracking_mutex_);
                buffer_consumers_[slot->buffer_id].insert(consumer_id);
            }
        }
        update_stats_sent(slot ? slot->meta.data_size : 0, true);
    }
}

inline void IoUringProducer::handle_close(struct io_uring_cqe* cqe, OperationData* data) {
    int client_fd = data->fd;
    delete data;
    close_connection(client_fd);
}

inline void IoUringProducer::close_connection(int fd) {
    auto it = connections_.find(fd);
    if (it != connections_.end()) {
        std::string consumer_id = it->second.consumer_id;
        
        // Clean up all buffer references for this consumer
        {
            std::lock_guard<std::mutex> lock(buffer_tracking_mutex_);
            auto buffer_it = buffer_consumers_.begin();
            while (buffer_it != buffer_consumers_.end()) {
                buffer_it->second.erase(consumer_id);
                if (buffer_it->second.empty()) {
                    buffer_it = buffer_consumers_.erase(buffer_it);
                } else {
                    ++buffer_it;
                }
            }
        }
        
        // Release any buffers this consumer might still have
        // This ensures proper cleanup if client disconnects unexpectedly
        buffer_manager_.return_all_from_remote(consumer_id);
        
        connections_.erase(it);
        close(fd);
        
        // Update subscriber status
        has_active_subscribers_.store(!connections_.empty());
    }
}

inline void IoUringProducer::update_stats_sent(size_t bytes, bool success) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (success) {
        stats_.frames_sent.fetch_add(1);
        stats_.bytes_sent.fetch_add(bytes);
    } else {
        stats_.failed_operations.fetch_add(1);
    }
    stats_.last_update_time.store(std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
}

// ========================================
// IoUringConsumer 实现
// ========================================

inline bool IoUringConsumer::initialize(const TransportConfig& config) {
    if (initialized_.load()) {
        return true;
    }
    
    if (config.type != TransportType::DMA && config.type != TransportType::SHMEM) {
        return false;
    }
    
    try {
        config_ = config;
        socket_path_ = config.topic_name;
        
        msg_pool_ = MessagePool(4);
        
        connect_to_server();
        setup_io_uring();
        submit_recv();
        running_ = true;
        event_thread_ = std::thread(&IoUringConsumer::process_events, this);
        
        initialized_.store(true);
        connected_.store(true);
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

inline void IoUringConsumer::cleanup() {
    running_ = false;
    if (event_thread_.joinable()) {
        io_uring_submit_and_wait(&ring_, 1);
        event_thread_.join();
    }
    
    if (server_fd_ >= 0) {
        close(server_fd_);
        server_fd_ = -1;
    }
    // Cleanup all persistent mapped buffers - this is where munmap happens
    // We maintain persistent mappings during operation for performance
    for (auto& [buffer_id, info] : mapped_buffers_) {
        if (info.addr && info.addr != MAP_FAILED) {
            munmap(info.addr, info.size);
        }
    }
    mapped_buffers_.clear();

    io_uring_queue_exit(&ring_);
    initialized_.store(false);
    connected_.store(false);
}

inline BufferResult IoUringConsumer::receive_frame_buffer(BufferHandle& handle, int timeout_ms) {
    if (!initialized_.load() || !connected_.load()) {
        return BufferResult::TRANSPORT_ERROR;
    }
    
    // Wait for sync buffer with timeout
    std::unique_lock<std::mutex> lock(sync_buffer_mutex_);
    bool success = sync_buffer_cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                                           [this]() { return !pending_sync_buffers_.empty(); });
    
    if (!success) {
        return BufferResult::TIMEOUT;
    }
    
    auto sync_buffer = pending_sync_buffers_.front();
    pending_sync_buffers_.pop();
    lock.unlock();
    
    // Check if we already have this buffer mapped (persistent mapping)
    void* mapped_addr = nullptr;
    {
        std::lock_guard<std::mutex> map_lock(mapped_buffers_mutex_);
        auto it = mapped_buffers_.find(sync_buffer.metadata.buffer_id);
        if (it != mapped_buffers_.end()) {
            // Buffer already mapped, reuse existing mapping
            mapped_addr = it->second.addr;
            // Update fd if different (shouldn't happen but be safe)
            if (it->second.fd != sync_buffer.fd) {
                close(it->second.fd);
                it->second.fd = sync_buffer.fd;
            }
        } else {
            // First time seeing this buffer, create persistent mapping
            mapped_addr = mmap(nullptr, sync_buffer.metadata.data_size, 
                                PROT_READ, MAP_SHARED, sync_buffer.fd, 0);
            
            if (mapped_addr == MAP_FAILED) {
                close(sync_buffer.fd);
                return BufferResult::TRANSPORT_ERROR;
            }
            
            // Store persistent mapping
            mapped_buffers_[sync_buffer.metadata.buffer_id] = {
                mapped_addr, sync_buffer.metadata.data_size, sync_buffer.fd
            };
        }
    }
    
    // Set BufferHandle fields
    handle.data = mapped_addr;
    handle.size = sync_buffer.metadata.data_size;
    handle.used_size = sync_buffer.metadata.data_size;
    handle.buffer_id = sync_buffer.metadata.buffer_id;
    handle.metadata = sync_buffer.metadata;
    handle.transport_type = (sync_buffer.metadata.type == BufferType::DMA) ? 
                           TransportType::DMA : TransportType::SHMEM;
    handle.transport_data.dma.slot = nullptr;
    handle.transport_data.dma.borrowed_from_v4l2 = false;
    handle.is_valid = true;
    
    return BufferResult::SUCCESS;
}

inline BufferResult IoUringConsumer::return_frame_buffer(BufferHandle& handle) {
    if (!handle.is_valid) {
        return BufferResult::INVALID_DATA;
    }
    // Send release message to producer
    submit_release(handle.buffer_id);
    handle.is_valid = false;
    return BufferResult::SUCCESS;
}

inline bool IoUringConsumer::receive_frame(fastdds::video::Frame& frame, int timeout_ms) {
    BufferHandle handle;
    if (receive_frame_buffer(handle, timeout_ms) != BufferResult::SUCCESS) {
        return false;
    }
    
    // 复制数据到FastDDS帧
    frame.width = handle.metadata.width;
    frame.height = handle.metadata.height;
    frame.format = handle.metadata.format;
    frame.timestamp = handle.metadata.timestamp;
    frame.data.resize(handle.used_size);
    memcpy(frame.data.data(), handle.data, handle.used_size);
    
    // 释放缓冲区
    return_frame_buffer(handle);
    
    update_stats_received(handle.used_size, true);
    return true;
}

inline void IoUringConsumer::set_frame_callback(std::function<void(const fastdds::video::Frame&)> callback) {
    fastdds_frame_callback_ = callback;
}

inline void IoUringConsumer::set_buffer_callback(std::function<void(BufferHandle&)> callback) {
    buffer_callback_ = callback;
}

inline bool IoUringConsumer::is_connected() const {
    return connected_.load();
}

inline TransportStats IoUringConsumer::get_stats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

inline void IoUringConsumer::reset_stats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = TransportStats{};
}

inline std::string IoUringConsumer::get_status() const {
    std::ostringstream oss;
    oss << "IoUringConsumer: " 
        << (initialized_.load() ? "initialized" : "not initialized")
        << ", connected: " << (connected_.load() ? "yes" : "no")
        << ", socket: " << socket_path_;
    return oss.str();
}

// Private method implementations
inline void IoUringConsumer::setup_io_uring() {
    struct io_uring_params params = {};
    if (io_uring_queue_init_params(1024, &ring_, &params)) {
        throw std::runtime_error("Failed to initialize io_uring");
    }
}

inline void IoUringConsumer::connect_to_server() {
    server_fd_ = socket(AF_UNIX, SOCK_SEQPACKET, 0);
    if (server_fd_ < 0) {
        throw std::runtime_error("Failed to create socket");
    }

    struct sockaddr_un addr = {};
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path)-1);

    if (connect(server_fd_, (struct sockaddr*)&addr, sizeof(addr))) {
        close(server_fd_);
        throw std::runtime_error("Failed to connect to server");
    }
}

inline void IoUringConsumer::submit_recv() {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;

    auto* msg = msg_pool_.acquire();
    auto* data = new OperationData{OperationData::RECV, msg};

    io_uring_prep_recvmsg(sqe, server_fd_, &msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void IoUringConsumer::submit_release(uint64_t buffer_id) {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;

    // Create release message with consumer info on stack
    static thread_local ReleaseMessage release_msg;
    release_msg.buffer_id = buffer_id;
    release_msg.msg_type = 1;
    
    auto* data = new OperationData{OperationData::SEND, nullptr};

    io_uring_prep_send(sqe, server_fd_, &release_msg, sizeof(ReleaseMessage), 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void IoUringConsumer::update_stats_received(size_t bytes, bool success) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (success) {
        stats_.frames_received.fetch_add(1);
        stats_.bytes_received.fetch_add(bytes);
    } else {
        stats_.failed_operations.fetch_add(1);
    }
    stats_.last_update_time.store(std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
}

inline void IoUringConsumer::process_events() {
    while (running_.load()) {
        struct io_uring_cqe* cqe;
        int ret = io_uring_wait_cqe(&ring_, &cqe);
        
        if (ret < 0) {
            if (errno == EINTR) continue;
            break;
        }
        
        auto* data = static_cast<OperationData*>(io_uring_cqe_get_data(cqe));
        if (!data) {
            io_uring_cqe_seen(&ring_, cqe);
            continue;
        }
        
        switch (data->type) {
            case OperationData::RECV:
                handle_recv(cqe, data);
                break;
            case OperationData::SEND:
                handle_send(cqe, data);
                break;
        }
        
        io_uring_cqe_seen(&ring_, cqe);
    }
}

inline void IoUringConsumer::handle_recv(struct io_uring_cqe* cqe, struct OperationData* data) {
    int bytes_received = cqe->res;
    auto* msg = data->msg;
    
    if (bytes_received <= 0) {
        // Connection lost or error
        connected_.store(false);
        msg_pool_.release(msg);
        delete data;
        return;
    }
    
    // Extract DMA fd from control message
    struct cmsghdr* cmsg = CMSG_FIRSTHDR(&msg->hdr);
    if (cmsg && cmsg->cmsg_type == SCM_RIGHTS) {
        int dma_fd = *((int*)CMSG_DATA(cmsg));
        
        // Create sync buffer and add to queue
        {
            std::lock_guard<std::mutex> lock(sync_buffer_mutex_);
            pending_sync_buffers_.emplace(msg->meta, dma_fd);
        }
        sync_buffer_cv_.notify_one();
        
        // Trigger callbacks if set
        if (buffer_callback_) {
            BufferHandle handle;
            if (receive_frame_buffer(handle, 0) == BufferResult::SUCCESS) {
                buffer_callback_(handle);
            }
        }
        
        if (fastdds_frame_callback_) {
            fastdds::video::Frame frame;
            if (receive_frame(frame, 0)) {
                fastdds_frame_callback_(frame);
            }
        }        
        update_stats_received(msg->meta.data_size, true);
    } else {
        update_stats_received(0, false);
    }
    
    msg_pool_.release(msg);
    delete data;
    
    // Continue receiving if still connected
    if (connected_.load()) {
        return_frame_buffer(handle);
        submit_recv();
    }
}

inline void IoUringConsumer::handle_send(struct io_uring_cqe* cqe, struct OperationData* data) {
    int bytes_sent = cqe->res;
    delete data;
    
    if (bytes_sent < 0) {
        // Send failed, connection might be lost
        connected_.store(false);
    }
    // For release messages, no further action needed
}

// 消费者客户端 - 实现 IVideoSubscriber 接口
class IoUringConsumer : public IVideoSubscriber {
public:
    using FrameCallback = std::function<void(const FrameMetadata&, int)>;
    
    IoUringConsumer() : initialized_(false), connected_(false), running_(false) {}
    ~IoUringConsumer() override { cleanup(); }
    
    // IVideoSubscriber接口实现
    bool initialize(const TransportConfig& config) override;
    void cleanup() override;
    BufferResult receive_frame_buffer(BufferHandle& handle, int timeout_ms = 1000) override;
    BufferResult return_frame_buffer(BufferHandle& handle) override;
    bool receive_frame(fastdds::video::Frame& frame, int timeout_ms = 1000) override;
    void set_frame_callback(std::function<void(const fastdds::video::Frame&)> callback) override;
    void set_buffer_callback(std::function<void(BufferHandle&)> callback) override;
    bool is_connected() const override;
    TransportStats get_stats() const override;
    void reset_stats() override;
    std::string get_status() const override;
    
    // 设置帧回调（兼容旧接口）
    void set_frame_callback(FrameCallback callback);

private:
    void setup_io_uring();
    void connect_to_server();
    void submit_recv();
    void submit_release(uint64_t buffer_id);
    void process_events();
    void handle_recv(struct io_uring_cqe* cqe, struct OperationData* data);
    void handle_send(struct io_uring_cqe* cqe, struct OperationData* data);
    void update_stats_received(size_t bytes, bool success);

    // Basic operation data structure for consumer
    struct OperationData {
        enum Type { RECV, SEND } type;
        MessagePool::Message* msg;
    };

    // 同步缓冲区管理的结构
    struct SyncBuffer {
        FrameMetadata metadata;
        int fd; // 缓冲区文件描述符 DMA or SHMEM
        SyncBuffer(const FrameMetadata& meta, int fd) : metadata(meta), fd(fd) {}
    };
    
    struct MappedBufferInfo {
        void* addr;
        size_t size;
        int fd;
    };

    std::string socket_path_;
    int server_fd_ = -1;
    io_uring ring_;
    std::thread event_thread_;
    std::atomic<bool> running_;
    
    // IVideoSubscriber required members
    TransportConfig config_;
    std::atomic<bool> initialized_;
    std::atomic<bool> connected_;
    mutable std::mutex stats_mutex_;
    TransportStats stats_;
    std::function<void(const fastdds::video::Frame&)> fastdds_frame_callback_;
    std::function<void(BufferHandle&)> buffer_callback_;
    
    // 同步缓冲区队列
    std::queue<SyncBuffer> pending_sync_buffers_;
    std::mutex sync_buffer_mutex_;
    std::condition_variable sync_buffer_cv_;
    
    // 映射的缓冲区记录
    std::unordered_map<uint64_t, MappedBufferInfo> mapped_buffers_;
    std::mutex mapped_buffers_mutex_;
};

} // namespace video_transport

#endif // BUFFER_SHARE_VIDEO_TRANSPORT_H